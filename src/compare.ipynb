#%%
import re
import sys
from pathlib import Path

source_path = Path("../resources/decompiled_output")
results_path = Path("../resources/results")

side_regex = re.compile("minecraft-(.+?)-")
version_regex = re.compile("-(1\\.\\d{1,2}\\.\\d{1,2})-")


def gather_files(folder: Path):
    """
    Recursively gather all files under `root` and return a set of their
    relative paths (relative to `root`).
    """
    if not folder.is_dir():
        print(f"Error: {folder!r} is not a directory.", file=sys.stderr)
        sys.exit(1)

    return {
        p.relative_to(folder)
        for p in folder.rglob('*')
        if p.is_file()
    }


class Folder:
    def __init__(self, path):
        self.path = Path(path)
        self.platform = side_regex.match(path).group(1)
        self.version = version_regex.search(path).group(1)
        self.files = gather_files(source_path / self.path)

    def __repr__(self):
        return f"Folder({self.path}, {self.platform}, {self.version})"
    def __str__(self):
        return f"{self.path} ({self.platform}, {self.version})"
#%%
folders = [Folder(d.name) for d in Path("../resources/decompiled_output/").iterdir() if d.is_dir()]

folders.sort(key=lambda f: (f.platform, f.version))
#%%
from collections import defaultdict

groups = defaultdict(list)
for idx, val in enumerate(folders):
    groups[val.platform].append(idx)
#%%
def set_to_str(s):
    return "     \n - ".join(map(str, sorted(s)))

for platform, indices in groups.items():
    folder1, folder2 = folders[indices[0]], folders[indices[1]]
    with open(results_path / f"only in {platform} {folder1.version}.txt", "w") as f:
        f.write(set_to_str(folder1.files - folder2.files))
    with open(results_path / f"only in {platform} {folder2.version}.txt", "w") as f:
        f.write(set_to_str(folder2.files - folder1.files))
