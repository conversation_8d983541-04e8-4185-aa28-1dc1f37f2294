data/minecraft/advancement/adventure/heart_transplanter.json
data/minecraft/advancement/husbandry/place_dried_ghast_in_water.json
data/minecraft/advancement/recipes/building_blocks/dried_ghast.json
data/minecraft/advancement/recipes/combat/black_harness.json
data/minecraft/advancement/recipes/combat/blue_harness.json
data/minecraft/advancement/recipes/combat/brown_harness.json
data/minecraft/advancement/recipes/combat/cyan_harness.json
data/minecraft/advancement/recipes/combat/dye_black_harness.json
data/minecraft/advancement/recipes/combat/dye_blue_harness.json
data/minecraft/advancement/recipes/combat/dye_brown_harness.json
data/minecraft/advancement/recipes/combat/dye_cyan_harness.json
data/minecraft/advancement/recipes/combat/dye_gray_harness.json
data/minecraft/advancement/recipes/combat/dye_green_harness.json
data/minecraft/advancement/recipes/combat/dye_light_blue_harness.json
data/minecraft/advancement/recipes/combat/dye_light_gray_harness.json
data/minecraft/advancement/recipes/combat/dye_lime_harness.json
data/minecraft/advancement/recipes/combat/dye_magenta_harness.json
data/minecraft/advancement/recipes/combat/dye_orange_harness.json
data/minecraft/advancement/recipes/combat/dye_pink_harness.json
data/minecraft/advancement/recipes/combat/dye_purple_harness.json
data/minecraft/advancement/recipes/combat/dye_red_harness.json
data/minecraft/advancement/recipes/combat/dye_white_harness.json
data/minecraft/advancement/recipes/combat/dye_yellow_harness.json
data/minecraft/advancement/recipes/combat/gray_harness.json
data/minecraft/advancement/recipes/combat/green_harness.json
data/minecraft/advancement/recipes/combat/light_blue_harness.json
data/minecraft/advancement/recipes/combat/light_gray_harness.json
data/minecraft/advancement/recipes/combat/lime_harness.json
data/minecraft/advancement/recipes/combat/magenta_harness.json
data/minecraft/advancement/recipes/combat/orange_harness.json
data/minecraft/advancement/recipes/combat/pink_harness.json
data/minecraft/advancement/recipes/combat/purple_harness.json
data/minecraft/advancement/recipes/combat/red_harness.json
data/minecraft/advancement/recipes/combat/saddle.json
data/minecraft/advancement/recipes/combat/white_harness.json
data/minecraft/advancement/recipes/combat/yellow_harness.json
data/minecraft/advancement/recipes/decorations/dye_black_bed.json
data/minecraft/advancement/recipes/decorations/dye_black_carpet.json
data/minecraft/advancement/recipes/decorations/dye_blue_bed.json
data/minecraft/advancement/recipes/decorations/dye_blue_carpet.json
data/minecraft/advancement/recipes/decorations/dye_brown_bed.json
data/minecraft/advancement/recipes/decorations/dye_brown_carpet.json
data/minecraft/advancement/recipes/decorations/dye_cyan_bed.json
data/minecraft/advancement/recipes/decorations/dye_cyan_carpet.json
data/minecraft/advancement/recipes/decorations/dye_gray_bed.json
data/minecraft/advancement/recipes/decorations/dye_gray_carpet.json
data/minecraft/advancement/recipes/decorations/dye_green_bed.json
data/minecraft/advancement/recipes/decorations/dye_green_carpet.json
data/minecraft/advancement/recipes/decorations/dye_light_blue_bed.json
data/minecraft/advancement/recipes/decorations/dye_light_blue_carpet.json
data/minecraft/advancement/recipes/decorations/dye_light_gray_bed.json
data/minecraft/advancement/recipes/decorations/dye_light_gray_carpet.json
data/minecraft/advancement/recipes/decorations/dye_lime_bed.json
data/minecraft/advancement/recipes/decorations/dye_lime_carpet.json
data/minecraft/advancement/recipes/decorations/dye_magenta_bed.json
data/minecraft/advancement/recipes/decorations/dye_magenta_carpet.json
data/minecraft/advancement/recipes/decorations/dye_orange_bed.json
data/minecraft/advancement/recipes/decorations/dye_orange_carpet.json
data/minecraft/advancement/recipes/decorations/dye_pink_bed.json
data/minecraft/advancement/recipes/decorations/dye_pink_carpet.json
data/minecraft/advancement/recipes/decorations/dye_purple_bed.json
data/minecraft/advancement/recipes/decorations/dye_purple_carpet.json
data/minecraft/advancement/recipes/decorations/dye_red_bed.json
data/minecraft/advancement/recipes/decorations/dye_red_carpet.json
data/minecraft/advancement/recipes/decorations/dye_white_bed.json
data/minecraft/advancement/recipes/decorations/dye_white_carpet.json
data/minecraft/advancement/recipes/decorations/dye_yellow_bed.json
data/minecraft/advancement/recipes/decorations/dye_yellow_carpet.json
data/minecraft/dialog/custom_options.json
data/minecraft/dialog/quick_actions.json
data/minecraft/dialog/server_links.json
data/minecraft/jukebox_song/tears.json
data/minecraft/loot_table/blocks/dried_ghast.json
data/minecraft/loot_table/entities/happy_ghast.json
data/minecraft/recipe/black_harness.json
data/minecraft/recipe/blue_harness.json
data/minecraft/recipe/brown_harness.json
data/minecraft/recipe/cyan_harness.json
data/minecraft/recipe/dried_ghast.json
data/minecraft/recipe/dye_black_harness.json
data/minecraft/recipe/dye_blue_harness.json
data/minecraft/recipe/dye_brown_harness.json
data/minecraft/recipe/dye_cyan_harness.json
data/minecraft/recipe/dye_gray_harness.json
data/minecraft/recipe/dye_green_harness.json
data/minecraft/recipe/dye_light_blue_harness.json
data/minecraft/recipe/dye_light_gray_harness.json
data/minecraft/recipe/dye_lime_harness.json
data/minecraft/recipe/dye_magenta_harness.json
data/minecraft/recipe/dye_orange_harness.json
data/minecraft/recipe/dye_pink_harness.json
data/minecraft/recipe/dye_purple_harness.json
data/minecraft/recipe/dye_red_harness.json
data/minecraft/recipe/dye_white_harness.json
data/minecraft/recipe/dye_yellow_harness.json
data/minecraft/recipe/gray_harness.json
data/minecraft/recipe/green_harness.json
data/minecraft/recipe/light_blue_harness.json
data/minecraft/recipe/light_gray_harness.json
data/minecraft/recipe/lime_harness.json
data/minecraft/recipe/magenta_harness.json
data/minecraft/recipe/orange_harness.json
data/minecraft/recipe/pink_harness.json
data/minecraft/recipe/purple_harness.json
data/minecraft/recipe/red_harness.json
data/minecraft/recipe/saddle.json
data/minecraft/recipe/white_harness.json
data/minecraft/recipe/yellow_harness.json
data/minecraft/tags/block/happy_ghast_avoids.json
data/minecraft/tags/block/triggers_ambient_desert_dry_vegetation_block_sounds.json
data/minecraft/tags/block/triggers_ambient_desert_sand_block_sounds.json
data/minecraft/tags/block/triggers_ambient_dried_ghast_block_sounds.json
data/minecraft/tags/dialog/pause_screen_additions.json
data/minecraft/tags/dialog/quick_actions.json
data/minecraft/tags/entity_type/can_equip_harness.json
data/minecraft/tags/entity_type/followable_friendly_mobs.json
data/minecraft/tags/item/happy_ghast_food.json
data/minecraft/tags/item/happy_ghast_tempt_items.json
data/minecraft/tags/item/harnesses.json
net/minecraft/block/DriedGhastBlock.java
net/minecraft/command/PermissionLevelPredicate.java
net/minecraft/command/PermissionLevelSource.java
net/minecraft/command/argument/HexColorArgumentType.java
net/minecraft/command/argument/WaypointArgument.java
net/minecraft/component/type/BlockPredicatesComponent.java
net/minecraft/data/tag/ProvidedTagBuilder.java
net/minecraft/data/tag/SimpleTagProvider.java
net/minecraft/data/tag/vanilla/VanillaBlockItemTags.java
net/minecraft/data/tag/vanilla/VanillaDialogTagProvider.java
net/minecraft/datafixer/fix/LegacyDimensionFix.java
net/minecraft/datafixer/fix/SignTextStrictJsonFix.java
net/minecraft/datafixer/fix/WrittenBookPagesStrictJsonFix.java
net/minecraft/datafixer/schema/Schema3439_1.java
net/minecraft/datafixer/schema/Schema4420.java
net/minecraft/datafixer/schema/Schema4421.java
net/minecraft/dialog/AfterAction.java
net/minecraft/dialog/DialogActionButtonData.java
net/minecraft/dialog/DialogActionTypes.java
net/minecraft/dialog/DialogBodyTypes.java
net/minecraft/dialog/DialogButtonData.java
net/minecraft/dialog/DialogCommonData.java
net/minecraft/dialog/DialogTypes.java
net/minecraft/dialog/Dialogs.java
net/minecraft/dialog/InputControlTypes.java
net/minecraft/dialog/action/DialogAction.java
net/minecraft/dialog/action/DynamicCustomDialogAction.java
net/minecraft/dialog/action/DynamicRunCommandDialogAction.java
net/minecraft/dialog/action/ParsedTemplate.java
net/minecraft/dialog/action/SimpleDialogAction.java
net/minecraft/dialog/body/DialogBody.java
net/minecraft/dialog/body/ItemDialogBody.java
net/minecraft/dialog/body/PlainMessageDialogBody.java
net/minecraft/dialog/input/BooleanInputControl.java
net/minecraft/dialog/input/InputControl.java
net/minecraft/dialog/input/NumberRangeInputControl.java
net/minecraft/dialog/input/SingleOptionInputControl.java
net/minecraft/dialog/input/TextInputControl.java
net/minecraft/dialog/type/ColumnsDialog.java
net/minecraft/dialog/type/ConfirmationDialog.java
net/minecraft/dialog/type/Dialog.java
net/minecraft/dialog/type/DialogInput.java
net/minecraft/dialog/type/DialogListDialog.java
net/minecraft/dialog/type/MultiActionDialog.java
net/minecraft/dialog/type/NoticeDialog.java
net/minecraft/dialog/type/ServerLinksDialog.java
net/minecraft/dialog/type/SimpleDialog.java
net/minecraft/entity/ai/brain/sensor/NearestFollowableFriendlyMobSensor.java
net/minecraft/entity/ai/brain/task/WalkTowardsEntityTask.java
net/minecraft/entity/passive/HappyGhastBrain.java
net/minecraft/entity/passive/HappyGhastEntity.java
net/minecraft/inventory/StackWithSlot.java
net/minecraft/network/packet/c2s/common/CustomClickActionC2SPacket.java
net/minecraft/network/packet/c2s/play/ChangeGameModeC2SPacket.java
net/minecraft/network/packet/s2c/common/ClearDialogS2CPacket.java
net/minecraft/network/packet/s2c/common/ShowDialogS2CPacket.java
net/minecraft/network/packet/s2c/play/WaypointS2CPacket.java
net/minecraft/registry/tag/DialogTags.java
net/minecraft/server/command/DialogCommand.java
net/minecraft/server/command/VersionCommand.java
net/minecraft/server/command/WaypointCommand.java
net/minecraft/server/network/ServerWaypointHandler.java
net/minecraft/storage/NbtReadView.java
net/minecraft/storage/NbtWriteView.java
net/minecraft/storage/ReadContext.java
net/minecraft/storage/ReadView.java
net/minecraft/storage/WriteView.java
net/minecraft/unused/packageinfo/PackageInfo11436.java
net/minecraft/unused/packageinfo/PackageInfo11445.java
net/minecraft/unused/packageinfo/PackageInfo11446.java
net/minecraft/unused/packageinfo/PackageInfo11526.java
net/minecraft/util/LenientJsonParser.java
net/minecraft/util/StrictJsonParser.java
net/minecraft/world/waypoint/ServerWaypoint.java
net/minecraft/world/waypoint/TrackedWaypoint.java
net/minecraft/world/waypoint/TrackedWaypointHandler.java
net/minecraft/world/waypoint/Waypoint.java
net/minecraft/world/waypoint/WaypointHandler.java
net/minecraft/world/waypoint/WaypointStyle.java
net/minecraft/world/waypoint/WaypointStyles.java