assets/minecraft/atlases/mob_effects.json
assets/minecraft/textures/gui/title/edition.png.mcmeta
assets/minecraft/textures/gui/title/minceraft.png.mcmeta
assets/minecraft/textures/gui/title/minecraft.png.mcmeta
assets/minecraft/textures/gui/title/realms.png.mcmeta
com/mojang/blaze3d/buffers/BufferType.java
com/mojang/blaze3d/buffers/BufferUsage.java
com/mojang/blaze3d/systems/ScissorState.java
net/minecraft/client/gl/FramebufferManager.java
net/minecraft/client/gl/GlResourceManager.java
net/minecraft/client/gl/Uniform.java
net/minecraft/client/gui/LayeredDrawer.java
net/minecraft/client/gui/screen/DialogScreen.java
net/minecraft/client/gui/screen/ServerLinksScreen.java
net/minecraft/client/realms/dto/RealmsWorldSettings.java
net/minecraft/client/realms/gui/screen/DisconnectedRealmsScreen.java
net/minecraft/client/realms/gui/screen/RealmsLongRunningTickableTaskScreen.java
net/minecraft/client/realms/gui/screen/RealmsPlayerScreen.java
net/minecraft/client/realms/gui/screen/RealmsSettingsScreen.java
net/minecraft/client/realms/gui/screen/RealmsSubscriptionInfoScreen.java
net/minecraft/client/render/BackgroundRenderer.java
net/minecraft/client/render/Fog.java
net/minecraft/client/render/FogShape.java
net/minecraft/client/texture/StatusEffectSpriteManager.java