package net.minecraft.client.render.entity.model;

import com.google.common.collect.ImmutableMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.block.WoodType;
import net.minecraft.client.model.Dilation;
import net.minecraft.client.model.TexturedModelData;
import net.minecraft.client.render.block.entity.BedBlockEntityRenderer;
import net.minecraft.client.render.block.entity.ConduitBlockEntityRenderer;
import net.minecraft.client.render.block.entity.DecoratedPotBlockEntityRenderer;
import net.minecraft.client.render.block.entity.HangingSignBlockEntityRenderer;
import net.minecraft.client.render.block.entity.SignBlockEntityRenderer;
import net.minecraft.client.render.block.entity.model.BannerBlockModel;
import net.minecraft.client.render.block.entity.model.BannerFlagBlockModel;
import net.minecraft.client.render.block.entity.model.BellBlockModel;
import net.minecraft.client.render.block.entity.model.ChestBlockModel;
import net.minecraft.client.render.entity.DragonEntityModel;
import net.minecraft.client.render.entity.WitherSkullEntityRenderer;

@Environment(EnvType.CLIENT)
public class EntityModels {
   private static final Dilation FISH_PATTERN_DILATION = new Dilation(0.008F);
   private static final Dilation ARMOR_DILATION = new Dilation(1.0F);
   private static final Dilation HAT_DILATION = new Dilation(0.5F);

   public static Map<EntityModelLayer, TexturedModelData> getModels() {
      ImmutableMap.Builder<EntityModelLayer, TexturedModelData> builder = ImmutableMap.builder();
      TexturedModelData texturedModelData = TexturedModelData.of(BipedEntityModel.getModelData(Dilation.NONE, 0.0F), 64, 64);
      TexturedModelData texturedModelData2 = TexturedModelData.of(ArmorEntityModel.getModelData(ARMOR_DILATION), 64, 32);
      TexturedModelData texturedModelData3 = TexturedModelData.of(ArmorEntityModel.getModelData(new Dilation(1.02F)), 64, 32);
      TexturedModelData texturedModelData4 = TexturedModelData.of(ArmorEntityModel.getModelData(HAT_DILATION), 64, 32);
      TexturedModelData texturedModelData5 = MinecartEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData6 = SkullEntityModel.getSkullTexturedModelData();
      TexturedModelData texturedModelData7 = TexturedModelData.of(AbstractHorseEntityModel.getModelData(Dilation.NONE), 64, 64);
      TexturedModelData texturedModelData8 = TexturedModelData.of(AbstractHorseEntityModel.getBabyHorseModelData(Dilation.NONE), 64, 64);
      TexturedModelData texturedModelData9 = HorseSaddleEntityModel.getTexturedModelData(false);
      TexturedModelData texturedModelData10 = HorseSaddleEntityModel.getTexturedModelData(true);
      ModelTransformer modelTransformer = ModelTransformer.scaling(0.9375F);
      TexturedModelData texturedModelData11 = IllagerEntityModel.getTexturedModelData().transform(modelTransformer);
      TexturedModelData texturedModelData12 = AxolotlEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData13 = BeeEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData14 = CowEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData15 = ColdChickenEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData16 = ColdCowEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData17 = ColdPigEntityModel.getTexturedModelData(Dilation.NONE);
      TexturedModelData texturedModelData18 = ElytraEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData19 = TexturedModelData.of(FelineEntityModel.getModelData(Dilation.NONE), 64, 32);
      TexturedModelData texturedModelData20 = texturedModelData19.transform(FelineEntityModel.BABY_TRANSFORMER);
      TexturedModelData texturedModelData21 = TexturedModelData.of(FelineEntityModel.getModelData(new Dilation(0.01F)), 64, 32);
      TexturedModelData texturedModelData22 = TexturedModelData.of(PiglinEntityModel.getModelData(Dilation.NONE), 64, 64);
      TexturedModelData texturedModelData23 = TexturedModelData.of(PiglinHeadEntityModel.getModelData(), 64, 64);
      TexturedModelData texturedModelData24 = SkullEntityModel.getHeadTexturedModelData();
      TexturedModelData texturedModelData25 = LlamaEntityModel.getTexturedModelData(Dilation.NONE);
      TexturedModelData texturedModelData26 = LlamaEntityModel.getTexturedModelData(new Dilation(0.5F));
      TexturedModelData texturedModelData27 = StriderEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData28 = HoglinEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData29 = HoglinEntityModel.getBabyTexturedModelData();
      TexturedModelData texturedModelData30 = SkeletonEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData31 = TexturedModelData.of(VillagerResemblingModel.getModelData(), 64, 64).transform(modelTransformer);
      TexturedModelData texturedModelData32 = SpiderEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData33 = ArmadilloEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData34 = CamelEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData35 = CamelSaddleEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData36 = ChickenEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData37 = GoatEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData38 = PandaEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData39 = PigEntityModel.getTexturedModelData(Dilation.NONE);
      TexturedModelData texturedModelData40 = PigEntityModel.getTexturedModelData(new Dilation(0.5F));
      TexturedModelData texturedModelData41 = SheepEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData42 = SheepWoolEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData43 = SnifferEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData44 = TurtleEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData45 = WarmCowEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData46 = TexturedModelData.of(WolfEntityModel.getTexturedModelData(Dilation.NONE), 64, 32);
      TexturedModelData texturedModelData47 = TexturedModelData.of(WolfEntityModel.getTexturedModelData(new Dilation(0.2F)), 64, 32);
      TexturedModelData texturedModelData48 = ZombieVillagerEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData49 = ArmorStandEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData50 = ArmorStandArmorEntityModel.getTexturedModelData(HAT_DILATION);
      TexturedModelData texturedModelData51 = ArmorStandArmorEntityModel.getTexturedModelData(ARMOR_DILATION);
      TexturedModelData texturedModelData52 = DrownedEntityModel.getTexturedModelData(Dilation.NONE);
      TexturedModelData texturedModelData53 = DrownedEntityModel.getTexturedModelData(new Dilation(0.25F));
      TexturedModelData texturedModelData54 = SquidEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData55 = DolphinEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData56 = SalmonEntityModel.getTexturedModelData();
      builder.put(EntityModelLayers.ALLAY, AllayEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.ARMADILLO, texturedModelData33);
      builder.put(EntityModelLayers.ARMADILLO_BABY, texturedModelData33.transform(ArmadilloEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ARMOR_STAND, texturedModelData49);
      builder.put(EntityModelLayers.ARMOR_STAND_INNER_ARMOR, texturedModelData50);
      builder.put(EntityModelLayers.ARMOR_STAND_OUTER_ARMOR, texturedModelData51);
      builder.put(EntityModelLayers.ARMOR_STAND_SMALL, texturedModelData49.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ARMOR_STAND_SMALL_INNER_ARMOR, texturedModelData50.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ARMOR_STAND_SMALL_OUTER_ARMOR, texturedModelData51.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ARROW, ArrowEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.AXOLOTL, texturedModelData12);
      builder.put(EntityModelLayers.AXOLOTL_BABY, texturedModelData12.transform(AxolotlEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.STANDING_BANNER, BannerBlockModel.getTexturedModelData(true));
      builder.put(EntityModelLayers.WALL_BANNER, BannerBlockModel.getTexturedModelData(false));
      builder.put(EntityModelLayers.STANDING_BANNER_FLAG, BannerFlagBlockModel.getTexturedModelData(true));
      builder.put(EntityModelLayers.WALL_BANNER_FLAG, BannerFlagBlockModel.getTexturedModelData(false));
      builder.put(EntityModelLayers.BAT, BatEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.BED_FOOT, BedBlockEntityRenderer.getFootTexturedModelData());
      builder.put(EntityModelLayers.BED_HEAD, BedBlockEntityRenderer.getHeadTexturedModelData());
      builder.put(EntityModelLayers.BEE, texturedModelData13);
      builder.put(EntityModelLayers.BEE_BABY, texturedModelData13.transform(BeeEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.BEE_STINGER, StingerModel.getTexturedModelData());
      builder.put(EntityModelLayers.BELL, BellBlockModel.getTexturedModelData());
      builder.put(EntityModelLayers.BLAZE, BlazeEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.BOAT, BoatEntityModel.getBaseTexturedModelData());
      builder.put(EntityModelLayers.BOGGED, BoggedEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.BOGGED_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.BOGGED_OUTER_ARMOR, texturedModelData2);
      builder.put(EntityModelLayers.BOGGED_OUTER, TexturedModelData.of(BipedEntityModel.getModelData(new Dilation(0.2F), 0.0F), 64, 32));
      builder.put(EntityModelLayers.BOOK, BookModel.getTexturedModelData());
      builder.put(EntityModelLayers.BREEZE, BreezeEntityModel.getTexturedModelData(32, 32));
      builder.put(EntityModelLayers.BREEZE_WIND, BreezeEntityModel.getTexturedModelData(128, 128));
      builder.put(EntityModelLayers.CAT, texturedModelData19.transform(CatEntityModel.CAT_TRANSFORMER));
      builder.put(EntityModelLayers.CAT_BABY, texturedModelData20.transform(CatEntityModel.CAT_TRANSFORMER));
      builder.put(EntityModelLayers.CAT_COLLAR, texturedModelData21.transform(CatEntityModel.CAT_TRANSFORMER));
      builder.put(EntityModelLayers.CAT_BABY_COLLAR, texturedModelData21.transform(FelineEntityModel.BABY_TRANSFORMER).transform(CatEntityModel.CAT_TRANSFORMER));
      builder.put(EntityModelLayers.CAMEL, texturedModelData34);
      builder.put(EntityModelLayers.CAMEL_BABY, texturedModelData34.transform(CamelEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.CAMEL_SADDLE, texturedModelData35);
      builder.put(EntityModelLayers.CAMEL_BABY_SADDLE, texturedModelData35.transform(CamelEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.CAVE_SPIDER, texturedModelData32.transform(ModelTransformer.scaling(0.7F)));
      builder.put(EntityModelLayers.CHEST, ChestBlockModel.getSingleTexturedModelData());
      builder.put(EntityModelLayers.CHEST_MINECART, texturedModelData5);
      builder.put(EntityModelLayers.CHICKEN, texturedModelData36);
      builder.put(EntityModelLayers.CHICKEN_BABY, texturedModelData36.transform(ChickenEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.COD, CodEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.COLD_CHICKEN, texturedModelData15);
      builder.put(EntityModelLayers.COLD_CHICKEN_BABY, texturedModelData15.transform(ChickenEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.COLD_COW, texturedModelData16);
      builder.put(EntityModelLayers.COLD_COW_BABY, texturedModelData16.transform(CowEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.COLD_PIG, texturedModelData17);
      builder.put(EntityModelLayers.COLD_PIG_BABY, texturedModelData17.transform(PigEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.COMMAND_BLOCK_MINECART, texturedModelData5);
      builder.put(EntityModelLayers.CONDUIT_EYE, ConduitBlockEntityRenderer.getEyeTexturedModelData());
      builder.put(EntityModelLayers.CONDUIT_WIND, ConduitBlockEntityRenderer.getWindTexturedModelData());
      builder.put(EntityModelLayers.CONDUIT_SHELL, ConduitBlockEntityRenderer.getShellTexturedModelData());
      builder.put(EntityModelLayers.CONDUIT, ConduitBlockEntityRenderer.getPlainTexturedModelData());
      builder.put(EntityModelLayers.COW, texturedModelData14);
      builder.put(EntityModelLayers.COW_BABY, texturedModelData14.transform(CowEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.CREAKING, CreakingEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.CREEPER, CreeperEntityModel.getTexturedModelData(Dilation.NONE));
      builder.put(EntityModelLayers.CREEPER_ARMOR, CreeperEntityModel.getTexturedModelData(new Dilation(2.0F)));
      builder.put(EntityModelLayers.CREEPER_HEAD, texturedModelData6);
      builder.put(EntityModelLayers.DECORATED_POT_BASE, DecoratedPotBlockEntityRenderer.getTopBottomNeckTexturedModelData());
      builder.put(EntityModelLayers.DECORATED_POT_SIDES, DecoratedPotBlockEntityRenderer.getSidesTexturedModelData());
      builder.put(EntityModelLayers.DOLPHIN, texturedModelData55);
      builder.put(EntityModelLayers.DOLPHIN_BABY, texturedModelData55.transform(DolphinEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.DONKEY, DonkeyEntityModel.getTexturedModelData(0.87F));
      builder.put(EntityModelLayers.DONKEY_BABY, DonkeyEntityModel.getBabyTexturedModelData(0.87F));
      builder.put(EntityModelLayers.DONKEY_SADDLE, DonkeyEntityModel.getSaddleTexturedModelData(0.87F, false));
      builder.put(EntityModelLayers.DONKEY_BABY_SADDLE, DonkeyEntityModel.getSaddleTexturedModelData(0.87F, true));
      builder.put(EntityModelLayers.DOUBLE_CHEST_LEFT, ChestBlockModel.getDoubleChestLeftTexturedBlockData());
      builder.put(EntityModelLayers.DOUBLE_CHEST_RIGHT, ChestBlockModel.getDoubleChestRightTexturedBlockData());
      builder.put(EntityModelLayers.DRAGON_SKULL, DragonHeadEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.DROWNED, texturedModelData52);
      builder.put(EntityModelLayers.DROWNED_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.DROWNED_OUTER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.DROWNED_OUTER, texturedModelData53);
      builder.put(EntityModelLayers.DROWNED_BABY, texturedModelData52.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.DROWNED_BABY_INNER_ARMOR, texturedModelData4.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.DROWNED_BABY_OUTER_ARMOR, texturedModelData4.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.DROWNED_BABY_OUTER, texturedModelData53.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ELDER_GUARDIAN, GuardianEntityModel.getElderTexturedModelData());
      builder.put(EntityModelLayers.ELYTRA, texturedModelData18);
      builder.put(EntityModelLayers.ELYTRA_BABY, texturedModelData18.transform(ElytraEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ENDERMAN, EndermanEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.ENDERMITE, EndermiteEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.ENDER_DRAGON, DragonEntityModel.createTexturedModelData());
      builder.put(EntityModelLayers.END_CRYSTAL, EndCrystalEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.EVOKER, texturedModelData11);
      builder.put(EntityModelLayers.EVOKER_FANGS, EvokerFangsEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.FOX, FoxEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.FOX_BABY, FoxEntityModel.getTexturedModelData().transform(FoxEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.FROG, FrogEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.FURNACE_MINECART, texturedModelData5);
      builder.put(EntityModelLayers.GHAST, GhastEntityModel.getTexturedModelData());
      ModelTransformer modelTransformer2 = ModelTransformer.scaling(6.0F);
      builder.put(EntityModelLayers.GIANT, texturedModelData.transform(modelTransformer2));
      builder.put(EntityModelLayers.GIANT_INNER_ARMOR, texturedModelData4.transform(modelTransformer2));
      builder.put(EntityModelLayers.GIANT_OUTER_ARMOR, texturedModelData2.transform(modelTransformer2));
      builder.put(EntityModelLayers.GLOW_SQUID, texturedModelData54);
      builder.put(EntityModelLayers.GLOW_SQUID_BABY, texturedModelData54.transform(SquidEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.GOAT, texturedModelData37);
      builder.put(EntityModelLayers.GOAT_BABY, texturedModelData37.transform(GoatEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.GUARDIAN, GuardianEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.HAPPY_GHAST, HappyGhastEntityModel.getTexturedModelData(false, Dilation.NONE));
      builder.put(EntityModelLayers.HAPPY_GHAST_BABY, HappyGhastEntityModel.getTexturedModelData(true, Dilation.NONE).transform(HappyGhastEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.HAPPY_GHAST_HARNESS, HappyGhastHarnessEntityModel.getTexturedModelData(false));
      builder.put(EntityModelLayers.HAPPY_GHAST_BABY_HARNESS, HappyGhastHarnessEntityModel.getTexturedModelData(true).transform(HappyGhastEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.HAPPY_GHAST_ROPES, HappyGhastEntityModel.getTexturedModelData(false, new Dilation(0.2F)));
      builder.put(EntityModelLayers.HAPPY_GHAST_BABY_ROPES, HappyGhastEntityModel.getTexturedModelData(true, new Dilation(0.2F)).transform(HappyGhastEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.HOGLIN, texturedModelData28);
      builder.put(EntityModelLayers.HOGLIN_BABY, texturedModelData29);
      builder.put(EntityModelLayers.HOPPER_MINECART, texturedModelData5);
      ModelTransformer modelTransformer3 = ModelTransformer.scaling(1.1F);
      builder.put(EntityModelLayers.HORSE, texturedModelData7.transform(modelTransformer3));
      builder.put(EntityModelLayers.HORSE_BABY, texturedModelData8.transform(modelTransformer3));
      builder.put(EntityModelLayers.HORSE_ARMOR, TexturedModelData.of(AbstractHorseEntityModel.getModelData(new Dilation(0.1F)), 64, 64).transform(modelTransformer3));
      builder.put(EntityModelLayers.HORSE_ARMOR_BABY, TexturedModelData.of(AbstractHorseEntityModel.getBabyHorseModelData(new Dilation(0.1F)), 64, 64).transform(modelTransformer3));
      builder.put(EntityModelLayers.HORSE_SADDLE, texturedModelData9.transform(modelTransformer3));
      builder.put(EntityModelLayers.HORSE_BABY_SADDLE, texturedModelData10.transform(modelTransformer3));
      ModelTransformer modelTransformer4 = ModelTransformer.scaling(1.0625F);
      builder.put(EntityModelLayers.HUSK, texturedModelData.transform(modelTransformer4));
      builder.put(EntityModelLayers.HUSK_INNER_ARMOR, texturedModelData4.transform(modelTransformer4));
      builder.put(EntityModelLayers.HUSK_OUTER_ARMOR, texturedModelData2.transform(modelTransformer4));
      builder.put(EntityModelLayers.HUSK_BABY, texturedModelData.transform(BipedEntityModel.BABY_TRANSFORMER).transform(modelTransformer4));
      builder.put(EntityModelLayers.HUSK_BABY_INNER_ARMOR, texturedModelData4.transform(BipedEntityModel.BABY_TRANSFORMER).transform(modelTransformer4));
      builder.put(EntityModelLayers.HUSK_BABY_OUTER_ARMOR, texturedModelData2.transform(BipedEntityModel.BABY_TRANSFORMER).transform(modelTransformer4));
      builder.put(EntityModelLayers.ILLUSIONER, texturedModelData11);
      builder.put(EntityModelLayers.IRON_GOLEM, IronGolemEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.LEASH_KNOT, LeashKnotEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.LLAMA, texturedModelData25);
      builder.put(EntityModelLayers.LLAMA_BABY, texturedModelData25.transform(LlamaEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.LLAMA_DECOR, texturedModelData26);
      builder.put(EntityModelLayers.LLAMA_BABY_DECOR, texturedModelData26.transform(LlamaEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.LLAMA_SPIT, LlamaSpitEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.MAGMA_CUBE, MagmaCubeEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.MINECART, texturedModelData5);
      builder.put(EntityModelLayers.MOOSHROOM, texturedModelData14);
      builder.put(EntityModelLayers.MOOSHROOM_BABY, texturedModelData14.transform(CowEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.MULE, DonkeyEntityModel.getTexturedModelData(0.92F));
      builder.put(EntityModelLayers.MULE_BABY, DonkeyEntityModel.getBabyTexturedModelData(0.92F));
      builder.put(EntityModelLayers.MULE_SADDLE, DonkeyEntityModel.getSaddleTexturedModelData(0.92F, false));
      builder.put(EntityModelLayers.MULE_BABY_SADDLE, DonkeyEntityModel.getSaddleTexturedModelData(0.92F, true));
      builder.put(EntityModelLayers.OCELOT, texturedModelData19);
      builder.put(EntityModelLayers.OCELOT_BABY, texturedModelData20);
      builder.put(EntityModelLayers.PANDA, texturedModelData38);
      builder.put(EntityModelLayers.PANDA_BABY, texturedModelData38.transform(PandaEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.PARROT, ParrotEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.PHANTOM, PhantomEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.PIG, texturedModelData39);
      builder.put(EntityModelLayers.PIG_BABY, texturedModelData39.transform(PigEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.PIG_SADDLE, texturedModelData40);
      builder.put(EntityModelLayers.PIG_BABY_SADDLE, texturedModelData40.transform(PigEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.PIGLIN, texturedModelData22);
      builder.put(EntityModelLayers.PIGLIN_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.PIGLIN_OUTER_ARMOR, texturedModelData3);
      builder.put(EntityModelLayers.PIGLIN_BRUTE, texturedModelData22);
      builder.put(EntityModelLayers.PIGLIN_BRUTE_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.PIGLIN_BRUTE_OUTER_ARMOR, texturedModelData3);
      builder.put(EntityModelLayers.PIGLIN_BABY, texturedModelData22.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.PIGLIN_BABY_INNER_ARMOR, texturedModelData4.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.PIGLIN_BABY_OUTER_ARMOR, texturedModelData3.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.PIGLIN_HEAD, texturedModelData23);
      builder.put(EntityModelLayers.PILLAGER, texturedModelData11);
      builder.put(EntityModelLayers.PLAYER, TexturedModelData.of(PlayerEntityModel.getTexturedModelData(Dilation.NONE, false), 64, 64));
      builder.put(EntityModelLayers.PLAYER_EARS, Deadmau5EarsEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.PLAYER_CAPE, PlayerCapeModel.getTexturedModelData());
      builder.put(EntityModelLayers.PLAYER_HEAD, texturedModelData24);
      builder.put(EntityModelLayers.PLAYER_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.PLAYER_OUTER_ARMOR, texturedModelData2);
      builder.put(EntityModelLayers.PLAYER_SLIM, TexturedModelData.of(PlayerEntityModel.getTexturedModelData(Dilation.NONE, true), 64, 64));
      builder.put(EntityModelLayers.PLAYER_SLIM_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.PLAYER_SLIM_OUTER_ARMOR, texturedModelData2);
      builder.put(EntityModelLayers.SPIN_ATTACK, TridentRiptideEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.POLAR_BEAR, PolarBearEntityModel.getTexturedModelData(false));
      builder.put(EntityModelLayers.POLAR_BEAR_BABY, PolarBearEntityModel.getTexturedModelData(true));
      builder.put(EntityModelLayers.PUFFERFISH_BIG, LargePufferfishEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.PUFFERFISH_MEDIUM, MediumPufferfishEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.PUFFERFISH_SMALL, SmallPufferfishEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.RABBIT, RabbitEntityModel.getTexturedModelData(false));
      builder.put(EntityModelLayers.RABBIT_BABY, RabbitEntityModel.getTexturedModelData(true));
      builder.put(EntityModelLayers.RAVAGER, RavagerEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.SALMON, texturedModelData56);
      builder.put(EntityModelLayers.SALMON_SMALL, texturedModelData56.transform(SalmonEntityModel.SMALL_TRANSFORMER));
      builder.put(EntityModelLayers.SALMON_LARGE, texturedModelData56.transform(SalmonEntityModel.LARGE_TRANSFORMER));
      builder.put(EntityModelLayers.SHEEP, texturedModelData41);
      builder.put(EntityModelLayers.SHEEP_BABY, texturedModelData41.transform(SheepEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.SHEEP_WOOL, texturedModelData42);
      builder.put(EntityModelLayers.SHEEP_BABY_WOOL, texturedModelData42.transform(SheepEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.SHEEP_WOOL_UNDERCOAT, texturedModelData41);
      builder.put(EntityModelLayers.SHEEP_BABY_WOOL_UNDERCOAT, texturedModelData41.transform(SheepEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.SHIELD, ShieldEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.SHULKER, ShulkerEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.SHULKER_BOX, ShulkerEntityModel.getShulkerBoxTexturedModelData());
      builder.put(EntityModelLayers.SHULKER_BULLET, ShulkerBulletEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.SILVERFISH, SilverfishEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.SKELETON, texturedModelData30);
      builder.put(EntityModelLayers.SKELETON_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.SKELETON_OUTER_ARMOR, texturedModelData2);
      builder.put(EntityModelLayers.SKELETON_HORSE, texturedModelData7);
      builder.put(EntityModelLayers.SKELETON_HORSE_BABY, texturedModelData8);
      builder.put(EntityModelLayers.SKELETON_HORSE_SADDLE, texturedModelData9);
      builder.put(EntityModelLayers.SKELETON_HORSE_BABY_SADDLE, texturedModelData10);
      builder.put(EntityModelLayers.SKELETON_SKULL, texturedModelData6);
      builder.put(EntityModelLayers.SLIME, SlimeEntityModel.getInnerTexturedModelData());
      builder.put(EntityModelLayers.SLIME_OUTER, SlimeEntityModel.getOuterTexturedModelData());
      builder.put(EntityModelLayers.SNIFFER, texturedModelData43);
      builder.put(EntityModelLayers.SNIFFER_BABY, texturedModelData43.transform(SnifferEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.SNOW_GOLEM, SnowGolemEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.SPAWNER_MINECART, texturedModelData5);
      builder.put(EntityModelLayers.SPIDER, texturedModelData32);
      builder.put(EntityModelLayers.SQUID, texturedModelData54);
      builder.put(EntityModelLayers.SQUID_BABY, texturedModelData54.transform(SquidEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.STRAY, texturedModelData30);
      builder.put(EntityModelLayers.STRAY_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.STRAY_OUTER_ARMOR, texturedModelData2);
      builder.put(EntityModelLayers.STRAY_OUTER, TexturedModelData.of(BipedEntityModel.getModelData(new Dilation(0.25F), 0.0F), 64, 32));
      builder.put(EntityModelLayers.STRIDER, texturedModelData27);
      builder.put(EntityModelLayers.STRIDER_SADDLE, texturedModelData27);
      builder.put(EntityModelLayers.STRIDER_BABY, texturedModelData27.transform(StriderEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.STRIDER_BABY_SADDLE, texturedModelData27.transform(StriderEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.TADPOLE, TadpoleEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.TNT_MINECART, texturedModelData5);
      builder.put(EntityModelLayers.TRADER_LLAMA, texturedModelData25);
      builder.put(EntityModelLayers.TRADER_LLAMA_BABY, texturedModelData25.transform(LlamaEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.TRIDENT, TridentEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.TROPICAL_FISH_LARGE, LargeTropicalFishEntityModel.getTexturedModelData(Dilation.NONE));
      builder.put(EntityModelLayers.TROPICAL_FISH_LARGE_PATTERN, LargeTropicalFishEntityModel.getTexturedModelData(FISH_PATTERN_DILATION));
      builder.put(EntityModelLayers.TROPICAL_FISH_SMALL, SmallTropicalFishEntityModel.getTexturedModelData(Dilation.NONE));
      builder.put(EntityModelLayers.TROPICAL_FISH_SMALL_PATTERN, SmallTropicalFishEntityModel.getTexturedModelData(FISH_PATTERN_DILATION));
      builder.put(EntityModelLayers.TURTLE, texturedModelData44);
      builder.put(EntityModelLayers.TURTLE_BABY, texturedModelData44.transform(TurtleEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.VEX, VexEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.VILLAGER, texturedModelData31);
      builder.put(EntityModelLayers.VILLAGER_BABY, texturedModelData31.transform(VillagerResemblingModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.VINDICATOR, texturedModelData11);
      builder.put(EntityModelLayers.WARDEN, WardenEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.WARM_COW, texturedModelData45);
      builder.put(EntityModelLayers.WARM_COW_BABY, texturedModelData45.transform(CowEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.WANDERING_TRADER, texturedModelData31);
      builder.put(EntityModelLayers.WIND_CHARGE, WindChargeEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.WITCH, WitchEntityModel.getTexturedModelData().transform(modelTransformer));
      builder.put(EntityModelLayers.WITHER, WitherEntityModel.getTexturedModelData(Dilation.NONE));
      builder.put(EntityModelLayers.WITHER_ARMOR, WitherEntityModel.getTexturedModelData(HAT_DILATION));
      builder.put(EntityModelLayers.WITHER_SKULL, WitherSkullEntityRenderer.getTexturedModelData());
      ModelTransformer modelTransformer5 = ModelTransformer.scaling(1.2F);
      builder.put(EntityModelLayers.WITHER_SKELETON, texturedModelData30.transform(modelTransformer5));
      builder.put(EntityModelLayers.WITHER_SKELETON_INNER_ARMOR, texturedModelData4.transform(modelTransformer5));
      builder.put(EntityModelLayers.WITHER_SKELETON_OUTER_ARMOR, texturedModelData2.transform(modelTransformer5));
      builder.put(EntityModelLayers.WITHER_SKELETON_SKULL, texturedModelData6);
      builder.put(EntityModelLayers.WOLF, texturedModelData46);
      builder.put(EntityModelLayers.WOLF_ARMOR, texturedModelData47);
      builder.put(EntityModelLayers.WOLF_BABY, texturedModelData46.transform(WolfEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.WOLF_BABY_ARMOR, texturedModelData47.transform(WolfEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ZOGLIN, texturedModelData28);
      builder.put(EntityModelLayers.ZOGLIN_BABY, texturedModelData29);
      builder.put(EntityModelLayers.ZOMBIE, texturedModelData);
      builder.put(EntityModelLayers.ZOMBIE_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.ZOMBIE_OUTER_ARMOR, texturedModelData2);
      builder.put(EntityModelLayers.ZOMBIE_BABY, texturedModelData.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ZOMBIE_BABY_INNER_ARMOR, texturedModelData4.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ZOMBIE_BABY_OUTER_ARMOR, texturedModelData2.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ZOMBIE_HEAD, texturedModelData24);
      builder.put(EntityModelLayers.ZOMBIE_HORSE, texturedModelData7);
      builder.put(EntityModelLayers.ZOMBIE_HORSE_BABY, texturedModelData8);
      builder.put(EntityModelLayers.ZOMBIE_HORSE_SADDLE, texturedModelData9);
      builder.put(EntityModelLayers.ZOMBIE_HORSE_BABY_SADDLE, texturedModelData10);
      builder.put(EntityModelLayers.ZOMBIE_VILLAGER, texturedModelData48);
      builder.put(EntityModelLayers.ZOMBIE_VILLAGER_INNER_ARMOR, ZombieVillagerEntityModel.getArmorTexturedModelData(HAT_DILATION));
      builder.put(EntityModelLayers.ZOMBIE_VILLAGER_OUTER_ARMOR, ZombieVillagerEntityModel.getArmorTexturedModelData(ARMOR_DILATION));
      builder.put(EntityModelLayers.ZOMBIE_VILLAGER_BABY, texturedModelData48.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ZOMBIE_VILLAGER_BABY_INNER_ARMOR, ZombieVillagerEntityModel.getArmorTexturedModelData(HAT_DILATION).transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ZOMBIE_VILLAGER_BABY_OUTER_ARMOR, ZombieVillagerEntityModel.getArmorTexturedModelData(ARMOR_DILATION).transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ZOMBIFIED_PIGLIN, texturedModelData22);
      builder.put(EntityModelLayers.ZOMBIFIED_PIGLIN_INNER_ARMOR, texturedModelData4);
      builder.put(EntityModelLayers.ZOMBIFIED_PIGLIN_OUTER_ARMOR, texturedModelData3);
      builder.put(EntityModelLayers.ZOMBIFIED_PIGLIN_BABY, texturedModelData22.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ZOMBIFIED_PIGLIN_BABY_INNER_ARMOR, texturedModelData4.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.ZOMBIFIED_PIGLIN_BABY_OUTER_ARMOR, texturedModelData3.transform(BipedEntityModel.BABY_TRANSFORMER));
      builder.put(EntityModelLayers.BAMBOO_BOAT, RaftEntityModel.getTexturedModelData());
      builder.put(EntityModelLayers.BAMBOO_CHEST_BOAT, RaftEntityModel.getChestTexturedModelData());
      TexturedModelData texturedModelData57 = BoatEntityModel.getTexturedModelData();
      TexturedModelData texturedModelData58 = BoatEntityModel.getChestTexturedModelData();
      builder.put(EntityModelLayers.OAK_BOAT, texturedModelData57);
      builder.put(EntityModelLayers.OAK_CHEST_BOAT, texturedModelData58);
      builder.put(EntityModelLayers.SPRUCE_BOAT, texturedModelData57);
      builder.put(EntityModelLayers.SPRUCE_CHEST_BOAT, texturedModelData58);
      builder.put(EntityModelLayers.BIRCH_BOAT, texturedModelData57);
      builder.put(EntityModelLayers.BIRCH_CHEST_BOAT, texturedModelData58);
      builder.put(EntityModelLayers.JUNGLE_BOAT, texturedModelData57);
      builder.put(EntityModelLayers.JUNGLE_CHEST_BOAT, texturedModelData58);
      builder.put(EntityModelLayers.ACACIA_BOAT, texturedModelData57);
      builder.put(EntityModelLayers.ACACIA_CHEST_BOAT, texturedModelData58);
      builder.put(EntityModelLayers.CHERRY_BOAT, texturedModelData57);
      builder.put(EntityModelLayers.CHERRY_CHEST_BOAT, texturedModelData58);
      builder.put(EntityModelLayers.DARK_OAK_BOAT, texturedModelData57);
      builder.put(EntityModelLayers.DARK_OAK_CHEST_BOAT, texturedModelData58);
      builder.put(EntityModelLayers.PALE_OAK_BOAT, texturedModelData57);
      builder.put(EntityModelLayers.PALE_OAK_CHEST_BOAT, texturedModelData58);
      builder.put(EntityModelLayers.MANGROVE_BOAT, texturedModelData57);
      builder.put(EntityModelLayers.MANGROVE_CHEST_BOAT, texturedModelData58);
      TexturedModelData texturedModelData59 = SignBlockEntityRenderer.getTexturedModelData(true);
      TexturedModelData texturedModelData60 = SignBlockEntityRenderer.getTexturedModelData(false);
      WoodType.stream().forEach((woodType) -> {
         builder.put(EntityModelLayers.createStandingSign(woodType), texturedModelData59);
         builder.put(EntityModelLayers.createWallSign(woodType), texturedModelData60);

         for(HangingSignBlockEntityRenderer.AttachmentType attachmentType : HangingSignBlockEntityRenderer.AttachmentType.values()) {
            TexturedModelData texturedModelData3 = HangingSignBlockEntityRenderer.getTexturedModelData(attachmentType);
            builder.put(EntityModelLayers.createHangingSign(woodType, attachmentType), texturedModelData3);
         }

      });
      ImmutableMap<EntityModelLayer, TexturedModelData> immutableMap = builder.build();
      List<EntityModelLayer> list = (List)EntityModelLayers.getLayers().filter((layer) -> !immutableMap.containsKey(layer)).collect(Collectors.toList());
      if (!list.isEmpty()) {
         throw new IllegalStateException("Missing layer definitions: " + String.valueOf(list));
      } else {
         return immutableMap;
      }
   }
}
