package net.minecraft.client.render.entity;

import java.util.List;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.block.BlockRenderType;
import net.minecraft.block.BlockState;
import net.minecraft.client.render.Frustum;
import net.minecraft.client.render.OverlayTexture;
import net.minecraft.client.render.RenderLayers;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.block.BlockRenderManager;
import net.minecraft.client.render.entity.state.FallingBlockEntityRenderState;
import net.minecraft.client.render.model.BlockModelPart;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.FallingBlockEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.random.Random;

@Environment(EnvType.CLIENT)
public class FallingBlockEntityRenderer extends EntityRenderer<FallingBlockEntity, FallingBlockEntityRenderState> {
   private final BlockRenderManager blockRenderManager;

   public FallingBlockEntityRenderer(EntityRendererFactory.Context context) {
      super(context);
      this.shadowRadius = 0.5F;
      this.blockRenderManager = context.getBlockRenderManager();
   }

   public boolean shouldRender(FallingBlockEntity fallingBlockEntity, Frustum frustum, double d, double e, double f) {
      if (!super.shouldRender(fallingBlockEntity, frustum, d, e, f)) {
         return false;
      } else {
         return fallingBlockEntity.getBlockState() != fallingBlockEntity.getWorld().getBlockState(fallingBlockEntity.getBlockPos());
      }
   }

   public void render(FallingBlockEntityRenderState fallingBlockEntityRenderState, MatrixStack matrixStack, VertexConsumerProvider vertexConsumerProvider, int i) {
      BlockState blockState = fallingBlockEntityRenderState.blockState;
      if (blockState.getRenderType() == BlockRenderType.MODEL) {
         matrixStack.push();
         matrixStack.translate((double)-0.5F, (double)0.0F, (double)-0.5F);
         List<BlockModelPart> list = this.blockRenderManager.getModel(blockState).getParts(Random.create(blockState.getRenderingSeed(fallingBlockEntityRenderState.fallingBlockPos)));
         this.blockRenderManager.getModelRenderer().render(fallingBlockEntityRenderState, list, blockState, fallingBlockEntityRenderState.currentPos, matrixStack, vertexConsumerProvider.getBuffer(RenderLayers.getMovingBlockLayer(blockState)), false, OverlayTexture.DEFAULT_UV);
         matrixStack.pop();
         super.render(fallingBlockEntityRenderState, matrixStack, vertexConsumerProvider, i);
      }
   }

   public FallingBlockEntityRenderState createRenderState() {
      return new FallingBlockEntityRenderState();
   }

   public void updateRenderState(FallingBlockEntity fallingBlockEntity, FallingBlockEntityRenderState fallingBlockEntityRenderState, float f) {
      super.updateRenderState(fallingBlockEntity, fallingBlockEntityRenderState, f);
      BlockPos blockPos = BlockPos.ofFloored(fallingBlockEntity.getX(), fallingBlockEntity.getBoundingBox().maxY, fallingBlockEntity.getZ());
      fallingBlockEntityRenderState.fallingBlockPos = fallingBlockEntity.getFallingBlockPos();
      fallingBlockEntityRenderState.currentPos = blockPos;
      fallingBlockEntityRenderState.blockState = fallingBlockEntity.getBlockState();
      fallingBlockEntityRenderState.biome = fallingBlockEntity.getWorld().getBiome(blockPos);
      fallingBlockEntityRenderState.world = fallingBlockEntity.getWorld();
   }
}
