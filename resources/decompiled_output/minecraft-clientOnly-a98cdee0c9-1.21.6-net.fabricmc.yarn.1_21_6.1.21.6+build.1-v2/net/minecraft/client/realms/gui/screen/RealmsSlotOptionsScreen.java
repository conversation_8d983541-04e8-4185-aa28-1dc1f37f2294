package net.minecraft.client.realms.gui.screen;

import com.google.common.collect.ImmutableList;
import java.util.List;
import java.util.function.Consumer;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.CyclingButtonWidget;
import net.minecraft.client.gui.widget.SliderWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.client.realms.RealmsLabel;
import net.minecraft.client.realms.dto.RealmsServer;
import net.minecraft.client.realms.dto.RealmsSlot;
import net.minecraft.client.realms.dto.RealmsWorldOptions;
import net.minecraft.client.realms.gui.RealmsPopups;
import net.minecraft.screen.ScreenTexts;
import net.minecraft.text.StringVisitable;
import net.minecraft.text.Text;
import net.minecraft.util.math.MathHelper;
import net.minecraft.world.Difficulty;
import net.minecraft.world.GameMode;

@Environment(EnvType.CLIENT)
public class RealmsSlotOptionsScreen extends RealmsScreen {
   private static final int field_32125 = 2;
   public static final List<Difficulty> DIFFICULTIES;
   private static final int field_32126 = 0;
   public static final List<GameMode> GAME_MODES;
   private static final Text EDIT_SLOT_NAME;
   static final Text SPAWN_PROTECTION;
   private TextFieldWidget nameEdit;
   protected final RealmsConfigureWorldScreen parent;
   private int column1_x;
   private int column2_x;
   private final RealmsSlot slot;
   private final RealmsServer.WorldType worldType;
   private Difficulty difficulty;
   private GameMode gameMode;
   private final String defaultSlotName;
   private String slotName;
   private boolean pvp;
   private boolean spawnMonsters;
   int spawnProtection;
   private boolean commandBlocks;
   private boolean forceGameMode;
   SettingsSlider spawnProtectionButton;

   public RealmsSlotOptionsScreen(RealmsConfigureWorldScreen parent, RealmsSlot slot, RealmsServer.WorldType worldType, int activeSlot) {
      super(Text.translatable("mco.configure.world.buttons.options"));
      this.parent = parent;
      this.slot = slot;
      this.worldType = worldType;
      this.difficulty = (Difficulty)get(DIFFICULTIES, slot.options.difficulty, 2);
      this.gameMode = (GameMode)get(GAME_MODES, slot.options.gameMode, 0);
      this.defaultSlotName = slot.options.getDefaultSlotName(activeSlot);
      this.setSlotName(slot.options.getSlotName(activeSlot));
      if (worldType == RealmsServer.WorldType.NORMAL) {
         this.pvp = slot.options.pvp;
         this.spawnProtection = slot.options.spawnProtection;
         this.forceGameMode = slot.options.forceGameMode;
         this.spawnMonsters = slot.options.spawnMonsters;
         this.commandBlocks = slot.options.commandBlocks;
      } else {
         this.pvp = true;
         this.spawnProtection = 0;
         this.forceGameMode = false;
         this.spawnMonsters = true;
         this.commandBlocks = true;
      }

   }

   public void close() {
      this.client.setScreen(this.parent);
   }

   private static <T> T get(List<T> list, int index, int fallbackIndex) {
      try {
         return (T)list.get(index);
      } catch (IndexOutOfBoundsException var4) {
         return (T)list.get(fallbackIndex);
      }
   }

   private static <T> int indexOf(List<T> list, T value, int fallbackIndex) {
      int i = list.indexOf(value);
      return i == -1 ? fallbackIndex : i;
   }

   public void init() {
      this.column2_x = 170;
      this.column1_x = this.width / 2 - this.column2_x;
      int i = this.width / 2 + 10;
      if (this.worldType != RealmsServer.WorldType.NORMAL) {
         Text text;
         if (this.worldType == RealmsServer.WorldType.ADVENTUREMAP) {
            text = Text.translatable("mco.configure.world.edit.subscreen.adventuremap");
         } else if (this.worldType == RealmsServer.WorldType.INSPIRATION) {
            text = Text.translatable("mco.configure.world.edit.subscreen.inspiration");
         } else {
            text = Text.translatable("mco.configure.world.edit.subscreen.experience");
         }

         this.addLabel(new RealmsLabel(text, this.width / 2, 26, -65536));
      }

      this.nameEdit = (TextFieldWidget)this.addSelectableChild(new TextFieldWidget(this.client.textRenderer, this.column1_x, row(1), this.column2_x, 20, (TextFieldWidget)null, Text.translatable("mco.configure.world.edit.slot.name")));
      this.nameEdit.setText(this.slotName);
      this.nameEdit.setChangedListener(this::setSlotName);
      CyclingButtonWidget<Boolean> cyclingButtonWidget = (CyclingButtonWidget)this.addDrawableChild(CyclingButtonWidget.onOffBuilder(this.pvp).build(i, row(1), this.column2_x, 20, Text.translatable("mco.configure.world.pvp"), (button, pvp) -> this.pvp = pvp));
      CyclingButtonWidget<GameMode> cyclingButtonWidget2 = (CyclingButtonWidget)this.addDrawableChild(CyclingButtonWidget.builder(GameMode::getSimpleTranslatableName).values(GAME_MODES).initially(this.gameMode).build(this.column1_x, row(3), this.column2_x, 20, Text.translatable("selectWorld.gameMode"), (button, gameModeIndex) -> this.gameMode = gameModeIndex));
      this.spawnProtectionButton = (SettingsSlider)this.addDrawableChild(new SettingsSlider(i, row(3), this.column2_x, this.spawnProtection, 0.0F, 16.0F));
      Text text2 = Text.translatable("mco.configure.world.spawn_toggle.message");
      CyclingButtonWidget<Boolean> cyclingButtonWidget3 = CyclingButtonWidget.onOffBuilder(this.difficulty != Difficulty.PEACEFUL && this.spawnMonsters).build(i, row(5), this.column2_x, 20, Text.translatable("mco.configure.world.spawnMonsters"), this.getSpawnToggleButtonCallback(text2, (spawnMonsters) -> this.spawnMonsters = spawnMonsters));
      CyclingButtonWidget<Difficulty> cyclingButtonWidget4 = (CyclingButtonWidget)this.addDrawableChild(CyclingButtonWidget.builder(Difficulty::getTranslatableName).values(DIFFICULTIES).initially(this.difficulty).build(this.column1_x, row(5), this.column2_x, 20, Text.translatable("options.difficulty"), (button, difficulty) -> {
         this.difficulty = difficulty;
         if (this.worldType == RealmsServer.WorldType.NORMAL) {
            boolean bl = this.difficulty != Difficulty.PEACEFUL;
            cyclingButtonWidget3.active = bl;
            cyclingButtonWidget3.setValue(bl && this.spawnMonsters);
         }

      }));
      this.addDrawableChild(cyclingButtonWidget3);
      CyclingButtonWidget<Boolean> cyclingButtonWidget5 = (CyclingButtonWidget)this.addDrawableChild(CyclingButtonWidget.onOffBuilder(this.forceGameMode).build(this.column1_x, row(7), this.column2_x, 20, Text.translatable("mco.configure.world.forceGameMode"), (button, forceGameMode) -> this.forceGameMode = forceGameMode));
      CyclingButtonWidget<Boolean> cyclingButtonWidget6 = (CyclingButtonWidget)this.addDrawableChild(CyclingButtonWidget.onOffBuilder(this.commandBlocks).build(i, row(7), this.column2_x, 20, Text.translatable("mco.configure.world.commandBlocks"), (button, commandBlocks) -> this.commandBlocks = commandBlocks));
      if (this.worldType != RealmsServer.WorldType.NORMAL) {
         cyclingButtonWidget.active = false;
         cyclingButtonWidget3.active = false;
         this.spawnProtectionButton.active = false;
         cyclingButtonWidget5.active = false;
      }

      if (this.difficulty == Difficulty.PEACEFUL) {
         cyclingButtonWidget3.active = false;
      }

      if (this.slot.isHardcore()) {
         cyclingButtonWidget6.active = false;
         cyclingButtonWidget4.active = false;
         cyclingButtonWidget2.active = false;
         cyclingButtonWidget3.active = false;
         cyclingButtonWidget5.active = false;
      }

      this.addDrawableChild(ButtonWidget.builder(Text.translatable("mco.configure.world.buttons.done"), (button) -> this.saveSettings()).dimensions(this.column1_x, row(13), this.column2_x, 20).build());
      this.addDrawableChild(ButtonWidget.builder(ScreenTexts.CANCEL, (button) -> this.close()).dimensions(i, row(13), this.column2_x, 20).build());
   }

   private CyclingButtonWidget.UpdateCallback<Boolean> getSpawnToggleButtonCallback(Text text, Consumer<Boolean> valueSetter) {
      return (button, value) -> {
         if (value) {
            valueSetter.accept(true);
         } else {
            this.client.setScreen(RealmsPopups.createContinuableWarningPopup(this, text, (popup) -> {
               valueSetter.accept(false);
               popup.close();
            }));
         }

      };
   }

   public Text getNarratedTitle() {
      return ScreenTexts.joinSentences(this.getTitle(), this.narrateLabels());
   }

   public void render(DrawContext context, int mouseX, int mouseY, float deltaTicks) {
      super.render(context, mouseX, mouseY, deltaTicks);
      context.drawCenteredTextWithShadow(this.textRenderer, (Text)this.title, this.width / 2, 17, -1);
      context.drawTextWithShadow(this.textRenderer, (Text)EDIT_SLOT_NAME, this.column1_x + this.column2_x / 2 - this.textRenderer.getWidth((StringVisitable)EDIT_SLOT_NAME) / 2, row(0) - 5, -1);
      this.nameEdit.render(context, mouseX, mouseY, deltaTicks);
   }

   private void setSlotName(String slotName) {
      if (slotName.equals(this.defaultSlotName)) {
         this.slotName = "";
      } else {
         this.slotName = slotName;
      }

   }

   private void saveSettings() {
      int i = indexOf(DIFFICULTIES, this.difficulty, 2);
      int j = indexOf(GAME_MODES, this.gameMode, 0);
      if (this.worldType != RealmsServer.WorldType.ADVENTUREMAP && this.worldType != RealmsServer.WorldType.EXPERIENCE && this.worldType != RealmsServer.WorldType.INSPIRATION) {
         boolean bl = this.worldType == RealmsServer.WorldType.NORMAL && this.difficulty != Difficulty.PEACEFUL && this.spawnMonsters;
         this.parent.saveSlotSettings(new RealmsSlot(this.slot.slotId, new RealmsWorldOptions(this.pvp, bl, this.spawnProtection, this.commandBlocks, i, j, this.forceGameMode, this.slotName, this.slot.options.version, this.slot.options.compatibility), this.slot.settings));
      } else {
         this.parent.saveSlotSettings(new RealmsSlot(this.slot.slotId, new RealmsWorldOptions(this.slot.options.pvp, this.slot.options.spawnMonsters, this.slot.options.spawnProtection, this.slot.options.commandBlocks, i, j, this.slot.options.forceGameMode, this.slotName, this.slot.options.version, this.slot.options.compatibility), this.slot.settings));
      }

   }

   static {
      DIFFICULTIES = ImmutableList.of(Difficulty.PEACEFUL, Difficulty.EASY, Difficulty.NORMAL, Difficulty.HARD);
      GAME_MODES = ImmutableList.of(GameMode.SURVIVAL, GameMode.CREATIVE, GameMode.ADVENTURE);
      EDIT_SLOT_NAME = Text.translatable("mco.configure.world.edit.slot.name");
      SPAWN_PROTECTION = Text.translatable("mco.configure.world.spawnProtection");
   }

   @Environment(EnvType.CLIENT)
   class SettingsSlider extends SliderWidget {
      private final double min;
      private final double max;

      public SettingsSlider(final int x, final int y, final int width, final int value, final float min, final float max) {
         super(x, y, width, 20, ScreenTexts.EMPTY, (double)0.0F);
         this.min = (double)min;
         this.max = (double)max;
         this.value = (double)((MathHelper.clamp((float)value, min, max) - min) / (max - min));
         this.updateMessage();
      }

      public void applyValue() {
         if (RealmsSlotOptionsScreen.this.spawnProtectionButton.active) {
            RealmsSlotOptionsScreen.this.spawnProtection = (int)MathHelper.lerp(MathHelper.clamp(this.value, (double)0.0F, (double)1.0F), this.min, this.max);
         }
      }

      protected void updateMessage() {
         this.setMessage(ScreenTexts.composeGenericOptionText(RealmsSlotOptionsScreen.SPAWN_PROTECTION, (Text)(RealmsSlotOptionsScreen.this.spawnProtection == 0 ? ScreenTexts.OFF : Text.literal(String.valueOf(RealmsSlotOptionsScreen.this.spawnProtection)))));
      }
   }
}
