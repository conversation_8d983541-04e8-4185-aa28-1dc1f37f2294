[{"section": "Mojang Studios", "disciplines": [{"discipline": "Mojang Studios Leadership", "titles": [{"title": "Studio Head", "names": ["<PERSON><PERSON>"]}, {"title": "Chief Operating Officer", "names": ["<PERSON>"]}, {"title": "Head of Brand, Growth & Partnerships", "names": ["<PERSON>"]}, {"title": "Head of Minecraft Game Experience", "names": ["<PERSON>"]}, {"title": "Chief of Staff", "names": ["<PERSON>"]}, {"title": "Chief Creative Officer", "names": ["<PERSON><PERSON>"]}, {"title": "Head of Franchise Product Strategy", "names": ["<PERSON>"]}, {"title": "Head of People Operations", "names": ["<PERSON>"]}]}, {"discipline": "Design", "titles": [{"title": "Game Director, Minecraft", "names": ["<PERSON>"]}, {"title": "Creative Director, Brand", "names": ["<PERSON>"]}, {"title": "Creative Director", "names": ["<PERSON>"]}, {"title": "Creative Directors, Games", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Creative Director, Entertainment", "names": ["<PERSON><PERSON>"]}, {"title": "Creative Managers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Design Directors", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Design Managers", "names": ["Art Usher", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Lead Game Designers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Game Designers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "Justice Mealer (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Tate Sliwa (Insight Global, Inc)", "<PERSON><PERSON>"]}, {"title": "Narrative Director", "names": ["<PERSON>"]}, {"title": "Narrative Director, Entertainment", "names": ["<PERSON>"]}, {"title": "User Experience Design Directors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "User Experience Design Leads", "names": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "User Experience Designers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}]}, {"discipline": "Programming", "titles": [{"title": "Technical Directors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Engineering Director", "names": ["<PERSON>"]}, {"title": "Engineering Director, Franchise Technologies and Services", "names": ["<PERSON>"]}, {"title": "Engineering Director, Growth Products", "names": ["<PERSON>"]}, {"title": "Engineering Director, Internal New Games", "names": ["Aisling Canton"]}, {"title": "Engineering Directors, Bedrock Platform", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Engineering Manager, Franchise Services", "names": ["<PERSON>"]}, {"title": "Engineering Managers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Ascendion, Inc)"]}, {"title": "Lead Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "River Gillis", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Technical Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Software Engineers", "names": ["A.J. <PERSON>", "<PERSON><PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Globant)", "<PERSON><PERSON>", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Artech Consulting, LLC)", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON> (Netlight)", "<PERSON><PERSON><PERSON> (Globant)", "Alvee Akash (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>  (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Netlight)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Globant)", "<PERSON>", "<PERSON>", "<PERSON><PERSON> \"<PERSON>\" <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Artech Consulting, LLC)", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> (Netlight)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Artech Consulting, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Mirado Consulting)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Netlight)", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "Nico Su<PERSON>", "<PERSON> (Globant)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Netlight)", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Parikh (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Netlight)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "Rodel Dela Cruz (Ascendion, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Soundar B (Ascendion, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>ran<PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> Kumar S <PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Zawar Alam (Ascendion, Inc)"]}]}, {"discipline": "Production", "titles": [{"title": "Head of Franchise Technologies and Services", "names": ["<PERSON><PERSON>"]}, {"title": "Executive Producer, Education", "names": ["<PERSON>"]}, {"title": "Executive Producers", "names": ["<PERSON>", "<PERSON><PERSON>", "Hai Shi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Director of Bedrock Platform", "names": ["<PERSON>"]}, {"title": "Director of Creator", "names": ["<PERSON><PERSON>"]}, {"title": "Director of Internal New Games", "names": ["<PERSON>"]}, {"title": "Director of Minecraft Launcher", "names": ["<PERSON>"]}, {"title": "Director of Minecraft Online & Marketplace", "names": ["<PERSON>"]}, {"title": "Director of Minecraft Websites", "names": ["<PERSON>"]}, {"title": "Director of Publishing and Licensing", "names": ["<PERSON>"]}, {"title": "Director of Trust and Safety", "names": ["<PERSON>"]}, {"title": "Production Directors", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Production Managers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON>"]}, {"title": "Production Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Principal Producers", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Producers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Simplicity Consulting Inc.)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ra<PERSON><PERSON> Joy", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Media Producers", "names": ["<PERSON>", "<PERSON> Kvarnemo", "<PERSON>"]}, {"title": "Product Directors, Franchise Technologies and Services", "names": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Product Manager Leads", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Product Managers", "names": ["<PERSON>", "August Carow", "<PERSON>", "<PERSON> B<PERSON>rg (Netlight)", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON>", "Esteban Balbuena (Globant)", "<PERSON> (Allegis Group Services, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (MVP Global AB)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Organizational Coach", "names": ["<PERSON><PERSON> (Jenuine Coaching & Consulting AB)"]}, {"title": "Release Management Lead", "names": ["<PERSON>"]}, {"title": "Release Managers", "names": ["<PERSON>", "<PERSON> Dice (Hanson Consulting Group, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Hanson Consulting Group, Inc)"]}, {"title": "Technical Writers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)"]}, {"title": "Technical Program Manager Lead", "names": ["<PERSON>"]}, {"title": "Technical Program Managers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON> (my3Twelve, LLC)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON> (my3Twelve, LLC)", "<PERSON>", "<PERSON> (my3Twelve, LLC)", "<PERSON> (Harvey Nash, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (my3Twelve, LLC)", "<PERSON>", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (my3Twelve, LLC)", "<PERSON> (Aquent, LLC)", "<PERSON> (Ascendion, Inc)", "<PERSON> (Apex Systems, Inc)"]}, {"title": "Playtest Coordinator", "names": ["<PERSON> (Insight Global, Inc)"]}]}, {"discipline": "Visual Arts", "titles": [{"title": "Creative Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>-He"]}, {"title": "Art Directors", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Art Manager", "names": ["<PERSON>"]}, {"title": "Artist Leads", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Michael <PERSON>", "<PERSON>"]}, {"title": "Artists", "names": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Ten Gun Design, Inc)", "<PERSON><PERSON> (Harvey Nash, Inc)", "Chase Farthing (Allegis Group Services, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Aquent, LLC)", "<PERSON>", "<PERSON> (Harvey Nash, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Allegis Group Services, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Allegis Group Services, Inc)", "<PERSON> (Harvey Nash, Inc)"]}, {"title": "Technical Artists", "names": ["<PERSON> '<PERSON><PERSON>' <PERSON>", "<PERSON>"]}, {"title": "Product Design Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Product Designers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Ro Ocampo (Allegis Group Services, Inc)", "<PERSON> (Allegis Group Services, Inc)"]}, {"title": "Graphic Designers", "names": ["<PERSON><PERSON><PERSON> (Ten Gun Design, Inc)", "<PERSON><PERSON>"]}]}, {"discipline": "Audio", "titles": [{"title": "Audio Directors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Sound Designers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Music composed by", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Music Supervisors", "names": ["<PERSON>", "<PERSON>"]}]}, {"discipline": "Quality Assessment", "titles": [{"title": "Quality Director, Franchise Technologies and Services", "names": ["<PERSON>"]}, {"title": "Quality Manager", "names": ["<PERSON>"]}, {"title": "Quality Leads", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Quality Engineers", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Quality Assessment Specialists", "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Program Managers", "names": ["<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON>"]}, {"title": "Test Director", "names": ["<PERSON> (Experis)"]}, {"title": "Test Managers", "names": ["<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON> (Experis)"]}, {"title": "Team Leads", "names": ["<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Test Leads", "names": ["<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "Santiago Quinio (Experis)"]}, {"title": "Test Automation Engineers", "names": ["<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Software Test Engineers", "names": ["<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> '<PERSON>' <PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON>-<PERSON><PERSON><PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Test Associates", "names": ["<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "Aleksander <PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "Aleksander <PERSON>ygie<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> W<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "Burton Groves (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> Spence (Experis)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON>bina <PERSON>amon (Lionbridge)", "<PERSON><PERSON>a <PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON>-<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "Wiktoria Brodzik (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON>ik<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}]}, {"discipline": "User Research", "titles": [{"title": "User Research Lead", "names": ["<PERSON><PERSON>"]}, {"title": "User Researcher", "names": ["<PERSON>"]}]}, {"discipline": "Operations", "titles": [{"title": "Operations Director, Global", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Operations Director", "names": ["<PERSON><PERSON>"]}, {"title": "Operations Managers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)"]}, {"title": "Operations Coordinator", "names": ["<PERSON><PERSON>"]}, {"title": "Creative Operations Director", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Creative Operations Manager", "names": ["<PERSON>"]}, {"title": "People Operations Managers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Pettway", "<PERSON>", "Georgia Marra", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "HR Directors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Human Resources", "names": ["Line Thomson", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Talent Acquisition", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>"]}, {"title": "Executive Business Administrators", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "IT Managers", "names": ["<PERSON>", "<PERSON>"]}, {"title": "IT", "names": ["<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON> (Techfactory, AB)", "<PERSON> (My3Twelve, LLC)", "<PERSON>-<PERSON><PERSON> (Techfactory, AB)", "<PERSON> (my3Twelve, LLC)", "Shoghi Cervantes Pueyo", "<PERSON> (my3Twelve, LLC)", "<PERSON><PERSON><PERSON> (Academic Work IT AB)"]}, {"title": "Lead Automation Support", "names": ["<PERSON>"]}, {"title": "DevOps Engineer", "names": ["Jordan Crockett (Allegis Group Services, Inc)"]}]}, {"discipline": "Player Care", "titles": [{"title": "Director of Player Care", "names": ["<PERSON>"]}, {"title": "Player Support Program Leads", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Player Support Operations Manager", "names": ["<PERSON> (Apex Systems, Inc)"]}, {"title": "Player Support", "names": ["<PERSON> (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON><PERSON> (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)", "Chirs Lok (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON><PERSON><PERSON> (Apex Systems, Inc)", "<PERSON>ro<PERSON>y (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON><PERSON> (Apex Systems, Inc)", "<PERSON><PERSON>ar<PERSON> (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON>or (Apex Systems, Inc)"]}]}, {"discipline": "Data Analytics", "titles": [{"title": "Player Analytics and Insights Director", "names": ["<PERSON>"]}, {"title": "Data Science and Analytics Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Data Engineering Manager", "names": ["<PERSON>"]}, {"title": "Data and Analytics Lead", "names": ["<PERSON><PERSON>"]}, {"title": "Analytics Environment Manager", "names": ["<PERSON>"]}, {"title": "Analytics Environment Engineering", "names": ["<PERSON> (Ascendion, Inc)", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Fractal Analytics Inc)"]}, {"title": "Data Science", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Kent Go (Aquent, LLC)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Siva Balantrapu (Hitachi Digital Services LLC)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (KellyMitchell Group, LLC)", "Usama Bin Salim (Agility Partners  LLC)", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON>"]}, {"title": "Data Engineering", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}]}, {"discipline": "Business Management & Licensing", "titles": [{"title": "Program Director, China Consumer & Global Partnerships", "names": ["<PERSON>"]}, {"title": "Program Director, Consumer Products", "names": ["<PERSON>"]}, {"title": "Program Director, Franchise Development", "names": ["Federico <PERSON>"]}, {"title": "Program Directors", "names": ["<PERSON> Vol<PERSON>es", "<PERSON>", "<PERSON>", "Gaylon Blank", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Business Director, Franchise", "names": ["<PERSON>"]}, {"title": "Business Director, Japan & Market Expansion", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Business Directors, Operations", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Business Directors", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Business Development Manager Leads", "names": ["<PERSON><PERSON> Chamberlain", "<PERSON>"]}, {"title": "Business Development Managers", "names": ["<PERSON>", "<PERSON> (Iconma LLC)", "<PERSON>", "<PERSON><PERSON><PERSON> Mullen (Allegis Global Services, Inc)", "<PERSON> (Allegis Global Services, Inc)", "<PERSON> (Ascendion, Inc)"]}, {"title": "Program Manager Lead", "names": ["<PERSON>"]}, {"title": "Program Managers", "names": ["<PERSON>", "<PERSON> (Excelsior Staffing LLC)", "Bethany Gager (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON> (Insight Global, Inc)"]}, {"title": "Business Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON> (Allegis Group Services, Inc)", "<PERSON>", "<PERSON> (Aquent, LLC)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (SGF USA LLC)", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Simplicity Consulting Inc.)"]}, {"title": "Technical Program Managers", "names": ["<PERSON> <PERSON>", "<PERSON>"]}]}, {"discipline": "Brand Management", "titles": [{"title": "Brand Director", "names": ["<PERSON>"]}, {"title": "Brand Manager", "names": ["<PERSON>"]}, {"title": "Brand Strategist", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Brand Analyst", "names": ["Sofia Alm"]}]}, {"discipline": "Communications", "titles": [{"title": "Media Director", "names": ["<PERSON><PERSON>"]}, {"title": "Director of Communications", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Communications Managers", "names": ["<PERSON><PERSON>", "<PERSON> (Assembly Media, Inc)", "<PERSON>", "<PERSON> (Assembly Media, Inc)", "<PERSON>", "<PERSON><PERSON> (Assembly Media, Inc)", "<PERSON> (Assembly Media, Inc)", "Petra Tell", "<PERSON><PERSON> (Assembly Media, Inc)", "<PERSON> (Assembly Media, Inc)", "<PERSON><PERSON><PERSON>"]}, {"title": "Creative Writers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Director of Community", "names": ["<PERSON><PERSON>"]}, {"title": "Social Media Lead", "names": ["<PERSON>"]}, {"title": "Social Media Managers", "names": ["<PERSON> (Kforce, Inc)", "<PERSON>", "<PERSON> (Cypress Human Capital)", "<PERSON><PERSON>-<PERSON> (Kforce, Inc)", "<PERSON> (Harvey Nash, Inc)", "<PERSON> (Randstad)"]}, {"title": "Community Management Lead", "names": ["<PERSON>"]}, {"title": "Community Managers", "names": ["<PERSON> (Averro LLC)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Content Managers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Apex Systems, Inc)"]}, {"title": "Publishing Editor", "names": ["<PERSON>"]}]}, {"discipline": "Marketing", "titles": [{"title": "Head of Marketing", "names": ["<PERSON><PERSON>"]}, {"title": "Marketing Directors", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Marketing Managers", "names": ["<PERSON>", "<PERSON> (Cypress Human Capital)", "<PERSON>", "Arkadiy Goldenshtern", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Unfollow Media)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Ten Gun Design, Inc)", "<PERSON> (Ten Gun Design, Inc)", "<PERSON> (Synaxis Corporation)", "<PERSON><PERSON><PERSON> (Harvey Nash, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Nextant LLC)", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Aquent, LLC)", "<PERSON> (Ten Gun Design, Inc)"]}, {"title": "Marketing Content Managers", "names": ["<PERSON><PERSON><PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Tata Consultancy Services LTD)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Tata Consultancy Services LTD)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Tata Consultancy Services LTD)", "<PERSON> (Tata Consultancy Services LTD)"]}, {"title": "Web Content Manager", "names": ["<PERSON> (Ten Gun Design, Inc)"]}, {"title": "Web Content Authors & QA", "names": ["<PERSON><PERSON><PERSON><PERSON> (HCL Technologies)", "<PERSON> (HCL Technologies)", "<PERSON><PERSON> (HCL Technologies)", "<PERSON> (Ten Gun Design, Inc)"]}, {"title": "Project Manager", "names": ["<PERSON> (Ten Gun Design, Inc)"]}]}, {"discipline": "Legal", "titles": [{"title": "Head of Legal", "names": ["<PERSON> (CELA)"]}, {"title": "Senior Legal Counsel", "names": ["<PERSON> (CELA)"]}, {"title": "Legal Counsel", "names": ["<PERSON>", "<PERSON><PERSON> (CELA)", "<PERSON><PERSON> (Snodgrass Annand)", "<PERSON> (<PERSON>)"]}]}, {"discipline": "Finance", "titles": [{"title": "Finance Director", "names": ["<PERSON>"]}, {"title": "Finance Managers", "names": ["<PERSON> (Apex Systems, Inc)", "<PERSON>.", "<PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Financial Accountant", "names": ["<PERSON><PERSON>"]}, {"title": "Financial Consultants", "names": ["<PERSON>", "<PERSON>"]}]}]}, {"section": "Special Thanks", "disciplines": [{"discipline": "", "titles": [{"title": "", "names": ["4J Studios", "Accenture, Web Engineering Services", "<PERSON>, <PERSON><PERSON><PERSON>", "<PERSON>, Zen3 Infosolutions America, Inc", "<PERSON><PERSON>, Creator of Blockbench", "<PERSON><PERSON><PERSON><PERSON><PERSON>' <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "<PERSON>, Writer", "<PERSON>, <PERSON><PERSON><PERSON>", "<PERSON> \"skypjack\" <PERSON><PERSON>, Senior Software Engineer", "Microsoft Gaming Safety Team", "Microsoft Magic Team", "<PERSON><PERSON>, Developer Account Manager", "The PlayFab Team", "The Xbox Live Team", "<PERSON>, Zen3 Infosolutions America, Inc", "Xbox Games Studios"]}]}]}, {"section": "Development Partner - Blackbird Interactive", "disciplines": [{"discipline": "", "titles": [{"title": "Programmers", "names": ["<PERSON>", "Devon Plourde", "<PERSON><PERSON> F<PERSON>les", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Producer", "names": ["<PERSON>"]}, {"title": "Vice President, Development", "names": ["<PERSON>"]}, {"title": "Studio Technical Director", "names": ["<PERSON>"]}]}]}, {"section": "Development Partner - Disbelief", "disciplines": [{"discipline": "", "titles": [{"title": "President", "names": ["<PERSON>"]}, {"title": "CTO", "names": ["<PERSON>"]}, {"title": "Producer", "names": ["<PERSON>"]}, {"title": "Additional Production", "names": ["<PERSON>"]}, {"title": "Technical Director", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Lead Programmer", "names": ["<PERSON>"]}, {"title": "Senior Programmers", "names": ["<PERSON>", "<PERSON><PERSON>"]}, {"title": "Programmers", "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Art Director", "names": ["<PERSON>"]}, {"title": "Senior Technical Artist", "names": ["<PERSON>"]}, {"title": "Technical Artist", "names": ["<PERSON><PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Red Lens", "disciplines": [{"discipline": "", "titles": [{"title": "President", "names": ["<PERSON>"]}, {"title": "<PERSON>", "names": ["Veasna Chhaysy-Park"]}, {"title": "Tech Lead", "names": ["<PERSON>"]}, {"title": "Producer", "names": ["<PERSON>"]}, {"title": "Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Skybox", "disciplines": [{"discipline": "", "titles": [{"title": "Founders", "names": ["<PERSON>", "Shyang Kong", "<PERSON>"]}, {"title": "Production", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Phoenix Valencia", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Quality Assurance", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Software Developers", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Eser Kokturk", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>o T<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-Drevillon", "<PERSON>g<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Sprung Studios Ltd", "disciplines": [{"discipline": "", "titles": [{"title": "CEO", "names": ["<PERSON>"]}, {"title": "Director of Global Business Development", "names": ["<PERSON>"]}, {"title": "Studio Director", "names": ["<PERSON>"]}, {"title": "Senior Producer", "names": ["Amelia Wales"]}, {"title": "Producer", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Head of UX/UI", "names": ["<PERSON>"]}, {"title": "Senior UX/UI Designer", "names": ["<PERSON><PERSON>"]}, {"title": "UX/UI Designers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Head of Engineering", "names": ["<PERSON>"]}, {"title": "Senior UI Engineer", "names": ["<PERSON>"]}, {"title": "Front-End Developers", "names": ["<PERSON>", "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"title": "UI Engineers", "names": ["<PERSON>", "Michael <PERSON>", "<PERSON><PERSON>"]}, {"title": "Head of User Research", "names": ["<PERSON>"]}, {"title": "Senior User Researcher", "names": ["<PERSON>"]}, {"title": "IT Support", "names": ["<PERSON><PERSON><PERSON>"]}]}]}, {"section": "Mojang Studios Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Original Creator of Minecraft", "names": ["<PERSON>"]}, {"title": "Studio Head", "names": ["<PERSON><PERSON>", "<PERSON>"]}, {"title": "Head of Minecraft", "names": ["<PERSON>"]}, {"title": "Head of Stockholm Studio", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Chief Executive Officer", "names": ["<PERSON>"]}, {"title": "Chief Technology Officer", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Chief of Staff", "names": ["<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Chief Creative Officer", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Head of Franchise Operations", "names": ["<PERSON>"]}, {"title": "Head of Games", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Game Director", "names": ["<PERSON>"]}, {"title": "Creative Director", "names": ["<PERSON>"]}, {"title": "Creative Leads", "names": ["<PERSON>", "<PERSON> (Formosa)", "<PERSON>"]}, {"title": "Design Managers", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Game Designers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (TEKsystems, Inc.)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (TEKsystems, Inc.)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Narrative Designer", "names": ["<PERSON>"]}, {"title": "Head of Creator Platform Engineering", "names": ["<PERSON>"]}, {"title": "Franchise Technical Director", "names": ["<PERSON>"]}, {"title": "Technical Directors", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Engineering Managers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Lead Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Syrgak <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Technical Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Software Engineers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (TEKsystems, Inc)", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> K <PERSON>o", "<PERSON><PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON>wami (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> (Artech Consulting, LLC)", "<PERSON><PERSON> (Ascendion Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Coseo Frerichs (Insight Global, Inc)", "<PERSON>", "<PERSON>", "Dag Oldenburg", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> '<PERSON>' <PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON> (Insight Global, Inc)", "Delia <PERSON> (Globant)", "<PERSON> (CSI Interfusion, Inc)", "Dodge Lafnitzegger (Insight Global, Inc)", "<PERSON>", "Don <PERSON> II", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Insight Global, Inc)", "<PERSON> (Ascendion, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON> (Globant)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Gage Way (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> He<PERSON>jkenskjöld", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Globant)", "<PERSON> (Insight Global, Inc)", "<PERSON> '<PERSON><PERSON>' <PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Agility Partners, LLC)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON> (Randstad)", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Design Laboratory, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON> (Randstad)", "<PERSON> (Insight Global Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Kalvi Consulting Services, Inc)", "Louis <PERSON>da (Insight Global, Inc)", "<PERSON> (Collabera, LLC)", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (WaferWire Cloud Technologies)", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "Maxxwell Plum (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> '<PERSON><PERSON><PERSON>' <PERSON>", "<PERSON>", "<PERSON> (Globant)", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (Collabera, LLC)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> M Riviera", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (TEKsystems, Inc.)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)"]}, {"title": "Lead Architects", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Architects", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Launcher", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Additional Programming", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Chief Product Officer", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Head of Games Expansion", "names": ["<PERSON>"]}, {"title": "Head of Minecraft Atlas", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Production Directors", "names": ["<PERSON>", "<PERSON> (Formosa)"]}, {"title": "Director of Minecraft Online & Marketplace", "names": ["<PERSON>"]}, {"title": "Director of Bedrock Platform", "names": ["<PERSON>"]}, {"title": "Executive Producers", "names": ["<PERSON>", "Gama Aguilar-Gamez", "<PERSON>"]}, {"title": "Production Managers", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Production Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Producers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Best Liang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Randstad)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Randstad)", "<PERSON><PERSON> (Aquent, LLC)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Formosa)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (Globant)", "Isabella <PERSON>", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>, Ifeguni", "<PERSON><PERSON> (TEKsystems, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Randstad)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (Design Laboratory, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Loudon St Hill (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (eXcell, a division of CompuCom)", "<PERSON> (Formosa)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (Randstad)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Sofia Orrheim", "<PERSON><PERSON>", "<PERSON><PERSON> (Allegis Group Services, Inc)", "<PERSON> (Collabera, LLC)", "<PERSON><PERSON>", "<PERSON> (Formosa)", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> (TEKsystems, Inc.)", "<PERSON><PERSON>", "<PERSON> (Yoh Services LLC)", "<PERSON> (Pivotal Consulting, LLC)", "<PERSON>", "Yesenia Cisneros"]}, {"title": "Assistant Producer", "names": ["<PERSON>"]}, {"title": "Media Producers", "names": ["<PERSON>", "<PERSON><PERSON>"]}, {"title": "Product Managers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON><PERSON><PERSON>"]}, {"title": "Organizational Coaches", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Release Managers", "names": ["<PERSON> (Kforce, Inc)", "<PERSON> (Design Laboratory, Inc)", "<PERSON> (Randstad)", "<PERSON> (Apex Systems, Inc)"]}, {"title": "Technical Writers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (TEKsystems, Inc.)"]}, {"title": "Technical Program Managers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON> (Cyborg Mobile LLC)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "Morgan J. East (Randstad)", "<PERSON> (Insight Global, Inc)", "<PERSON> (TEKsystems, Inc.)", "<PERSON> (Wimmer Solutions)"]}, {"title": "Localization", "names": ["<PERSON> (Shanghai Wicresoft Co, Ltd.)"]}, {"title": "Playtest Coordinators", "names": ["<PERSON>", "<PERSON> (Aquent, LLC)", "<PERSON> (Randstad)"]}, {"title": "Art Directors", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Formosa)", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Artist Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Artists", "names": ["<PERSON><PERSON><PERSON> (Formosa)", "<PERSON> (Harvey Nash, Inc)", "<PERSON> (Formosa)", "<PERSON> (Randstad)", "<PERSON><PERSON> (Aquent, LLC)", "<PERSON> (Formosa)", "<PERSON> (Collabera, LLC)", "<PERSON><PERSON>", "<PERSON> (Formosa)", "<PERSON> (eXcell, a division of CompuCom)", "<PERSON><PERSON><PERSON>", "<PERSON> Night (Aquent, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jei G Ling (Allegis Group Services, Inc)", "<PERSON><PERSON> (Formosa)", "<PERSON><PERSON> (TEKsystems, Inc.)", "<PERSON><PERSON>", "Jonatan Pöljö", "<PERSON> (Aquent, LLC)", "<PERSON><PERSON> (Formosa)", "<PERSON> (Formosa)", "<PERSON> (CompuCom Systems, Inc)", "<PERSON> (Randstad)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Formosa)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Formosa)", "<PERSON> (Apex Systems, Inc)", "<PERSON> (Formosa)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Randstad)", "<PERSON> (Formosa)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (Formosa)", "<PERSON><PERSON><PERSON> (Formosa)", "<PERSON> (Formosa)", "<PERSON> (Formosa)", "<PERSON>"]}, {"title": "Product Designers", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Graphic Designers", "names": ["<PERSON> (Formosa)", "<PERSON><PERSON> (Formosa)", "<PERSON> (Formosa)", "<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Motion Graphics Designer", "names": ["<PERSON> (Randstad)"]}, {"title": "Sound Designers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Insight Global, Inc)"]}, {"title": "Technical Audio Developer", "names": ["Jonatan Crafoord"]}, {"title": "Quality Engineers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON> (Kforce, Inc)", "<PERSON><PERSON><PERSON>"]}, {"title": "Quality Data Analysis & Engineering", "names": ["<PERSON>"]}, {"title": "Quality Assessment Specialists", "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Software Test Engineers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Experis)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON> (Experis)"]}, {"title": "Test Associates", "names": ["<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "NIck Latino (Insight Global, Inc)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Experis)"]}, {"title": "Test Managers", "names": ["<PERSON> (Experis)", "<PERSON> (Experis)"]}, {"title": "Team Leads", "names": ["<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Test Lead", "names": ["<PERSON> (Insight Global, Inc)"]}, {"title": "User Experience Design Directors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "User Experience Designers", "names": ["<PERSON><PERSON><PERSON> (Formosa)", "<PERSON> (CompuCom Systems, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Li (TEKsystems, Inc.)", "<PERSON>", "<PERSON>", "<PERSON> (Prieto)", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> (Randstad)", "<PERSON> (Aquent, LLC)", "<PERSON>", "<PERSON> (Digital Intelligence Systems, LLC)", "<PERSON>", "<PERSON>"]}, {"title": "User Experience Writer", "names": ["<PERSON>"]}, {"title": "User Experience Intern", "names": ["<PERSON>"]}, {"title": "User Research Lead", "names": ["<PERSON>"]}, {"title": "User Researchers", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Head of Creator Marketplace", "names": ["<PERSON>"]}, {"title": "Business Director, Minecraft Game", "names": ["<PERSON><PERSON>"]}, {"title": "Directors of Business Management", "names": ["<PERSON>"]}, {"title": "Director of Business Planning", "names": ["<PERSON>"]}, {"title": "Director of Business Development", "names": ["<PERSON>erie <PERSON>"]}, {"title": "Project Director", "names": ["<PERSON>"]}, {"title": "Operations Managers", "names": ["<PERSON>", "<PERSON> (Lions and Tigers)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Mira <PERSON>", "<PERSON>"]}, {"title": "Business Development Managers", "names": ["<PERSON> (JeffreyM Consulting, LLC)", "<PERSON> Comar (Digital Intelligence Systems, LLC)", "<PERSON><PERSON>", "<PERSON> (Digital Intelligence Systems, LLC)", "<PERSON> (Aerotek, Inc)"]}, {"title": "Program Managers", "names": ["Amador Abreu (Insight Global, Inc)", "<PERSON>", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON>", "<PERSON> (Experis)", "<PERSON> (my3Twelve, LLC)", "<PERSON>", "<PERSON><PERSON> (Populus Group, LLC)", "<PERSON> (Bluehawk LLC)", "<PERSON>", "<PERSON> (Aerotek, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON> (my3Twelve, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON> (Simplicity Consulting Inc.)", "<PERSON> (Digital Intelligence Systems, LLC)", "<PERSON> (Simplicity Consulting Inc.)", "Stuart U (my3Twelve, LLC)", "<PERSON> (Amaxra)", "<PERSON> (Simplicity Consulting Inc.)", "<PERSON> (TEKsystems, Inc)", "Tori <PERSON> (Cypress Human Capital)", "<PERSON>"]}, {"title": "Business Managers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON> (Simplicity Consulting Inc.)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Aston Carter, Inc)", "<PERSON>", "<PERSON> (Rylem)", "<PERSON> (Epitec Inc)", "<PERSON> (Simplicity Consulting Inc.)"]}, {"title": "Business Analysts", "names": ["<PERSON> (Populus Group, LLC)", "<PERSON><PERSON> (Apex Systems, Inc)", "<PERSON> (Populus Group, LLC)"]}, {"title": "Lead Project Manager", "names": ["<PERSON>"]}, {"title": "Intellectual Property Enforcement Leads", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Intellectual Property Enforcement Agents", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "People Operations Director", "names": ["<PERSON><PERSON>"]}, {"title": "People Experience Manager", "names": ["<PERSON>"]}, {"title": "HR Directors", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Human Resources", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Talent Acquisition", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Office Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Office Coordinators", "names": ["<PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Executive Business Administrators", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Business Administrator", "names": ["<PERSON><PERSON><PERSON> (C2S Technologies, Inc)"]}, {"title": "Administrative Support", "names": ["<PERSON> (Experis)", "<PERSON> (Experis)"]}, {"title": "Front of House", "names": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "IT Managers", "names": ["<PERSON>"]}, {"title": "IT", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (my3Twelve, LLC)", "<PERSON><PERSON> (Centric Professionals AB)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Automation Support", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (WaferWire Cloud Technologies)", "<PERSON> (Collabera, LLC)", "<PERSON> (Digital Intelligence Systems, LLC)", "<PERSON> (Insight Global, Inc)"]}, {"title": "DevOps Engineer", "names": ["<PERSON> (Collabera, LLC)"]}, {"title": "Data Engineering", "names": ["<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON>"]}, {"title": "Data and Analytics Lead", "names": ["<PERSON>"]}, {"title": "Analytics Environment Engineering", "names": ["<PERSON><PERSON><PERSON> (Manpower Services Canada Limit)", "<PERSON><PERSON> (Ascendion, Inc)", "Vini De Lima De Sousa (0965688 BC Ltd)"]}, {"title": "Data Science", "names": ["<PERSON> (National Business Innovations)", "<PERSON><PERSON><PERSON><PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Design Laboratory, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Design Laboratory, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON> (Allegis Group Services, Inc)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON> (Kelly Management Services, Inc)", "<PERSON><PERSON> (Allegis Global Solutions)", "<PERSON><PERSON><PERSON> (Agility Partners, LLC)", "<PERSON>", "<PERSON>", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON> (Design Laboratory, Inc)", "<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON>", "<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON><PERSON><PERSON><PERSON> Deshpande (KellyMitchell Group, LLC)", "<PERSON> (Simplicity Consulting Inc.)", "Tong Shen (KellyMitchell Group, LLC)", "<PERSON><PERSON><PERSON> (Design Laboratory, Inc)"]}, {"title": "Head of Player Operations", "names": ["<PERSON>"]}, {"title": "Player Support Leads", "names": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Player Support Operations Manager", "names": ["<PERSON>"]}, {"title": "Player Support", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "Angehlica Walling", "<PERSON><PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (TEKsystems, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (TEKsystems, Inc.)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (TEKsystems, Inc)", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Brand Director", "names": ["<PERSON>"]}, {"title": "Head of Creative Production", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Lead Producer - Brand Experience", "names": ["<PERSON><PERSON>"]}, {"title": "Media Director", "names": ["<PERSON>"]}, {"title": "Chief Storyteller", "names": ["<PERSON>"]}, {"title": "Head of Creative Communications", "names": ["<PERSON>"]}, {"title": "Director of Communications", "names": ["<PERSON>"]}, {"title": "Communications Managers", "names": ["<PERSON> (Assembly Media, Inc)", "<PERSON> (Simplicity Consulting Inc)", "<PERSON>", "<PERSON><PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Assembly Media, Inc)", "<PERSON>"]}, {"title": "Content Coordinators", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Communications Editors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Assembly Media, Inc", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Creative Writers", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Lead Web Developer", "names": ["<PERSON>"]}, {"title": "Web Designers", "names": ["<PERSON> (Ten Gun Design, Inc)", "<PERSON> (eXcell, a division of CompuCom)"]}, {"title": "Social Media Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Social Media Managers", "names": ["Alek<PERSON><PERSON> (Aston Carter Inc)", "<PERSON> (Adecco)", "<PERSON> (Troy Consulting LLC)", "<PERSON> (Collabera, LLC)", "<PERSON> (Aerotek, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Ranstad)", "<PERSON>"]}, {"title": "Community Managers", "names": ["<PERSON>", "Dè<PERSON><PERSON> Easter (Apex Systems, Inc)", "<PERSON> (Experis)", "<PERSON>", "<PERSON>", "Nadine Ebri (Apex Systems, Inc)", "<PERSON><PERSON> (Corestaff)"]}, {"title": "Content Manager", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Publishing Editor", "names": ["<PERSON>"]}, {"title": "Head of Marketing", "names": ["<PERSON>"]}, {"title": "Marketing Managers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON> (Simplicity Consulting Inc.)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Didac <PERSON>", "<PERSON>", "<PERSON><PERSON> (Simplicity Consulting Inc.)", "<PERSON>", "<PERSON> (Ascendion Inc)"]}, {"title": "Head of Legal", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Legal Counsel", "names": ["<PERSON><PERSON>"]}, {"title": "Finance Managers", "names": ["<PERSON>", "<PERSON><PERSON>"]}, {"title": "Financial Accountants", "names": ["<PERSON><PERSON>", "Camilla Brantefelt", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Financial Consultants", "names": ["<PERSON>", "<PERSON><PERSON><PERSON>"]}]}]}, {"section": "Studios Quality Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Quality Director", "names": ["<PERSON>"]}, {"title": "Quality Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Quality Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Quality Engineers", "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Quality Analyst", "names": ["<PERSON> (Kforce)"]}, {"title": "Program Managers", "names": ["<PERSON> (Pivotal Consulting)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Insight Global, Inc)"]}, {"title": "Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Data Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Team Leads", "names": ["<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Test Leads", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Experis)", "<PERSON> (Experis)"]}, {"title": "Software Test Engineers", "names": ["<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> Edwards (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Insight Global, Inc.)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Test Associates", "names": ["<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Experis)", "Aleksander <PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "Katarzyna <PERSON> (Lionbridge)", "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "Małgor<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "Šimon <PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Studios Quality Special Thanks", "names": ["<PERSON> – Data Science Manager", "<PERSON> – Outsourcing Manager", "<PERSON> – Client Account Director (Experis)", "<PERSON> – Business Manager", "<PERSON> – Director XGS Business Operations", "<PERSON> (Experis)", "<PERSON> – Quality Director, Studios Quality UK", "<PERSON> – Director of Quality, Studios Quality", "<PERSON> (Experis)", "<PERSON><PERSON> – Executive Business Administrator", "<PERSON> (Experis)", "<PERSON> – Software Engineering Manager", "<PERSON> – Center of Excellence (Experis)"]}]}]}, {"section": "Development Partner - Blackbird Interactive Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Technical Director", "names": ["<PERSON>"]}, {"title": "Programmers", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Lu"]}, {"title": "Producers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Designers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "UI Artist", "names": ["<PERSON><PERSON>"]}, {"title": "UX Designer", "names": ["<PERSON>"]}, {"title": "Quality Assurance Director", "names": ["<PERSON>"]}, {"title": "Lead Quality Assurance Analysts", "names": ["JP <PERSON>", "<PERSON>"]}, {"title": "Quality Assurance Analysts", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}]}]}, {"section": "Development Partner - CSI Interfusion Inc Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}]}]}, {"section": "Development Partner - Disbelief Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Producers", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Associate Producer", "names": ["<PERSON>"]}, {"title": "Senior Programmer", "names": ["<PERSON>"]}, {"title": "Programmers", "names": ["<PERSON>", "<PERSON>", "Kainin Tankersley", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Red Lens Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "GM & Development Director", "names": ["<PERSON>"]}, {"title": "<PERSON> Leads", "names": ["<PERSON><PERSON>", "<PERSON>"]}, {"title": "Tech Leads", "names": ["<PERSON>", "<PERSON> III", "<PERSON><PERSON> Lawson", "<PERSON>", "<PERSON>"]}, {"title": "Production Director", "names": ["<PERSON>"]}, {"title": "Producer", "names": ["<PERSON><PERSON>"]}, {"title": "Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}]}]}, {"section": "Development Partner - SiProgs Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Software Test Engineers", "names": ["Frantisek Beke", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Skybox Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Arta Seify", "Ashlyn Gadow", "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>z", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Thiago Braga", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Virtuosity Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Game Developers", "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Services Developers", "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Malliga Muthuraj", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Web Developers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nazia Nazia", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Producers", "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Sprung Studios Ltd Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Senior UX/UI Designer II", "names": ["<PERSON>"]}, {"title": "UI Engineer", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "IT Support", "names": ["<PERSON>"]}]}]}]