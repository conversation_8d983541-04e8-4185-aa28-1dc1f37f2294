[{"section": "Mojang Studios", "disciplines": [{"discipline": "Mojang Studios Leadership", "titles": [{"title": "Studio Head of Mojang Studios", "names": ["<PERSON><PERSON>"]}, {"title": "Chief Operating Officer", "names": ["<PERSON>"]}, {"title": "Head of Growth Products & Partnerships", "names": ["<PERSON>"]}, {"title": "Head of Minecraft Game Experience", "names": ["<PERSON>"]}, {"title": "Chief of Staff", "names": ["<PERSON>"]}, {"title": "Chief Creative Officer", "names": ["<PERSON><PERSON>"]}, {"title": "Head of Brand & Entertainment", "names": ["<PERSON><PERSON>"]}, {"title": "Head of Games Expansion", "names": ["<PERSON>"]}, {"title": "Franchise Strategic Advisor", "names": ["<PERSON>"]}]}, {"discipline": "Design", "titles": [{"title": "Game Director, Minecraft", "names": ["<PERSON>"]}, {"title": "Creative Director, Brand", "names": ["<PERSON>"]}, {"title": "Creative Director", "names": ["<PERSON>"]}, {"title": "Creative Directors, Games", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Creative Director, Original Content", "names": ["<PERSON><PERSON>"]}, {"title": "Creative Leads", "names": ["<PERSON> (Formosa)", "<PERSON>"]}, {"title": "Design Director", "names": ["<PERSON>"]}, {"title": "Design Managers", "names": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Lead Game Designers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Game Designers", "names": ["Art Usher", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "Justice Mealer (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Tate Sliwa (Insight Global, Inc)", "<PERSON><PERSON>"]}, {"title": "Narrative Director", "names": ["<PERSON>"]}, {"title": "Narrative Editor", "names": ["<PERSON>"]}, {"title": "User Experience Design Director", "names": ["<PERSON>"]}, {"title": "User Experience Design Leads", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "User Experience Designers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON><PERSON><PERSON> (Formosa)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>l Ocampo (Allegis Group Services, Inc)", "<PERSON> (Digital Intelligence Systems, LLC)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "User Experience Writer", "names": ["<PERSON>"]}]}, {"discipline": "Programming", "titles": [{"title": "Technical Directors", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Engineering Director, Bedrock Platform", "names": ["<PERSON>"]}, {"title": "Engineering Director, Growth Products", "names": ["<PERSON>"]}, {"title": "Engineering Director, Internal New Games", "names": ["Aisling Canton"]}, {"title": "Engineering Directors", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Engineering Manager, Franchise Services", "names": ["<PERSON>"]}, {"title": "Engineering Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Lead Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Technical Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Software Engineers", "names": ["A.J. <PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON> (TEKsystems, Inc)", "<PERSON>", "<PERSON> (Globant)", "<PERSON><PERSON>", "<PERSON><PERSON> (Ascendion  Inc)", "<PERSON>", "<PERSON> (Artech Consulting, LLC)", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Ascendion  Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (Murphy & Associates, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>  (Ascendion, Inc)", "<PERSON>", "<PERSON>", "Delia <PERSON> (Globant)", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Globant)", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON> (Globant)", "Francisco <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON> \"<PERSON>\" <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> (Artech Consulting, LLC)", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Globant)", "<PERSON> (Artech Consulting, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>  (Ascendion, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Globant)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON> (Globant)", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "Nico Su<PERSON>", "<PERSON> (Globant)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "River Gillis", "<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "Rodel Dela Cruz (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON> (Ascendion, Inc)", "Sarmis<PERSON><PERSON> (Globant)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Ascendion  Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Ascendion, Inc)", "<PERSON>ay Kumar S B (Ascendion  Inc)", "<PERSON><PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (Ascendion, Inc)"]}]}, {"discipline": "Production", "titles": [{"title": "Executive Producer, Education", "names": ["<PERSON>"]}, {"title": "Executive Producers", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Director of Bedrock Platform", "names": ["<PERSON>"]}, {"title": "Director of Creator", "names": ["<PERSON>"]}, {"title": "Director of Ecosystem Experiences", "names": ["<PERSON><PERSON>"]}, {"title": "Director of Internal New Games", "names": ["<PERSON>"]}, {"title": "Director of Minecraft Launcher", "names": ["<PERSON>"]}, {"title": "Director of Minecraft Online & Marketplace", "names": ["<PERSON>"]}, {"title": "Director of Minecraft Websites", "names": ["<PERSON>"]}, {"title": "Director of Publishing and Licensing", "names": ["<PERSON>"]}, {"title": "Production Directors", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Formosa)"]}, {"title": "Production Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Producers", "names": ["<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (Formosa)", "<PERSON> (Allegis Group Services, Inc)", "<PERSON> (Aquent, LLC)", "<PERSON> (Globant)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Formosa)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (TEKsystems, Inc)", "<PERSON> (Formosa)", "<PERSON> (Ascendion, Inc)"]}, {"title": "Media Producers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Product Manager Leads", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Product Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON> (Apex Systems, Inc)", "Esteban Balbuena (Globant)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Organizational Coaches", "names": ["<PERSON><PERSON>"]}, {"title": "Release Management Lead", "names": ["<PERSON>"]}, {"title": "Release Managers", "names": ["<PERSON>", "<PERSON> Dice (Hanson Consulting Group, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON> (Hanson Consulting Group, Inc)"]}, {"title": "Technical Writers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)"]}, {"title": "Technical Program Manager Lead", "names": ["<PERSON>"]}, {"title": "Technical Program Managers", "names": ["<PERSON><PERSON><PERSON> (Cyborg Mobile LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Harvey Nash, Inc)", "<PERSON> <PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hai Shi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Wimmer Solutions)", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (Apex Systems, Inc)"]}, {"title": "Localization", "names": ["<PERSON> (Shanghai Wicresoft Co, Ltd.)"]}, {"title": "Playtest Coordinator", "names": ["<PERSON>"]}]}, {"discipline": "Visual Arts", "titles": [{"title": "Creative Leads", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-He"]}, {"title": "Creative Manager", "names": ["<PERSON><PERSON>"]}, {"title": "Art Directors", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Formosa)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Artist Leads", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Michael <PERSON>", "<PERSON>"]}, {"title": "Artists", "names": ["<PERSON>", "<PERSON><PERSON><PERSON> (Formosa)", "<PERSON> (Formosa)", "<PERSON> (Ten Gun Design, Inc)", "<PERSON> '<PERSON><PERSON>' <PERSON>", "<PERSON><PERSON> (Harvey Nash, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Formosa)", "<PERSON>", "<PERSON> (Harvey Nash, Inc)", "<PERSON> (Formosa)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> (Formosa)", "<PERSON>", "<PERSON><PERSON> (Formosa)", "<PERSON> (Formosa)", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Formosa)", "<PERSON>", "<PERSON> (Formosa)", "<PERSON> (Formosa)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Formosa)", "<PERSON>", "<PERSON> (Formosa)", "<PERSON><PERSON><PERSON> (Formosa)", "<PERSON>", "<PERSON> (Formosa)", "<PERSON> (Formosa)", "<PERSON> (Harvey Nash, Inc)", "<PERSON>"]}, {"title": "Technical Artist", "names": ["<PERSON>"]}, {"title": "Product Design Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Product Designers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Graphic Designers", "names": ["<PERSON> (Formosa)", "<PERSON><PERSON><PERSON> (Ten Gun Design, Inc)", "<PERSON><PERSON> (Formosa)", "<PERSON> (Formosa)", "<PERSON><PERSON>"]}]}, {"discipline": "Audio", "titles": [{"title": "Audio Directors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Sound Designers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Music composed by", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}]}, {"discipline": "Quality Assessment", "titles": [{"title": "Quality Manager", "names": ["<PERSON>"]}, {"title": "Quality Leads", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Quality Engineers", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Quality Analysts", "names": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Quality Assessment Specialists", "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Program Managers", "names": ["<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)"]}, {"title": "Test Directors", "names": ["<PERSON> (Experis)", "<PERSON> (Experis)"]}, {"title": "Test Managers", "names": ["<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)"]}, {"title": "Team Leads", "names": ["<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Test Leads", "names": ["<PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "Santiago Quinio (Experis)"]}, {"title": "Test Automation Engineers", "names": ["<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Software Test Engineers", "names": ["<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> '<PERSON>' <PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON>-<PERSON><PERSON><PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Experis)", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Test Associates", "names": ["<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "Aleksander <PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "Burton Groves (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON>", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> Spence (Experis)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON>", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON>", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON>-<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON>ik<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}]}, {"discipline": "User Research", "titles": [{"title": "User Research Lead", "names": ["<PERSON><PERSON>"]}, {"title": "User Researcher", "names": ["<PERSON>"]}]}, {"discipline": "Operations", "titles": [{"title": "Player Operations Director", "names": ["<PERSON><PERSON>"]}, {"title": "Operations Directors, Global", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Operations Manager, Global", "names": ["<PERSON>"]}, {"title": "Operations Managers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)"]}, {"title": "Operations Coordinator", "names": ["<PERSON><PERSON>"]}, {"title": "Creative Operations Director", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Creative Operations Manager", "names": ["<PERSON>"]}, {"title": "People Operations Managers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Pettway", "<PERSON>", "Georgia Marra", "<PERSON>", "Mira <PERSON>"]}, {"title": "HR Directors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Human Resources", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Talent Acquisition", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)"]}, {"title": "Office Manager", "names": ["<PERSON><PERSON>"]}, {"title": "Executive Business Administrators", "names": ["<PERSON>", "<PERSON><PERSON><PERSON> (Simplicity Consulting Inc)", "<PERSON><PERSON><PERSON>"]}, {"title": "IT Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "IT", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Shoghi Cervantes Pueyo", "<PERSON><PERSON><PERSON>"]}, {"title": "Lead Automation Support", "names": ["<PERSON>"]}, {"title": "Automation Support", "names": ["<PERSON> (Insight Global, Inc)"]}]}, {"discipline": "Player Care", "titles": [{"title": "Director of Player Care", "names": ["<PERSON>"]}, {"title": "Player Support Program Leads", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Player Support", "names": ["<PERSON> (Apex Systems, Inc)", "<PERSON> (Apex Systems, Inc)"]}]}, {"discipline": "Data Analytics", "titles": [{"title": "Player Analytics and Insights Director", "names": ["<PERSON>"]}, {"title": "Data Science and Analytics Manager", "names": ["<PERSON>"]}, {"title": "Data Engineering Manager", "names": ["<PERSON>"]}, {"title": "Analytics Environment Manager", "names": ["<PERSON>"]}, {"title": "Analytics Environment Engineering", "names": ["<PERSON><PERSON> (Ascendion, Inc)", "<PERSON>"]}, {"title": "Data Science", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON> (Kelly Management Services, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Apex Systems, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Data Engineering", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}]}, {"discipline": "Business Management & Licensing", "titles": [{"title": "Strategy Director", "names": ["<PERSON>"]}, {"title": "Program Director, China Consumer & Global Partnerships", "names": ["<PERSON>"]}, {"title": "Program Director, Consumer Products", "names": ["Federico <PERSON>"]}, {"title": "Program Directors", "names": ["<PERSON> Vol<PERSON>es", "<PERSON>", "<PERSON>", "Gaylon Blank", "<PERSON>"]}, {"title": "Business Director, Franchise", "names": ["<PERSON>"]}, {"title": "Business Director, Japan & Market Expansion", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Business Director, Operations", "names": ["<PERSON>"]}, {"title": "Business Directors", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Business Development Managers", "names": ["<PERSON>", "<PERSON><PERSON> Chamberlain", "<PERSON><PERSON>", "<PERSON> (Digital Intelligence Systems, LLC)", "<PERSON>", "<PERSON> (Aerotek, Inc)"]}, {"title": "Program Managers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (my3Twelve, LLC)", "<PERSON> (Apex Systems, Inc)", "<PERSON> (my3Twelve, LLC)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (my3Twelve, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (my3Twelve, LLC)", "<PERSON><PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON><PERSON>", "Stuart U (my3Twelve, LLC)", "<PERSON>", "<PERSON> (Insight Global, Inc)"]}, {"title": "Business Managers", "names": ["<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Allegis Group Services, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Aquent, LLC)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Harvey Nash, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Epitec Inc)", "<PERSON> (JeffreyM Consulting, LLC)"]}]}, {"discipline": "Brand Management", "titles": [{"title": "Brand Director", "names": ["<PERSON>"]}, {"title": "Brand Manager", "names": ["<PERSON>"]}]}, {"discipline": "Communications", "titles": [{"title": "Chief Storyteller", "names": ["<PERSON>"]}, {"title": "Media Director", "names": ["<PERSON><PERSON>"]}, {"title": "Director of Communications", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Communications Managers", "names": ["<PERSON><PERSON>", "<PERSON> (Assembly Media, Inc)", "<PERSON><PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON> (Assembly Media, Inc)", "<PERSON><PERSON> (Assembly Media, Inc)", "<PERSON> (Assembly Media, Inc)", "Petra Tell", "<PERSON><PERSON> (Assembly Media, Inc)", "<PERSON> (Assembly Media, Inc)", "<PERSON><PERSON><PERSON>"]}, {"title": "Creative Writers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Director of Community", "names": ["<PERSON><PERSON>"]}, {"title": "Social Media Lead", "names": ["<PERSON>"]}, {"title": "Social Media Managers", "names": ["<PERSON>", "<PERSON> (Troy Consulting LLC)", "<PERSON> (Aston Carter, Inc)", "<PERSON><PERSON>-<PERSON> (Kforce, Inc)", "<PERSON> (JeffreyM Consulting, LLC)", "<PERSON> (Randstad)"]}, {"title": "Community Management Lead", "names": ["<PERSON>"]}, {"title": "Community Managers", "names": ["Dè<PERSON><PERSON> Easter (Apex Systems, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Nadine Ebri (Apex Systems, Inc)"]}, {"title": "Content Manager", "names": ["<PERSON><PERSON>"]}, {"title": "Publishing Editors", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>"]}]}, {"discipline": "Marketing", "titles": [{"title": "Head of Marketing", "names": ["<PERSON>"]}, {"title": "Marketing Directors", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Marketing Managers", "names": ["<PERSON>", "Arkadiy Goldenshtern", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Ascendion Inc)", "<PERSON>", "<PERSON> (Ten Gun Design, Inc)", "<PERSON> (Ten Gun Design, Inc)", "<PERSON> (Synaxis Corporation)", "<PERSON>", "<PERSON>", "<PERSON> (Ten Gun Design, Inc)", "<PERSON>"]}, {"title": "Web Content Manager", "names": ["<PERSON> (Ten Gun Design, Inc)"]}, {"title": "Web Content Authors & QA", "names": ["<PERSON><PERSON> (HCL Technologies)", "<PERSON><PERSON> (HCL Technologies)", "<PERSON> (Ten Gun Design, Inc)"]}, {"title": "Project Manager", "names": ["<PERSON> (Ten Gun Design, Inc)"]}]}, {"discipline": "Legal", "titles": [{"title": "Head of Legal", "names": ["<PERSON>"]}, {"title": "Senior Legal Counsel", "names": ["<PERSON>"]}, {"title": "Legal Counsel", "names": ["<PERSON><PERSON>", "<PERSON><PERSON> (Snodgrass Annand)", "<PERSON> (<PERSON>)"]}]}, {"discipline": "Finance", "titles": [{"title": "Finance Director", "names": ["<PERSON>"]}, {"title": "Finance Managers", "names": ["<PERSON> (Apex Systems, Inc)", "<PERSON>"]}, {"title": "Financial Accountants", "names": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Financial Consultant", "names": ["<PERSON><PERSON><PERSON>"]}]}]}, {"section": "Special Thanks", "disciplines": [{"discipline": "", "titles": [{"title": "", "names": ["4J Studios", "<PERSON>, Director of Business Development, Music and Talent", "<PERSON>, <PERSON><PERSON><PERSON>", "<PERSON>, Zen3 Infosolutions America, Inc", "<PERSON><PERSON>, Creator of Blockbench", "<PERSON><PERSON><PERSON><PERSON><PERSON>' <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "<PERSON>, Writer", "<PERSON>, <PERSON><PERSON><PERSON>", "<PERSON> \"skypjack\" <PERSON><PERSON>, Senior Software Engineer", "Microsoft Gaming Safety Team", "Microsoft Magic Team", "<PERSON><PERSON>, Developer Account Manager", "Sprung Studios, Ltd.", "The PlayFab Team", "The Xbox Live Team", "<PERSON>, Zen3 Infosolutions America, Inc", "Xbox Games Studios"]}]}]}, {"section": "Development Partner - Blackbird Interactive", "disciplines": [{"discipline": "", "titles": [{"title": "Technical Director", "names": ["<PERSON>"]}, {"title": "Programmers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Devon Plourde", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Lu"]}, {"title": "Producers", "names": ["<PERSON>", "<PERSON>"]}, {"title": "UI Artist", "names": ["<PERSON><PERSON>"]}, {"title": "UX Designer", "names": ["<PERSON>"]}, {"title": "Quality Assurance Director", "names": ["<PERSON>"]}, {"title": "Quality Assurance Analysts", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Vice President, Stategic Projects", "names": ["<PERSON>"]}]}]}, {"section": "Development Partner - CSI Interfusion Inc", "disciplines": [{"discipline": "", "titles": [{"title": "Software Engineers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}]}]}, {"section": "Development Partner - Disbelief", "disciplines": [{"discipline": "", "titles": [{"title": "President", "names": ["<PERSON>"]}, {"title": "CTO", "names": ["<PERSON>"]}, {"title": "Producer", "names": ["<PERSON>"]}, {"title": "Additional Production", "names": ["<PERSON>"]}, {"title": "Technical Director", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Lead Programmer", "names": ["<PERSON>"]}, {"title": "Senior Programmer", "names": ["<PERSON><PERSON>"]}, {"title": "Programmers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}]}]}, {"section": "Development Partner - Red Lens", "disciplines": [{"discipline": "", "titles": [{"title": "President", "names": ["<PERSON>"]}, {"title": "<PERSON>", "names": ["Veasna Chhaysy-Park"]}, {"title": "Tech Lead", "names": ["<PERSON>"]}, {"title": "Producer", "names": ["<PERSON>"]}, {"title": "Software Engineers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Skybox", "disciplines": [{"discipline": "", "titles": [{"title": "Founders", "names": ["<PERSON>", "Shyang Kong", "<PERSON>"]}, {"title": "Production", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Quality Assurance", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Software Developers", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Eser Kokturk", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>o T<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-Drevillon", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}]}]}, {"section": "Mojang Studios Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Original Creator of Minecraft", "names": ["<PERSON>"]}, {"title": "Studio Head of Mojang Studios", "names": ["<PERSON>"]}, {"title": "Head of Minecraft", "names": ["<PERSON>"]}, {"title": "Head of Stockholm Studio", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Chief Executive Officer", "names": ["<PERSON>"]}, {"title": "Chief Technology Officer", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Chief of Staff", "names": ["<PERSON>"]}, {"title": "Chief Creative Officer", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Head of Franchise Operations", "names": ["<PERSON>"]}, {"title": "Franchise Technical Director", "names": ["<PERSON>"]}, {"title": "Head of Games", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Game Director", "names": ["<PERSON>"]}, {"title": "Creative Director", "names": ["<PERSON>"]}, {"title": "Creative Lead", "names": ["<PERSON>"]}, {"title": "Design Managers", "names": ["<PERSON>", "<PERSON><PERSON>"]}, {"title": "Game Designers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (TEKsystems, Inc.)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (TEKsystems, Inc.)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Narrative Designer", "names": ["<PERSON>"]}, {"title": "Head of Creator Platform Engineering", "names": ["<PERSON>"]}, {"title": "Technical Directors", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Engineering Managers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Lead Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Syrgak <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Technical Leads", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> K <PERSON>o", "<PERSON><PERSON>", "<PERSON> (Collabera, LLC) (Software Development)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON>wami (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> (Artech Consulting, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Coseo Frerichs (Insight Global, Inc)", "<PERSON>", "<PERSON>", "Dag Oldenburg", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON>", "<PERSON> '<PERSON>' <PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON> (CSI Interfusion, Inc)", "Dodge Lafnitzegger (Insight Global, Inc)", "<PERSON>", "Don <PERSON> II", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON><PERSON>", "<PERSON><PERSON>", "Gage Way (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> He<PERSON>jkenskjöld", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON> '<PERSON><PERSON>' <PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Agility Partners, LLC)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON> (Randstad)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Design Laboratory, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON> (Ascendion, Inc)", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON> (Randstad)", "<PERSON> (Insight Global Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Kalvi Consulting Services, Inc)", "Louis <PERSON>da (Insight Global, Inc)", "<PERSON> (Collabera, LLC)", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (WaferWire Cloud Technologies)", "<PERSON> (Insight Global, Inc)", "Maxxwell Plum (Insight Global, Inc)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> '<PERSON><PERSON><PERSON>' <PERSON>", "<PERSON>", "<PERSON> (Ascendion, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (Collabera, LLC)", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON> (Ascendion, Inc)", "<PERSON>", "<PERSON><PERSON> (Virtuosity)", "<PERSON> (Insight Global, Inc)", "<PERSON> M Riviera", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Virtuosity)", "<PERSON>", "<PERSON><PERSON><PERSON> (Virtuosity)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON> (TEKsystems, Inc.)", "<PERSON> (Insight Global, Inc)", "<PERSON>"]}, {"title": "Lead Architects", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Architects", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Launcher", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Additional Programming", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Chief Product Officer", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Head of Minecraft Atlas", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Production Director", "names": ["<PERSON>"]}, {"title": "Executive Producers", "names": ["<PERSON>", "Gama Aguilar-Gamez", "<PERSON>"]}, {"title": "Production Manager", "names": ["<PERSON>"]}, {"title": "Production Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Producers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Best Liang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Randstad)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Randstad)", "<PERSON><PERSON> (Aquent, LLC)", "<PERSON><PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Isabella <PERSON>", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON>, Ifeguni", "<PERSON><PERSON> (TEKsystems, Inc)", "<PERSON>", "<PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Randstad)", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (Design Laboratory, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Loudon St Hill (Insight Global, Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (eXcell, a division of CompuCom)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON>", "<PERSON> (Randstad)", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Orrheim", "<PERSON><PERSON>", "<PERSON> (Collabera, LLC)", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> (TEKsystems, Inc.)", "<PERSON><PERSON>", "<PERSON> (Yoh Services LLC)", "<PERSON> (Pivotal Consulting, LLC)", "<PERSON>", "Yesenia Cisneros"]}, {"title": "Assistant Producer", "names": ["<PERSON>"]}, {"title": "Product Manager", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Organizational Coaches", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Release Managers", "names": ["<PERSON> (Kforce, Inc)", "<PERSON> (Design Laboratory, Inc)", "<PERSON> (Randstad)", "<PERSON> (Apex Systems, Inc)"]}, {"title": "Technical Writers", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (TEKsystems, Inc.)"]}, {"title": "Technical Program Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "Morgan J. East (Randstad)", "<PERSON> (TEKsystems, Inc.)"]}, {"title": "Playtest Coordinators", "names": ["<PERSON> (Aquent, LLC)", "<PERSON> (Randstad)"]}, {"title": "Art Directors", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Artist Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Artists", "names": ["<PERSON> (Harvey Nash, Inc)", "<PERSON> (Randstad)", "<PERSON><PERSON> (Aquent, LLC)", "<PERSON> (Collabera, LLC)", "<PERSON><PERSON>", "<PERSON> (eXcell, a division of CompuCom)", "<PERSON> Night (Aquent, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jei G Ling (Allegis Group Services, Inc)", "<PERSON><PERSON> (TEKsystems, Inc.)", "<PERSON><PERSON>", "Jonatan Pöljö", "<PERSON> (Aquent, LLC)", "<PERSON> (CompuCom Systems, Inc)", "<PERSON> (Randstad)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Collabera, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (TEKsystems, Inc.)", "<PERSON>", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Randstad)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Product Designers", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Graphic Designer", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Motion Graphics Designer", "names": ["<PERSON> (Randstad)"]}, {"title": "Sound Designers", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Insight Global, Inc)"]}, {"title": "Technical Audio Developer", "names": ["Jonatan Crafoord"]}, {"title": "Quality Engineers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON> (Kforce, Inc)", "<PERSON><PERSON><PERSON>"]}, {"title": "Quality Data Analysis & Engineering", "names": ["<PERSON>"]}, {"title": "Quality Assessment Specialists", "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"title": "User Experience Design Directors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "User Experience Designers", "names": ["<PERSON> (CompuCom Systems, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Li (TEKsystems, Inc.)", "<PERSON>", "<PERSON>", "<PERSON> (Prieto)", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> (Randstad)", "<PERSON> (Aquent, LLC)", "<PERSON>", "<PERSON> (Randstad)"]}, {"title": "User Experience Intern", "names": ["<PERSON>"]}, {"title": "User Research Lead", "names": ["<PERSON>"]}, {"title": "User Researchers", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Head of Player Operations", "names": ["<PERSON>"]}, {"title": "People Experience Manager", "names": ["<PERSON>"]}, {"title": "HR Directors", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Human Resources", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Talent Acquisition", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Office Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Office Coordinators", "names": ["<PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Executive Business Administrators", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"]}, {"title": "Business Administrator", "names": ["<PERSON><PERSON><PERSON> (C2S Technologies, Inc)"]}, {"title": "Front of House", "names": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "IT", "names": ["<PERSON>", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON> (Centric Professionals AB)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (Warner Marketing, Inc)", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Operations Managers", "names": ["<PERSON>", "<PERSON> (Lions and Tigers)", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Automation Support", "names": ["<PERSON> (WaferWire Cloud Technologies)", "<PERSON> (Collabera, LLC)", "<PERSON>ett (TEKsystems, Inc.)", "<PERSON> (Digital Intelligence Systems, LLC)", "<PERSON> (Insight Global, Inc)"]}, {"title": "DevOps Engineer", "names": ["<PERSON> (Collabera, LLC)"]}, {"title": "Player Support Leads", "names": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"title": "Player Support Operations Manager", "names": ["<PERSON>"]}, {"title": "Player Support", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "Angehlica Walling", "<PERSON><PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (TEKsystems, Inc)", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> (TEKsystems, Inc.)", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (TEKsystems, Inc)", "<PERSON>", "<PERSON> (Apex Systems, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Data Engineering", "names": ["<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON>"]}, {"title": "Data and Analytics Lead", "names": ["<PERSON>"]}, {"title": "Analytics Environment Engineering", "names": ["<PERSON><PERSON><PERSON> (Manpower Services Canada Limit)", "Vini De Lima De Sousa (0965688 BC Ltd)"]}, {"title": "Data Science", "names": ["<PERSON> (National Business Innovations)", "<PERSON><PERSON><PERSON><PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON>", "<PERSON> (Design Laboratory, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Design Laboratory, Inc)", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Allegis Group Services, Inc)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON><PERSON> (Allegis Global Solutions)", "<PERSON><PERSON><PERSON> (Agility Partners, LLC)", "<PERSON> (KellyMitchell Group, LLC)", "<PERSON>", "<PERSON> (Insight Global, Inc)", "<PERSON><PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON> (Design Laboratory, Inc)", "<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON><PERSON>", "<PERSON><PERSON> (Design Laboratory, Inc)", "<PERSON><PERSON><PERSON><PERSON> Deshpande (KellyMitchell Group, LLC)", "<PERSON> (Simplicity Consulting Inc.)", "Tong Shen (KellyMitchell Group, LLC)", "<PERSON><PERSON><PERSON> (Design Laboratory, Inc)"]}, {"title": "Head of Creator Marketplace", "names": ["<PERSON>"]}, {"title": "Business Director, Minecraft Game", "names": ["<PERSON><PERSON>"]}, {"title": "Directors of Business Management", "names": ["<PERSON>"]}, {"title": "Director of Business Planning", "names": ["<PERSON>"]}, {"title": "Director of Business Development", "names": ["<PERSON>erie <PERSON>"]}, {"title": "Project Director", "names": ["<PERSON>"]}, {"title": "Business Development Managers", "names": ["<PERSON> (JeffreyM Consulting, LLC)", "<PERSON> Comar (Digital Intelligence Systems, LLC)"]}, {"title": "Program Managers", "names": ["<PERSON> (Experis)", "<PERSON><PERSON>", "<PERSON> (Experis)", "<PERSON> (my3Twelve, LLC)", "<PERSON><PERSON> (Populus Group, LLC)", "<PERSON> (Bluehawk LLC)", "<PERSON>", "<PERSON> (Aerotek, Inc)", "<PERSON> (Apex Systems, Inc)", "<PERSON> (my3Twelve, LLC)", "<PERSON><PERSON><PERSON>", "<PERSON> (Simplicity Consulting Inc.)", "<PERSON> (Digital Intelligence Systems, LLC)", "<PERSON> (Simplicity Consulting Inc.)", "<PERSON> (Amaxra)", "<PERSON> (Simplicity Consulting Inc.)", "<PERSON> (TEKsystems, Inc)", "Tori <PERSON> (Cypress Human Capital)", "<PERSON>"]}, {"title": "Business Managers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON> (Simplicity Consulting Inc.)", "<PERSON>", "<PERSON>", "<PERSON> (Aston Carter, Inc)", "<PERSON>", "<PERSON> (Rylem)", "<PERSON> (Simplicity Consulting Inc.)"]}, {"title": "Business Analysts", "names": ["<PERSON> (Populus Group, LLC)", "<PERSON><PERSON> (Apex Systems, Inc)", "<PERSON> (Populus Group, LLC)"]}, {"title": "Head of Creative Production", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Lead Project Manager", "names": ["<PERSON>"]}, {"title": "Intellectual Property Enforcement Leads", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Intellectual Property Enforcement Agents", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Brand Director", "names": ["<PERSON>"]}, {"title": "Lead Producer - Brand Experience", "names": ["<PERSON><PERSON>"]}, {"title": "Media Director", "names": ["<PERSON>"]}, {"title": "Head of Creative Communications", "names": ["<PERSON>"]}, {"title": "Director of Communications", "names": ["<PERSON>"]}, {"title": "Communications Managers", "names": ["<PERSON> (Assembly Media, Inc)", "<PERSON> (Simplicity Consulting Inc)", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Content Coordinators", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Communications Editors", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Assembly Media, Inc", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Creative Writers", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Lead Web Developer", "names": ["<PERSON>"]}, {"title": "Web Designers", "names": ["<PERSON> (Ten Gun Design, Inc)", "<PERSON> (eXcell, a division of CompuCom)"]}, {"title": "Social Media Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Social Media Managers", "names": ["Alek<PERSON><PERSON> (Aston Carter Inc)", "<PERSON> (Adecco)", "<PERSON> (Collabera, LLC)", "<PERSON> (Aerotek, Inc)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Ranstad)", "<PERSON>"]}, {"title": "Community Managers", "names": ["<PERSON>", "<PERSON> (Experis)", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Corestaff)"]}, {"title": "Content Manager", "names": ["<PERSON><PERSON><PERSON>"]}, {"title": "Marketing Managers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON> (Simplicity Consulting Inc.)", "<PERSON>", "<PERSON>", "<PERSON>", "Didac <PERSON>", "<PERSON> (Emily)", "<PERSON>", "<PERSON><PERSON> (Simplicity Consulting Inc.)", "<PERSON> (Simplicity Consulting Inc.)", "<PERSON>", "<PERSON> (Ascendion Inc)"]}, {"title": "Head of Legal", "names": ["<PERSON>", "<PERSON>"]}, {"title": "Legal Counsel", "names": ["<PERSON><PERSON>"]}, {"title": "Finance Managers", "names": ["<PERSON>", "<PERSON><PERSON>"]}, {"title": "Financial Accountants", "names": ["Camilla Brantefelt", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Financial Consultant", "names": ["<PERSON>"]}]}]}, {"section": "Studios Quality Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Quality Director", "names": ["<PERSON>"]}, {"title": "Quality Managers", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Quality Leads", "names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Quality Engineers", "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Quality Analysts", "names": ["<PERSON>", "<PERSON> (Kforce)"]}, {"title": "Program Managers", "names": ["Amador Abreu (Insight Global, Inc)", "<PERSON> (Pivotal Consulting)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Insight Global, Inc)"]}, {"title": "Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Data Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Test Managers", "names": ["<PERSON> (Experis)"]}, {"title": "Team Leads", "names": ["<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Test Leads", "names": ["<PERSON> (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Experis)", "<PERSON> (Experis)"]}, {"title": "Software Test Engineers", "names": ["<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> Edwards (Insight Global, Inc)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Insight Global, Inc)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Insight Global, Inc.)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Test Associates", "names": ["<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Experis)", "Aleksander <PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Insight Global, Inc.)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "Katarzyna <PERSON> (Lionbridge)", "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "Małgor<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "Šimon <PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON> (Lionbridge)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lionbridge)", "<PERSON><PERSON><PERSON> (Lionbridge)"]}, {"title": "Studios Quality Special Thanks", "names": ["<PERSON> (Experis)", "<PERSON> (Experis)", "<PERSON> – Data Science Manager", "<PERSON> – Executive Business Administrator", "<PERSON> – Outsourcing Manager", "<PERSON> (Experis) – Test Manager, <PERSON><PERSON><PERSON>", "<PERSON> (Experis)", "<PERSON> – Client Account Director (Experis)", "<PERSON> – Business Manager", "<PERSON> – Director XGS Business Operations", "<PERSON> (Experis)", "<PERSON> – Quality Director, Studios Quality UK", "<PERSON> – Director of Quality, Studios Quality", "<PERSON> (Experis)", "<PERSON><PERSON> – Executive Business Administrator", "<PERSON> (Experis)", "<PERSON> – Software Engineering Manager", "<PERSON> – Software Engineering Lead", "<PERSON> – Center of Excellence (Experis)"]}]}]}, {"section": "Development Partner - Blackbird Interactive Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Programmers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"title": "Producers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Designers", "names": ["<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Lead Quality Assurance Analysts", "names": ["JP <PERSON>", "<PERSON>"]}, {"title": "Quality Assurance Analysts", "names": ["<PERSON>", "<PERSON>"]}]}]}, {"section": "Development Partner - CSI Interfusion Inc Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}]}]}, {"section": "Development Partner - Disbelief Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Producers", "names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"title": "Associate Producer", "names": ["<PERSON>"]}, {"title": "Senior Programmer", "names": ["<PERSON>"]}, {"title": "Programmers", "names": ["<PERSON>", "Kainin Tankersley", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Red Lens Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "GM & Development Director", "names": ["<PERSON>"]}, {"title": "<PERSON> Leads", "names": ["<PERSON>", "<PERSON><PERSON>"]}, {"title": "Tech Leads", "names": ["<PERSON>", "<PERSON> III", "<PERSON><PERSON> Lawson", "<PERSON>", "<PERSON>"]}, {"title": "Production Director", "names": ["<PERSON>"]}, {"title": "Producer", "names": ["<PERSON><PERSON>"]}, {"title": "Software Engineers", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}]}]}, {"section": "Development Partner - SiProgs Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Software Engineer", "names": ["<PERSON>"]}, {"title": "Software Engineers", "names": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"title": "Software Test Engineers", "names": ["Frantisek Beke", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Skybox Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Quality Assurance", "names": ["<PERSON><PERSON>", "<PERSON>"]}, {"title": "Software Developers", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>z", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "", "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ashlyn Gadow", "Arta Seify", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Thiago Braga", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>g<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}]}]}, {"section": "Development Partner - Virtuosity Alumni", "disciplines": [{"discipline": "", "titles": [{"title": "Game Developers", "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Services Developers", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Malliga Muthuraj", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Web Developers", "names": ["Nazia Nazia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"title": "Producers", "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}]}]}]