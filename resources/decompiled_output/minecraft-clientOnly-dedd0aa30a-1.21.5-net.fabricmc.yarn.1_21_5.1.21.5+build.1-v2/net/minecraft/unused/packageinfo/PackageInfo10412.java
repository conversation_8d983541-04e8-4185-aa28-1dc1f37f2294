package net.minecraft.unused.packageinfo;

import javax.annotation.ParametersAreNonnullByDefault;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.util.annotation.FieldsAreNonnullByDefault;
import net.minecraft.util.annotation.MethodsReturnNonnullByDefault;

// $FF: synthetic class
@ParametersAreNonnullByDefault
@MethodsReturnNonnullByDefault
@FieldsAreNonnullByDefault
@Environment(EnvType.CLIENT)
interface PackageInfo10412 {
}
