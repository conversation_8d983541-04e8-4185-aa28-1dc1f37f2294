package net.minecraft.client.item;

import com.mojang.serialization.Codec;
import com.mojang.serialization.MapCodec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.render.item.model.ItemModel;
import net.minecraft.client.render.item.model.ItemModelTypes;
import net.minecraft.registry.ContextSwapper;
import org.jetbrains.annotations.Nullable;

@Environment(EnvType.CLIENT)
public record ItemAsset(ItemModel.Unbaked model, Properties properties, @Nullable ContextSwapper registrySwapper) {
   public static final Codec<ItemAsset> CODEC = RecordCodecBuilder.create((instance) -> instance.group(ItemModelTypes.CODEC.fieldOf("model").forGetter(ItemAsset::model), ItemAsset.Properties.CODEC.forGetter(ItemAsset::properties)).apply(instance, ItemAsset::new));

   public ItemAsset(ItemModel.Unbaked model, Properties properties) {
      this(model, properties, (ContextSwapper)null);
   }

   public ItemAsset withContextSwapper(ContextSwapper contextSwapper) {
      return new ItemAsset(this.model, this.properties, contextSwapper);
   }

   @Environment(EnvType.CLIENT)
   public static record Properties(boolean handAnimationOnSwap) {
      public static final Properties DEFAULT = new Properties(true);
      public static final MapCodec<Properties> CODEC = RecordCodecBuilder.mapCodec((instance) -> instance.group(Codec.BOOL.optionalFieldOf("hand_animation_on_swap", true).forGetter(Properties::handAnimationOnSwap)).apply(instance, Properties::new));
   }
}
