package net.minecraft.client.render;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.BitSet;
import java.util.Collection;
import java.util.Deque;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.util.ClosableFactory;
import net.minecraft.client.util.ObjectAllocator;
import org.jetbrains.annotations.Nullable;

@Environment(EnvType.CLIENT)
public class FrameGraphBuilder {
   private final List<ResourceNode<?>> resourceNodes = new ArrayList();
   private final List<ObjectNode<?>> objectNodes = new ArrayList();
   private final List<FramePassImpl> passes = new ArrayList();

   public FramePass createPass(String name) {
      FramePassImpl framePassImpl = new FramePassImpl(this.passes.size(), name);
      this.passes.add(framePassImpl);
      return framePassImpl;
   }

   public <T> net.minecraft.client.util.Handle<T> createObjectNode(String name, T object) {
      ObjectNode<T> objectNode = new ObjectNode<T>(name, (FramePassImpl)null, object);
      this.objectNodes.add(objectNode);
      return objectNode.handle;
   }

   public <T> net.minecraft.client.util.Handle<T> createResourceHandle(String name, ClosableFactory<T> factory) {
      return this.createResourceNode(name, factory, (FramePassImpl)null).handle;
   }

   <T> ResourceNode<T> createResourceNode(String name, ClosableFactory<T> factory, @Nullable FramePassImpl stageNode) {
      int i = this.resourceNodes.size();
      ResourceNode<T> resourceNode = new ResourceNode<T>(i, name, stageNode, factory);
      this.resourceNodes.add(resourceNode);
      return resourceNode;
   }

   public void run(ObjectAllocator allocator) {
      this.run(allocator, FrameGraphBuilder.Profiler.NONE);
   }

   public void run(ObjectAllocator allocator, Profiler profiler) {
      BitSet bitSet = this.collectPassesToVisit();
      List<FramePassImpl> list = new ArrayList(bitSet.cardinality());
      BitSet bitSet2 = new BitSet(this.passes.size());

      for(FramePassImpl framePassImpl : this.passes) {
         this.visit(framePassImpl, bitSet, bitSet2, list);
      }

      this.checkResources(list);

      for(FramePassImpl framePassImpl : list) {
         for(ResourceNode<?> resourceNode : framePassImpl.resourcesToAcquire) {
            profiler.acquire(resourceNode.name);
            resourceNode.acquire(allocator);
         }

         profiler.push(framePassImpl.name);
         framePassImpl.renderer.run();
         profiler.pop(framePassImpl.name);

         for(int i = framePassImpl.resourcesToRelease.nextSetBit(0); i >= 0; i = framePassImpl.resourcesToRelease.nextSetBit(i + 1)) {
            ResourceNode<?> resourceNode = (ResourceNode)this.resourceNodes.get(i);
            profiler.release(resourceNode.name);
            resourceNode.release(allocator);
         }
      }

   }

   private BitSet collectPassesToVisit() {
      Deque<FramePassImpl> deque = new ArrayDeque(this.passes.size());
      BitSet bitSet = new BitSet(this.passes.size());

      for(Node<?> node : this.objectNodes) {
         FramePassImpl framePassImpl = node.handle.from;
         if (framePassImpl != null) {
            this.markForVisit(framePassImpl, bitSet, deque);
         }
      }

      for(FramePassImpl framePassImpl2 : this.passes) {
         if (framePassImpl2.toBeVisited) {
            this.markForVisit(framePassImpl2, bitSet, deque);
         }
      }

      return bitSet;
   }

   private void markForVisit(FramePassImpl pass, BitSet result, Deque<FramePassImpl> deque) {
      deque.add(pass);

      while(!deque.isEmpty()) {
         FramePassImpl framePassImpl = (FramePassImpl)deque.poll();
         if (!result.get(framePassImpl.id)) {
            result.set(framePassImpl.id);

            for(int i = framePassImpl.requiredPassIds.nextSetBit(0); i >= 0; i = framePassImpl.requiredPassIds.nextSetBit(i + 1)) {
               deque.add((FramePassImpl)this.passes.get(i));
            }
         }
      }

   }

   private void visit(FramePassImpl node, BitSet unvisited, BitSet visiting, List<FramePassImpl> topologicalOrderOut) {
      if (visiting.get(node.id)) {
         String string = (String)visiting.stream().mapToObj((id) -> ((FramePassImpl)this.passes.get(id)).name).collect(Collectors.joining(", "));
         throw new IllegalStateException("Frame graph cycle detected between " + string);
      } else if (unvisited.get(node.id)) {
         visiting.set(node.id);
         unvisited.clear(node.id);

         for(int i = node.requiredPassIds.nextSetBit(0); i >= 0; i = node.requiredPassIds.nextSetBit(i + 1)) {
            this.visit((FramePassImpl)this.passes.get(i), unvisited, visiting, topologicalOrderOut);
         }

         for(Handle<?> handle : node.transferredHandles) {
            for(int j = handle.dependents.nextSetBit(0); j >= 0; j = handle.dependents.nextSetBit(j + 1)) {
               if (j != node.id) {
                  this.visit((FramePassImpl)this.passes.get(j), unvisited, visiting, topologicalOrderOut);
               }
            }
         }

         topologicalOrderOut.add(node);
         visiting.clear(node.id);
      }
   }

   private void checkResources(Collection<FramePassImpl> passes) {
      FramePassImpl[] framePassImpls = new FramePassImpl[this.resourceNodes.size()];

      for(FramePassImpl framePassImpl : passes) {
         for(int i = framePassImpl.requiredResourceIds.nextSetBit(0); i >= 0; i = framePassImpl.requiredResourceIds.nextSetBit(i + 1)) {
            ResourceNode<?> resourceNode = (ResourceNode)this.resourceNodes.get(i);
            FramePassImpl framePassImpl2 = framePassImpls[i];
            framePassImpls[i] = framePassImpl;
            if (framePassImpl2 == null) {
               framePassImpl.resourcesToAcquire.add(resourceNode);
            } else {
               framePassImpl2.resourcesToRelease.clear(i);
            }

            framePassImpl.resourcesToRelease.set(i);
         }
      }

   }

   @Environment(EnvType.CLIENT)
   class FramePassImpl implements FramePass {
      final int id;
      final String name;
      final List<Handle<?>> transferredHandles = new ArrayList();
      final BitSet requiredResourceIds = new BitSet();
      final BitSet requiredPassIds = new BitSet();
      Runnable renderer = () -> {
      };
      final List<ResourceNode<?>> resourcesToAcquire = new ArrayList();
      final BitSet resourcesToRelease = new BitSet();
      boolean toBeVisited;

      public FramePassImpl(final int id, final String name) {
         this.id = id;
         this.name = name;
      }

      private <T> void addRequired(Handle<T> handle) {
         Node var3 = handle.parent;
         if (var3 instanceof ResourceNode<?> resourceNode) {
            this.requiredResourceIds.set(resourceNode.id);
         }

      }

      private void addRequired(FramePassImpl child) {
         this.requiredPassIds.set(child.id);
      }

      public <T> net.minecraft.client.util.Handle<T> addRequiredResource(String name, ClosableFactory<T> factory) {
         ResourceNode<T> resourceNode = FrameGraphBuilder.this.<T>createResourceNode(name, factory, this);
         this.requiredResourceIds.set(resourceNode.id);
         return resourceNode.handle;
      }

      public <T> void dependsOn(net.minecraft.client.util.Handle<T> handle) {
         this.dependsOn((Handle)handle);
      }

      private <T> void dependsOn(Handle<T> handle) {
         this.addRequired(handle);
         if (handle.from != null) {
            this.addRequired(handle.from);
         }

         handle.dependents.set(this.id);
      }

      public <T> net.minecraft.client.util.Handle<T> transfer(net.minecraft.client.util.Handle<T> handle) {
         return this.transfer((Handle)handle);
      }

      public void addRequired(FramePass pass) {
         this.requiredPassIds.set(((FramePassImpl)pass).id);
      }

      public void markToBeVisited() {
         this.toBeVisited = true;
      }

      private <T> Handle<T> transfer(Handle<T> handle) {
         this.transferredHandles.add(handle);
         this.dependsOn(handle);
         return handle.moveTo(this);
      }

      public void setRenderer(Runnable renderer) {
         this.renderer = renderer;
      }

      public String toString() {
         return this.name;
      }
   }

   @Environment(EnvType.CLIENT)
   static class Handle<T> implements net.minecraft.client.util.Handle<T> {
      final Node<T> parent;
      private final int id;
      @Nullable
      final FramePassImpl from;
      final BitSet dependents = new BitSet();
      @Nullable
      private Handle<T> movedTo;

      Handle(Node<T> parent, int id, @Nullable FramePassImpl from) {
         this.parent = parent;
         this.id = id;
         this.from = from;
      }

      public T get() {
         return this.parent.get();
      }

      Handle<T> moveTo(FramePassImpl pass) {
         if (this.parent.handle != this) {
            String var10002 = String.valueOf(this);
            throw new IllegalStateException("Handle " + var10002 + " is no longer valid, as its contents were moved into " + String.valueOf(this.movedTo));
         } else {
            Handle<T> handle = new Handle<T>(this.parent, this.id + 1, pass);
            this.parent.handle = handle;
            this.movedTo = handle;
            return handle;
         }
      }

      public String toString() {
         if (this.from != null) {
            String var1 = String.valueOf(this.parent);
            return var1 + "#" + this.id + " (from " + String.valueOf(this.from) + ")";
         } else {
            String var10000 = String.valueOf(this.parent);
            return var10000 + "#" + this.id;
         }
      }
   }

   @Environment(EnvType.CLIENT)
   abstract static class Node<T> {
      public final String name;
      public Handle<T> handle;

      public Node(String name, @Nullable FramePassImpl from) {
         this.name = name;
         this.handle = new Handle<T>(this, 0, from);
      }

      public abstract T get();

      public String toString() {
         return this.name;
      }
   }

   @Environment(EnvType.CLIENT)
   static class ResourceNode<T> extends Node<T> {
      final int id;
      private final ClosableFactory<T> factory;
      @Nullable
      private T resource;

      public ResourceNode(int id, String name, @Nullable FramePassImpl from, ClosableFactory<T> factory) {
         super(name, from);
         this.id = id;
         this.factory = factory;
      }

      public T get() {
         return (T)Objects.requireNonNull(this.resource, "Resource is not currently available");
      }

      public void acquire(ObjectAllocator allocator) {
         if (this.resource != null) {
            throw new IllegalStateException("Tried to acquire physical resource, but it was already assigned");
         } else {
            this.resource = (T)allocator.acquire(this.factory);
         }
      }

      public void release(ObjectAllocator allocator) {
         if (this.resource == null) {
            throw new IllegalStateException("Tried to release physical resource that was not allocated");
         } else {
            allocator.release(this.factory, this.resource);
            this.resource = null;
         }
      }
   }

   @Environment(EnvType.CLIENT)
   static class ObjectNode<T> extends Node<T> {
      private final T value;

      public ObjectNode(String name, @Nullable FramePassImpl parent, T value) {
         super(name, parent);
         this.value = value;
      }

      public T get() {
         return this.value;
      }
   }

   @Environment(EnvType.CLIENT)
   public interface Profiler {
      Profiler NONE = new Profiler() {
      };

      default void acquire(String name) {
      }

      default void release(String name) {
      }

      default void push(String location) {
      }

      default void pop(String location) {
      }
   }
}
