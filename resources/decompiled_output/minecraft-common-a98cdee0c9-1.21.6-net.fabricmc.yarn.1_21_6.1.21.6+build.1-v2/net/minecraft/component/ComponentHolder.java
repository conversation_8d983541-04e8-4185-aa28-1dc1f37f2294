package net.minecraft.component;

import java.util.stream.Stream;
import org.jetbrains.annotations.Nullable;

public interface ComponentHolder extends ComponentsAccess {
   ComponentMap getComponents();

   @Nullable
   default <T> T get(ComponentType<? extends T> type) {
      return (T)this.getComponents().get(type);
   }

   default <T> Stream<T> streamAll(Class<? extends T> valueClass) {
      return this.getComponents().stream().map(Component::value).filter((value) -> valueClass.isAssignableFrom(value.getClass())).map((value) -> value);
   }

   default <T> T getOrDefault(ComponentType<? extends T> type, T fallback) {
      return (T)this.getComponents().getOrDefault(type, fallback);
   }

   default boolean contains(ComponentType<?> type) {
      return this.getComponents().contains(type);
   }
}
