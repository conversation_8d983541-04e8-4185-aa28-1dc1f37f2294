package net.minecraft.block.entity;

import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.logging.LogUtils;
import java.util.List;
import java.util.UUID;
import java.util.function.UnaryOperator;
import net.minecraft.block.AbstractSignBlock;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.network.packet.s2c.play.BlockEntityUpdateS2CPacket;
import net.minecraft.registry.RegistryWrapper;
import net.minecraft.server.command.CommandOutput;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.filter.FilteredMessage;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundEvent;
import net.minecraft.sound.SoundEvents;
import net.minecraft.storage.ReadView;
import net.minecraft.storage.WriteView;
import net.minecraft.text.ClickEvent;
import net.minecraft.text.Style;
import net.minecraft.text.Text;
import net.minecraft.text.Texts;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec2f;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;

public class SignBlockEntity extends BlockEntity {
   private static final Logger LOGGER = LogUtils.getLogger();
   private static final int MAX_TEXT_WIDTH = 90;
   private static final int TEXT_LINE_HEIGHT = 10;
   private static final boolean DEFAULT_WAXED = false;
   @Nullable
   private UUID editor;
   private SignText frontText;
   private SignText backText;
   private boolean waxed;

   public SignBlockEntity(BlockPos pos, BlockState state) {
      this(BlockEntityType.SIGN, pos, state);
   }

   public SignBlockEntity(BlockEntityType blockEntityType, BlockPos blockPos, BlockState blockState) {
      super(blockEntityType, blockPos, blockState);
      this.waxed = false;
      this.frontText = this.createText();
      this.backText = this.createText();
   }

   protected SignText createText() {
      return new SignText();
   }

   public boolean isPlayerFacingFront(PlayerEntity player) {
      Block var3 = this.getCachedState().getBlock();
      if (var3 instanceof AbstractSignBlock abstractSignBlock) {
         Vec3d vec3d = abstractSignBlock.getCenter(this.getCachedState());
         double d = player.getX() - ((double)this.getPos().getX() + vec3d.x);
         double e = player.getZ() - ((double)this.getPos().getZ() + vec3d.z);
         float f = abstractSignBlock.getRotationDegrees(this.getCachedState());
         float g = (float)(MathHelper.atan2(e, d) * (double)(180F / (float)Math.PI)) - 90.0F;
         return MathHelper.angleBetween(f, g) <= 90.0F;
      } else {
         return false;
      }
   }

   public SignText getText(boolean front) {
      return front ? this.frontText : this.backText;
   }

   public SignText getFrontText() {
      return this.frontText;
   }

   public SignText getBackText() {
      return this.backText;
   }

   public int getTextLineHeight() {
      return 10;
   }

   public int getMaxTextWidth() {
      return 90;
   }

   protected void writeData(WriteView view) {
      super.writeData(view);
      view.put("front_text", SignText.CODEC, this.frontText);
      view.put("back_text", SignText.CODEC, this.backText);
      view.putBoolean("is_waxed", this.waxed);
   }

   protected void readData(ReadView view) {
      super.readData(view);
      this.frontText = (SignText)view.read("front_text", SignText.CODEC).map(this::parseLines).orElseGet(SignText::new);
      this.backText = (SignText)view.read("back_text", SignText.CODEC).map(this::parseLines).orElseGet(SignText::new);
      this.waxed = view.getBoolean("is_waxed", false);
   }

   private SignText parseLines(SignText signText) {
      for(int i = 0; i < 4; ++i) {
         Text text = this.parseLine(signText.getMessage(i, false));
         Text text2 = this.parseLine(signText.getMessage(i, true));
         signText = signText.withMessage(i, text, text2);
      }

      return signText;
   }

   private Text parseLine(Text text) {
      World var3 = this.world;
      if (var3 instanceof ServerWorld serverWorld) {
         try {
            return Texts.parse(createCommandSource((PlayerEntity)null, serverWorld, this.pos), text, (Entity)null, 0);
         } catch (CommandSyntaxException var4) {
         }
      }

      return text;
   }

   public void tryChangeText(PlayerEntity player, boolean front, List<FilteredMessage> messages) {
      if (!this.isWaxed() && player.getUuid().equals(this.getEditor()) && this.world != null) {
         this.changeText((text) -> this.getTextWithMessages(player, messages, text), front);
         this.setEditor((UUID)null);
         this.world.updateListeners(this.getPos(), this.getCachedState(), this.getCachedState(), 3);
      } else {
         LOGGER.warn("Player {} just tried to change non-editable sign", player.getName().getString());
      }
   }

   public boolean changeText(UnaryOperator<SignText> textChanger, boolean front) {
      SignText signText = this.getText(front);
      return this.setText((SignText)textChanger.apply(signText), front);
   }

   private SignText getTextWithMessages(PlayerEntity player, List<FilteredMessage> messages, SignText text) {
      for(int i = 0; i < messages.size(); ++i) {
         FilteredMessage filteredMessage = (FilteredMessage)messages.get(i);
         Style style = text.getMessage(i, player.shouldFilterText()).getStyle();
         if (player.shouldFilterText()) {
            text = text.withMessage(i, Text.literal(filteredMessage.getString()).setStyle(style));
         } else {
            text = text.withMessage(i, Text.literal(filteredMessage.raw()).setStyle(style), Text.literal(filteredMessage.getString()).setStyle(style));
         }
      }

      return text;
   }

   public boolean setText(SignText text, boolean front) {
      return front ? this.setFrontText(text) : this.setBackText(text);
   }

   private boolean setBackText(SignText backText) {
      if (backText != this.backText) {
         this.backText = backText;
         this.updateListeners();
         return true;
      } else {
         return false;
      }
   }

   private boolean setFrontText(SignText frontText) {
      if (frontText != this.frontText) {
         this.frontText = frontText;
         this.updateListeners();
         return true;
      } else {
         return false;
      }
   }

   public boolean canRunCommandClickEvent(boolean front, PlayerEntity player) {
      return this.isWaxed() && this.getText(front).hasRunCommandClickEvent(player);
   }

   public boolean runCommandClickEvent(ServerWorld world, PlayerEntity player, BlockPos pos, boolean front) {
      boolean bl = false;

      for(Text text : this.getText(front).getMessages(player.shouldFilterText())) {
         Style style = text.getStyle();
         ClickEvent clickEvent = style.getClickEvent();
         byte var13 = 0;
         //$FF: var13->value
         //0->net/minecraft/text/ClickEvent$RunCommand
         //1->net/minecraft/text/ClickEvent$ShowDialog
         //2->net/minecraft/text/ClickEvent$Custom
         switch (clickEvent.typeSwitch<invokedynamic>(clickEvent, var13)) {
            case -1:
            default:
               break;
            case 0:
               ClickEvent.RunCommand runCommand = (ClickEvent.RunCommand)clickEvent;
               world.getServer().getCommandManager().executeWithPrefix(createCommandSource(player, world, pos), runCommand.command());
               bl = true;
               break;
            case 1:
               ClickEvent.ShowDialog showDialog = (ClickEvent.ShowDialog)clickEvent;
               player.openDialog(showDialog.dialog());
               bl = true;
               break;
            case 2:
               ClickEvent.Custom custom = (ClickEvent.Custom)clickEvent;
               world.getServer().handleCustomClickAction(custom.id(), custom.payload());
               bl = true;
         }
      }

      return bl;
   }

   private static ServerCommandSource createCommandSource(@Nullable PlayerEntity player, ServerWorld world, BlockPos pos) {
      String string = player == null ? "Sign" : player.getName().getString();
      Text text = (Text)(player == null ? Text.literal("Sign") : player.getDisplayName());
      return new ServerCommandSource(CommandOutput.DUMMY, Vec3d.ofCenter(pos), Vec2f.ZERO, world, 2, string, text, world.getServer(), player);
   }

   public BlockEntityUpdateS2CPacket toUpdatePacket() {
      return BlockEntityUpdateS2CPacket.create(this);
   }

   public NbtCompound toInitialChunkDataNbt(RegistryWrapper.WrapperLookup registries) {
      return this.createComponentlessNbt(registries);
   }

   public void setEditor(@Nullable UUID editor) {
      this.editor = editor;
   }

   @Nullable
   public UUID getEditor() {
      return this.editor;
   }

   private void updateListeners() {
      this.markDirty();
      this.world.updateListeners(this.getPos(), this.getCachedState(), this.getCachedState(), 3);
   }

   public boolean isWaxed() {
      return this.waxed;
   }

   public boolean setWaxed(boolean waxed) {
      if (this.waxed != waxed) {
         this.waxed = waxed;
         this.updateListeners();
         return true;
      } else {
         return false;
      }
   }

   public boolean isPlayerTooFarToEdit(UUID uuid) {
      PlayerEntity playerEntity = this.world.getPlayerByUuid(uuid);
      return playerEntity == null || !playerEntity.canInteractWithBlockAt(this.getPos(), (double)4.0F);
   }

   public static void tick(World world, BlockPos pos, BlockState state, SignBlockEntity blockEntity) {
      UUID uUID = blockEntity.getEditor();
      if (uUID != null) {
         blockEntity.tryClearInvalidEditor(blockEntity, world, uUID);
      }

   }

   private void tryClearInvalidEditor(SignBlockEntity blockEntity, World world, UUID uuid) {
      if (blockEntity.isPlayerTooFarToEdit(uuid)) {
         blockEntity.setEditor((UUID)null);
      }

   }

   public SoundEvent getInteractionFailSound() {
      return SoundEvents.BLOCK_SIGN_WAXED_INTERACT_FAIL;
   }
}
