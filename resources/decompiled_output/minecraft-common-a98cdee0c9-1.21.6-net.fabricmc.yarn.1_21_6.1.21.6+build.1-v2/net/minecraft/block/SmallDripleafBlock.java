package net.minecraft.block;

import com.mojang.serialization.MapCodec;
import net.minecraft.block.enums.DoubleBlockHalf;
import net.minecraft.entity.LivingEntity;
import net.minecraft.fluid.FluidState;
import net.minecraft.fluid.Fluids;
import net.minecraft.item.ItemPlacementContext;
import net.minecraft.item.ItemStack;
import net.minecraft.registry.tag.BlockTags;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.state.StateManager;
import net.minecraft.state.property.BooleanProperty;
import net.minecraft.state.property.EnumProperty;
import net.minecraft.state.property.Properties;
import net.minecraft.util.BlockMirror;
import net.minecraft.util.BlockRotation;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.random.Random;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.world.BlockView;
import net.minecraft.world.World;
import net.minecraft.world.WorldAccess;
import net.minecraft.world.WorldView;
import net.minecraft.world.tick.ScheduledTickView;
import org.jetbrains.annotations.Nullable;

public class SmallDripleafBlock extends TallPlantBlock implements Fertilizable, Waterloggable {
   public static final MapCodec<SmallDripleafBlock> CODEC = createCodec(SmallDripleafBlock::new);
   private static final BooleanProperty WATERLOGGED;
   public static final EnumProperty<Direction> FACING;
   private static final VoxelShape SHAPE;

   public MapCodec<SmallDripleafBlock> getCodec() {
      return CODEC;
   }

   public SmallDripleafBlock(AbstractBlock.Settings settings) {
      super(settings);
      this.setDefaultState((BlockState)((BlockState)((BlockState)((BlockState)this.stateManager.getDefaultState()).with(HALF, DoubleBlockHalf.LOWER)).with(WATERLOGGED, false)).with(FACING, Direction.NORTH));
   }

   protected VoxelShape getOutlineShape(BlockState state, BlockView world, BlockPos pos, ShapeContext context) {
      return SHAPE;
   }

   protected boolean canPlantOnTop(BlockState floor, BlockView world, BlockPos pos) {
      return floor.isIn(BlockTags.SMALL_DRIPLEAF_PLACEABLE) || world.getFluidState(pos.up()).isEqualAndStill(Fluids.WATER) && super.canPlantOnTop(floor, world, pos);
   }

   @Nullable
   public BlockState getPlacementState(ItemPlacementContext ctx) {
      BlockState blockState = super.getPlacementState(ctx);
      return blockState != null ? withWaterloggedState(ctx.getWorld(), ctx.getBlockPos(), (BlockState)blockState.with(FACING, ctx.getHorizontalPlayerFacing().getOpposite())) : null;
   }

   public void onPlaced(World world, BlockPos pos, BlockState state, LivingEntity placer, ItemStack itemStack) {
      if (!world.isClient()) {
         BlockPos blockPos = pos.up();
         BlockState blockState = TallPlantBlock.withWaterloggedState(world, blockPos, (BlockState)((BlockState)this.getDefaultState().with(HALF, DoubleBlockHalf.UPPER)).with(FACING, (Direction)state.get(FACING)));
         world.setBlockState(blockPos, blockState, 3);
      }

   }

   protected FluidState getFluidState(BlockState state) {
      return (Boolean)state.get(WATERLOGGED) ? Fluids.WATER.getStill(false) : super.getFluidState(state);
   }

   protected boolean canPlaceAt(BlockState state, WorldView world, BlockPos pos) {
      if (state.get(HALF) == DoubleBlockHalf.UPPER) {
         return super.canPlaceAt(state, world, pos);
      } else {
         BlockPos blockPos = pos.down();
         BlockState blockState = world.getBlockState(blockPos);
         return this.canPlantOnTop(blockState, world, blockPos);
      }
   }

   protected BlockState getStateForNeighborUpdate(BlockState state, WorldView world, ScheduledTickView tickView, BlockPos pos, Direction direction, BlockPos neighborPos, BlockState neighborState, Random random) {
      if ((Boolean)state.get(WATERLOGGED)) {
         tickView.scheduleFluidTick(pos, Fluids.WATER, Fluids.WATER.getTickRate(world));
      }

      return super.getStateForNeighborUpdate(state, world, tickView, pos, direction, neighborPos, neighborState, random);
   }

   protected void appendProperties(StateManager.Builder<Block, BlockState> builder) {
      builder.add(HALF, WATERLOGGED, FACING);
   }

   public boolean isFertilizable(WorldView world, BlockPos pos, BlockState state) {
      return true;
   }

   public boolean canGrow(World world, Random random, BlockPos pos, BlockState state) {
      return true;
   }

   public void grow(ServerWorld world, Random random, BlockPos pos, BlockState state) {
      if (state.get(TallPlantBlock.HALF) == DoubleBlockHalf.LOWER) {
         BlockPos blockPos = pos.up();
         world.setBlockState(blockPos, world.getFluidState(blockPos).getBlockState(), 18);
         BigDripleafBlock.grow((WorldAccess)world, random, pos, (Direction)((Direction)state.get(FACING)));
      } else {
         BlockPos blockPos = pos.down();
         this.grow(world, random, blockPos, world.getBlockState(blockPos));
      }

   }

   protected BlockState rotate(BlockState state, BlockRotation rotation) {
      return (BlockState)state.with(FACING, rotation.rotate((Direction)state.get(FACING)));
   }

   protected BlockState mirror(BlockState state, BlockMirror mirror) {
      return state.rotate(mirror.getRotation((Direction)state.get(FACING)));
   }

   protected float getVerticalModelOffsetMultiplier() {
      return 0.1F;
   }

   static {
      WATERLOGGED = Properties.WATERLOGGED;
      FACING = Properties.HORIZONTAL_FACING;
      SHAPE = Block.createColumnShape((double)12.0F, (double)0.0F, (double)13.0F);
   }
}
