package net.minecraft.block;

import com.mojang.serialization.MapCodec;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import net.minecraft.block.enums.BlockFace;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.particle.DustParticleEffect;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import net.minecraft.state.StateManager;
import net.minecraft.state.property.BooleanProperty;
import net.minecraft.state.property.Properties;
import net.minecraft.state.property.Property;
import net.minecraft.util.ActionResult;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.random.Random;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.util.shape.VoxelShapes;
import net.minecraft.world.BlockView;
import net.minecraft.world.World;
import net.minecraft.world.WorldAccess;
import net.minecraft.world.block.OrientationHelper;
import net.minecraft.world.block.WireOrientation;
import net.minecraft.world.event.GameEvent;
import net.minecraft.world.explosion.Explosion;
import org.jetbrains.annotations.Nullable;

public class LeverBlock extends WallMountedBlock {
   public static final MapCodec<LeverBlock> CODEC = createCodec(LeverBlock::new);
   public static final BooleanProperty POWERED;
   private final Function<BlockState, VoxelShape> shapeFunction;

   public MapCodec<LeverBlock> getCodec() {
      return CODEC;
   }

   public LeverBlock(AbstractBlock.Settings settings) {
      super(settings);
      this.setDefaultState((BlockState)((BlockState)((BlockState)((BlockState)this.stateManager.getDefaultState()).with(FACING, Direction.NORTH)).with(POWERED, false)).with(FACE, BlockFace.WALL));
      this.shapeFunction = this.createShapeFunction();
   }

   private Function<BlockState, VoxelShape> createShapeFunction() {
      Map<BlockFace, Map<Direction, VoxelShape>> map = VoxelShapes.createBlockFaceHorizontalFacingShapeMap(Block.createCuboidZShape((double)6.0F, (double)8.0F, (double)10.0F, (double)16.0F));
      return this.createShapeFunction((state) -> (VoxelShape)((Map)map.get(state.get(FACE))).get(state.get(FACING)), new Property[]{POWERED});
   }

   protected VoxelShape getOutlineShape(BlockState state, BlockView world, BlockPos pos, ShapeContext context) {
      return (VoxelShape)this.shapeFunction.apply(state);
   }

   protected ActionResult onUse(BlockState state, World world, BlockPos pos, PlayerEntity player, BlockHitResult hit) {
      if (world.isClient) {
         BlockState blockState = (BlockState)state.cycle(POWERED);
         if ((Boolean)blockState.get(POWERED)) {
            spawnParticles(blockState, world, pos, 1.0F);
         }
      } else {
         this.togglePower(state, world, pos, (PlayerEntity)null);
      }

      return ActionResult.SUCCESS;
   }

   protected void onExploded(BlockState state, ServerWorld world, BlockPos pos, Explosion explosion, BiConsumer<ItemStack, BlockPos> stackMerger) {
      if (explosion.canTriggerBlocks()) {
         this.togglePower(state, world, pos, (PlayerEntity)null);
      }

      super.onExploded(state, world, pos, explosion, stackMerger);
   }

   public void togglePower(BlockState state, World world, BlockPos pos, @Nullable PlayerEntity player) {
      state = (BlockState)state.cycle(POWERED);
      world.setBlockState(pos, state, 3);
      this.updateNeighbors(state, world, pos);
      playClickSound(player, world, pos, state);
      world.emitGameEvent(player, (Boolean)state.get(POWERED) ? GameEvent.BLOCK_ACTIVATE : GameEvent.BLOCK_DEACTIVATE, pos);
   }

   protected static void playClickSound(@Nullable PlayerEntity player, WorldAccess world, BlockPos pos, BlockState state) {
      float f = (Boolean)state.get(POWERED) ? 0.6F : 0.5F;
      world.playSound(player, pos, SoundEvents.BLOCK_LEVER_CLICK, SoundCategory.BLOCKS, 0.3F, f);
   }

   private static void spawnParticles(BlockState state, WorldAccess world, BlockPos pos, float alpha) {
      Direction direction = ((Direction)state.get(FACING)).getOpposite();
      Direction direction2 = getDirection(state).getOpposite();
      double d = (double)pos.getX() + (double)0.5F + 0.1 * (double)direction.getOffsetX() + 0.2 * (double)direction2.getOffsetX();
      double e = (double)pos.getY() + (double)0.5F + 0.1 * (double)direction.getOffsetY() + 0.2 * (double)direction2.getOffsetY();
      double f = (double)pos.getZ() + (double)0.5F + 0.1 * (double)direction.getOffsetZ() + 0.2 * (double)direction2.getOffsetZ();
      world.addParticleClient(new DustParticleEffect(16711680, alpha), d, e, f, (double)0.0F, (double)0.0F, (double)0.0F);
   }

   public void randomDisplayTick(BlockState state, World world, BlockPos pos, Random random) {
      if ((Boolean)state.get(POWERED) && random.nextFloat() < 0.25F) {
         spawnParticles(state, world, pos, 0.5F);
      }

   }

   protected void onStateReplaced(BlockState state, ServerWorld world, BlockPos pos, boolean moved) {
      if (!moved && (Boolean)state.get(POWERED)) {
         this.updateNeighbors(state, world, pos);
      }

   }

   protected int getWeakRedstonePower(BlockState state, BlockView world, BlockPos pos, Direction direction) {
      return (Boolean)state.get(POWERED) ? 15 : 0;
   }

   protected int getStrongRedstonePower(BlockState state, BlockView world, BlockPos pos, Direction direction) {
      return (Boolean)state.get(POWERED) && getDirection(state) == direction ? 15 : 0;
   }

   protected boolean emitsRedstonePower(BlockState state) {
      return true;
   }

   private void updateNeighbors(BlockState state, World world, BlockPos pos) {
      Direction direction = getDirection(state).getOpposite();
      WireOrientation wireOrientation = OrientationHelper.getEmissionOrientation(world, direction, direction.getAxis().isHorizontal() ? Direction.UP : (Direction)state.get(FACING));
      world.updateNeighborsAlways(pos, this, wireOrientation);
      world.updateNeighborsAlways(pos.offset(direction), this, wireOrientation);
   }

   protected void appendProperties(StateManager.Builder<Block, BlockState> builder) {
      builder.add(FACE, FACING, POWERED);
   }

   static {
      POWERED = Properties.POWERED;
   }
}
