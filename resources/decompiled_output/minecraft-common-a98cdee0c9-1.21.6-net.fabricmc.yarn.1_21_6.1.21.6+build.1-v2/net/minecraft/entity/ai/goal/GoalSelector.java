package net.minecraft.entity.ai.goal;

import it.unimi.dsi.fastutil.objects.ObjectLinkedOpenHashSet;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import net.minecraft.util.profiler.Profiler;
import net.minecraft.util.profiler.Profilers;

public class GoalSelector {
   private static final PrioritizedGoal REPLACEABLE_GOAL = new PrioritizedGoal(Integer.MAX_VALUE, new Goal() {
      public boolean canStart() {
         return false;
      }
   }) {
      public boolean isRunning() {
         return false;
      }
   };
   private final Map<Goal.Control, PrioritizedGoal> goalsByControl = new EnumMap(Goal.Control.class);
   private final Set<PrioritizedGoal> goals = new ObjectLinkedOpenHashSet();
   private final EnumSet<Goal.Control> disabledControls = EnumSet.noneOf(Goal.Control.class);

   public void add(int priority, Goal goal) {
      this.goals.add(new PrioritizedGoal(priority, goal));
   }

   public void clear(Predicate<Goal> predicate) {
      this.goals.removeIf((goal) -> predicate.test(goal.getGoal()));
   }

   public void remove(Goal goal) {
      for(PrioritizedGoal prioritizedGoal : this.goals) {
         if (prioritizedGoal.getGoal() == goal && prioritizedGoal.isRunning()) {
            prioritizedGoal.stop();
         }
      }

      this.goals.removeIf((prioritizedGoalx) -> prioritizedGoalx.getGoal() == goal);
   }

   private static boolean usesAny(PrioritizedGoal goal, EnumSet<Goal.Control> controls) {
      for(Goal.Control control : goal.getControls()) {
         if (controls.contains(control)) {
            return true;
         }
      }

      return false;
   }

   private static boolean canReplaceAll(PrioritizedGoal goal, Map<Goal.Control, PrioritizedGoal> goalsByControl) {
      for(Goal.Control control : goal.getControls()) {
         if (!((PrioritizedGoal)goalsByControl.getOrDefault(control, REPLACEABLE_GOAL)).canBeReplacedBy(goal)) {
            return false;
         }
      }

      return true;
   }

   public void tick() {
      Profiler profiler = Profilers.get();
      profiler.push("goalCleanup");

      for(PrioritizedGoal prioritizedGoal : this.goals) {
         if (prioritizedGoal.isRunning() && (usesAny(prioritizedGoal, this.disabledControls) || !prioritizedGoal.shouldContinue())) {
            prioritizedGoal.stop();
         }
      }

      this.goalsByControl.entrySet().removeIf((entry) -> !((PrioritizedGoal)entry.getValue()).isRunning());
      profiler.pop();
      profiler.push("goalUpdate");

      for(PrioritizedGoal prioritizedGoal : this.goals) {
         if (!prioritizedGoal.isRunning() && !usesAny(prioritizedGoal, this.disabledControls) && canReplaceAll(prioritizedGoal, this.goalsByControl) && prioritizedGoal.canStart()) {
            for(Goal.Control control : prioritizedGoal.getControls()) {
               PrioritizedGoal prioritizedGoal2 = (PrioritizedGoal)this.goalsByControl.getOrDefault(control, REPLACEABLE_GOAL);
               prioritizedGoal2.stop();
               this.goalsByControl.put(control, prioritizedGoal);
            }

            prioritizedGoal.start();
         }
      }

      profiler.pop();
      this.tickGoals(true);
   }

   public void tickGoals(boolean tickAll) {
      Profiler profiler = Profilers.get();
      profiler.push("goalTick");

      for(PrioritizedGoal prioritizedGoal : this.goals) {
         if (prioritizedGoal.isRunning() && (tickAll || prioritizedGoal.shouldRunEveryTick())) {
            prioritizedGoal.tick();
         }
      }

      profiler.pop();
   }

   public Set<PrioritizedGoal> getGoals() {
      return this.goals;
   }

   public void disableControl(Goal.Control control) {
      this.disabledControls.add(control);
   }

   public void enableControl(Goal.Control control) {
      this.disabledControls.remove(control);
   }

   public void setControlEnabled(Goal.Control control, boolean enabled) {
      if (enabled) {
         this.enableControl(control);
      } else {
         this.disableControl(control);
      }

   }
}
