package net.minecraft.entity.ai.goal;

import java.util.EnumSet;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.pathing.BirdNavigation;
import net.minecraft.entity.ai.pathing.EntityNavigation;
import net.minecraft.entity.ai.pathing.MobNavigation;
import net.minecraft.entity.ai.pathing.PathNodeType;
import net.minecraft.entity.passive.TameableEntity;
import org.jetbrains.annotations.Nullable;

public class FollowOwnerGoal extends Goal {
   private final TameableEntity tameable;
   @Nullable
   private LivingEntity owner;
   private final double speed;
   private final EntityNavigation navigation;
   private int updateCountdownTicks;
   private final float maxDistance;
   private final float minDistance;
   private float oldWaterPathfindingPenalty;

   public FollowOwnerGoal(TameableEntity tameable, double speed, float minDistance, float maxDistance) {
      this.tameable = tameable;
      this.speed = speed;
      this.navigation = tameable.getNavigation();
      this.minDistance = minDistance;
      this.maxDistance = maxDistance;
      this.setControls(EnumSet.of(Goal.Control.MOVE, Goal.Control.LOOK));
      if (!(tameable.getNavigation() instanceof MobNavigation) && !(tameable.getNavigation() instanceof BirdNavigation)) {
         throw new IllegalArgumentException("Unsupported mob type for FollowOwnerGoal");
      }
   }

   public boolean canStart() {
      LivingEntity livingEntity = this.tameable.getOwner();
      if (livingEntity == null) {
         return false;
      } else if (this.tameable.cannotFollowOwner()) {
         return false;
      } else if (this.tameable.squaredDistanceTo(livingEntity) < (double)(this.minDistance * this.minDistance)) {
         return false;
      } else {
         this.owner = livingEntity;
         return true;
      }
   }

   public boolean shouldContinue() {
      if (this.navigation.isIdle()) {
         return false;
      } else if (this.tameable.cannotFollowOwner()) {
         return false;
      } else {
         return !(this.tameable.squaredDistanceTo(this.owner) <= (double)(this.maxDistance * this.maxDistance));
      }
   }

   public void start() {
      this.updateCountdownTicks = 0;
      this.oldWaterPathfindingPenalty = this.tameable.getPathfindingPenalty(PathNodeType.WATER);
      this.tameable.setPathfindingPenalty(PathNodeType.WATER, 0.0F);
   }

   public void stop() {
      this.owner = null;
      this.navigation.stop();
      this.tameable.setPathfindingPenalty(PathNodeType.WATER, this.oldWaterPathfindingPenalty);
   }

   public void tick() {
      boolean bl = this.tameable.shouldTryTeleportToOwner();
      if (!bl) {
         this.tameable.getLookControl().lookAt(this.owner, 10.0F, (float)this.tameable.getMaxLookPitchChange());
      }

      if (--this.updateCountdownTicks <= 0) {
         this.updateCountdownTicks = this.getTickCount(10);
         if (bl) {
            this.tameable.tryTeleportToOwner();
         } else {
            this.navigation.startMovingTo(this.owner, this.speed);
         }

      }
   }
}
