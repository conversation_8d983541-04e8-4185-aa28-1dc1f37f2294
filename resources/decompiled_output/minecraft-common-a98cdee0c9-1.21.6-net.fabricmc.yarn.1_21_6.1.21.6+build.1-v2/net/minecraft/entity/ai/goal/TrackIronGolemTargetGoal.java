package net.minecraft.entity.ai.goal;

import java.util.EnumSet;
import java.util.List;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.TargetPredicate;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.Box;
import org.jetbrains.annotations.Nullable;

public class TrackIronGolemTargetGoal extends TrackTargetGoal {
   private final IronGolemEntity golem;
   @Nullable
   private LivingEntity target;
   private final TargetPredicate targetPredicate = TargetPredicate.createAttackable().setBaseMaxDistance((double)64.0F);

   public TrackIronGolemTargetGoal(IronGolemEntity golem) {
      super(golem, false, true);
      this.golem = golem;
      this.setControls(EnumSet.of(Goal.Control.TARGET));
   }

   public boolean canStart() {
      Box box = this.golem.getBoundingBox().expand((double)10.0F, (double)8.0F, (double)10.0F);
      ServerWorld serverWorld = getServerWorld(this.golem);
      List<? extends LivingEntity> list = serverWorld.getTargets(VillagerEntity.class, this.targetPredicate, this.golem, box);
      List<PlayerEntity> list2 = serverWorld.getPlayers(this.targetPredicate, this.golem, box);

      for(LivingEntity livingEntity : list) {
         VillagerEntity villagerEntity = (VillagerEntity)livingEntity;

         for(PlayerEntity playerEntity : list2) {
            int i = villagerEntity.getReputation(playerEntity);
            if (i <= -100) {
               this.target = playerEntity;
            }
         }
      }

      if (this.target == null) {
         return false;
      } else {
         LivingEntity var12 = this.target;
         if (var12 instanceof PlayerEntity) {
            PlayerEntity playerEntity2 = (PlayerEntity)var12;
            if (playerEntity2.isSpectator() || playerEntity2.isCreative()) {
               return false;
            }
         }

         return true;
      }
   }

   public void start() {
      this.golem.setTarget(this.target);
      super.start();
   }
}
