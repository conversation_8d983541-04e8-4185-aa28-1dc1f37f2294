package net.minecraft.entity.data;

import io.netty.buffer.ByteBuf;
import java.util.List;
import java.util.Optional;
import java.util.OptionalInt;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.entity.EntityPose;
import net.minecraft.entity.LazyEntityReference;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.decoration.painting.PaintingVariant;
import net.minecraft.entity.passive.ArmadilloEntity;
import net.minecraft.entity.passive.CatVariant;
import net.minecraft.entity.passive.ChickenVariant;
import net.minecraft.entity.passive.CowVariant;
import net.minecraft.entity.passive.FrogVariant;
import net.minecraft.entity.passive.PigVariant;
import net.minecraft.entity.passive.SnifferEntity;
import net.minecraft.entity.passive.WolfSoundVariant;
import net.minecraft.entity.passive.WolfVariant;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.network.RegistryByteBuf;
import net.minecraft.network.codec.PacketCodec;
import net.minecraft.network.codec.PacketCodecs;
import net.minecraft.network.encoding.VarInts;
import net.minecraft.particle.ParticleEffect;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.text.Text;
import net.minecraft.text.TextCodecs;
import net.minecraft.util.collection.Int2ObjectBiMap;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.EulerAngle;
import net.minecraft.util.math.GlobalPos;
import net.minecraft.village.VillagerData;
import org.jetbrains.annotations.Nullable;
import org.joml.Quaternionf;
import org.joml.Vector3f;

public class TrackedDataHandlerRegistry {
   private static final Int2ObjectBiMap<TrackedDataHandler<?>> DATA_HANDLERS = Int2ObjectBiMap.<TrackedDataHandler<?>>create(16);
   public static final TrackedDataHandler<Byte> BYTE;
   public static final TrackedDataHandler<Integer> INTEGER;
   public static final TrackedDataHandler<Long> LONG;
   public static final TrackedDataHandler<Float> FLOAT;
   public static final TrackedDataHandler<String> STRING;
   public static final TrackedDataHandler<Text> TEXT_COMPONENT;
   public static final TrackedDataHandler<Optional<Text>> OPTIONAL_TEXT_COMPONENT;
   public static final TrackedDataHandler<ItemStack> ITEM_STACK;
   public static final TrackedDataHandler<BlockState> BLOCK_STATE;
   private static final PacketCodec<ByteBuf, Optional<BlockState>> OPTIONAL_BLOCK_STATE_CODEC;
   public static final TrackedDataHandler<Optional<BlockState>> OPTIONAL_BLOCK_STATE;
   public static final TrackedDataHandler<Boolean> BOOLEAN;
   public static final TrackedDataHandler<ParticleEffect> PARTICLE;
   public static final TrackedDataHandler<List<ParticleEffect>> PARTICLE_LIST;
   public static final TrackedDataHandler<EulerAngle> ROTATION;
   public static final TrackedDataHandler<BlockPos> BLOCK_POS;
   public static final TrackedDataHandler<Optional<BlockPos>> OPTIONAL_BLOCK_POS;
   public static final TrackedDataHandler<Direction> FACING;
   public static final TrackedDataHandler<Optional<LazyEntityReference<LivingEntity>>> LAZY_ENTITY_REFERENCE;
   public static final TrackedDataHandler<Optional<GlobalPos>> OPTIONAL_GLOBAL_POS;
   public static final TrackedDataHandler<NbtCompound> NBT_COMPOUND;
   public static final TrackedDataHandler<VillagerData> VILLAGER_DATA;
   private static final PacketCodec<ByteBuf, OptionalInt> OPTIONAL_INT_CODEC;
   public static final TrackedDataHandler<OptionalInt> OPTIONAL_INT;
   public static final TrackedDataHandler<EntityPose> ENTITY_POSE;
   public static final TrackedDataHandler<RegistryEntry<CatVariant>> CAT_VARIANT;
   public static final TrackedDataHandler<RegistryEntry<ChickenVariant>> CHICKEN_VARIANT;
   public static final TrackedDataHandler<RegistryEntry<CowVariant>> COW_VARIANT;
   public static final TrackedDataHandler<RegistryEntry<WolfVariant>> WOLF_VARIANT;
   public static final TrackedDataHandler<RegistryEntry<WolfSoundVariant>> WOLF_SOUND_VARIANT;
   public static final TrackedDataHandler<RegistryEntry<FrogVariant>> FROG_VARIANT;
   public static final TrackedDataHandler<RegistryEntry<PigVariant>> PIG_VARIANT;
   public static final TrackedDataHandler<RegistryEntry<PaintingVariant>> PAINTING_VARIANT;
   public static final TrackedDataHandler<ArmadilloEntity.State> ARMADILLO_STATE;
   public static final TrackedDataHandler<SnifferEntity.State> SNIFFER_STATE;
   public static final TrackedDataHandler<Vector3f> VECTOR_3F;
   public static final TrackedDataHandler<Quaternionf> QUATERNION_F;

   public static void register(TrackedDataHandler<?> handler) {
      DATA_HANDLERS.add(handler);
   }

   @Nullable
   public static TrackedDataHandler<?> get(int id) {
      return DATA_HANDLERS.get(id);
   }

   public static int getId(TrackedDataHandler<?> handler) {
      return DATA_HANDLERS.getRawId(handler);
   }

   private TrackedDataHandlerRegistry() {
   }

   static {
      BYTE = TrackedDataHandler.<Byte>create(PacketCodecs.BYTE);
      INTEGER = TrackedDataHandler.<Integer>create(PacketCodecs.VAR_INT);
      LONG = TrackedDataHandler.<Long>create(PacketCodecs.VAR_LONG);
      FLOAT = TrackedDataHandler.<Float>create(PacketCodecs.FLOAT);
      STRING = TrackedDataHandler.<String>create(PacketCodecs.STRING);
      TEXT_COMPONENT = TrackedDataHandler.<Text>create(TextCodecs.UNLIMITED_REGISTRY_PACKET_CODEC);
      OPTIONAL_TEXT_COMPONENT = TrackedDataHandler.<Optional<Text>>create(TextCodecs.OPTIONAL_UNLIMITED_REGISTRY_PACKET_CODEC);
      ITEM_STACK = new TrackedDataHandler<ItemStack>() {
         public PacketCodec<? super RegistryByteBuf, ItemStack> codec() {
            return ItemStack.OPTIONAL_PACKET_CODEC;
         }

         public ItemStack copy(ItemStack itemStack) {
            return itemStack.copy();
         }
      };
      BLOCK_STATE = TrackedDataHandler.<BlockState>create(PacketCodecs.entryOf(Block.STATE_IDS));
      OPTIONAL_BLOCK_STATE_CODEC = new PacketCodec<ByteBuf, Optional<BlockState>>() {
         public void encode(ByteBuf byteBuf, Optional<BlockState> optional) {
            if (optional.isPresent()) {
               VarInts.write(byteBuf, Block.getRawIdFromState((BlockState)optional.get()));
            } else {
               VarInts.write(byteBuf, 0);
            }

         }

         public Optional<BlockState> decode(ByteBuf byteBuf) {
            int i = VarInts.read(byteBuf);
            return i == 0 ? Optional.empty() : Optional.of(Block.getStateFromRawId(i));
         }
      };
      OPTIONAL_BLOCK_STATE = TrackedDataHandler.<Optional<BlockState>>create(OPTIONAL_BLOCK_STATE_CODEC);
      BOOLEAN = TrackedDataHandler.<Boolean>create(PacketCodecs.BOOLEAN);
      PARTICLE = TrackedDataHandler.<ParticleEffect>create(ParticleTypes.PACKET_CODEC);
      PARTICLE_LIST = TrackedDataHandler.<List<ParticleEffect>>create(ParticleTypes.PACKET_CODEC.collect(PacketCodecs.toList()));
      ROTATION = TrackedDataHandler.<EulerAngle>create(EulerAngle.PACKET_CODEC);
      BLOCK_POS = TrackedDataHandler.<BlockPos>create(BlockPos.PACKET_CODEC);
      OPTIONAL_BLOCK_POS = TrackedDataHandler.<Optional<BlockPos>>create(BlockPos.PACKET_CODEC.collect(PacketCodecs::optional));
      FACING = TrackedDataHandler.<Direction>create(Direction.PACKET_CODEC);
      LAZY_ENTITY_REFERENCE = TrackedDataHandler.<Optional<LazyEntityReference<LivingEntity>>>create(LazyEntityReference.createPacketCodec().collect(PacketCodecs::optional));
      OPTIONAL_GLOBAL_POS = TrackedDataHandler.<Optional<GlobalPos>>create(GlobalPos.PACKET_CODEC.collect(PacketCodecs::optional));
      NBT_COMPOUND = new TrackedDataHandler<NbtCompound>() {
         public PacketCodec<? super RegistryByteBuf, NbtCompound> codec() {
            return PacketCodecs.UNLIMITED_NBT_COMPOUND;
         }

         public NbtCompound copy(NbtCompound nbtCompound) {
            return nbtCompound.copy();
         }
      };
      VILLAGER_DATA = TrackedDataHandler.<VillagerData>create(VillagerData.PACKET_CODEC);
      OPTIONAL_INT_CODEC = new PacketCodec<ByteBuf, OptionalInt>() {
         public OptionalInt decode(ByteBuf byteBuf) {
            int i = VarInts.read(byteBuf);
            return i == 0 ? OptionalInt.empty() : OptionalInt.of(i - 1);
         }

         public void encode(ByteBuf byteBuf, OptionalInt optionalInt) {
            VarInts.write(byteBuf, optionalInt.orElse(-1) + 1);
         }
      };
      OPTIONAL_INT = TrackedDataHandler.<OptionalInt>create(OPTIONAL_INT_CODEC);
      ENTITY_POSE = TrackedDataHandler.<EntityPose>create(EntityPose.PACKET_CODEC);
      CAT_VARIANT = TrackedDataHandler.<RegistryEntry<CatVariant>>create(CatVariant.PACKET_CODEC);
      CHICKEN_VARIANT = TrackedDataHandler.<RegistryEntry<ChickenVariant>>create(ChickenVariant.ENTRY_PACKET_CODEC);
      COW_VARIANT = TrackedDataHandler.<RegistryEntry<CowVariant>>create(CowVariant.ENTRY_PACKET_CODEC);
      WOLF_VARIANT = TrackedDataHandler.<RegistryEntry<WolfVariant>>create(WolfVariant.ENTRY_PACKET_CODEC);
      WOLF_SOUND_VARIANT = TrackedDataHandler.<RegistryEntry<WolfSoundVariant>>create(WolfSoundVariant.PACKET_CODEC);
      FROG_VARIANT = TrackedDataHandler.<RegistryEntry<FrogVariant>>create(FrogVariant.PACKET_CODEC);
      PIG_VARIANT = TrackedDataHandler.<RegistryEntry<PigVariant>>create(PigVariant.ENTRY_PACKET_CODEC);
      PAINTING_VARIANT = TrackedDataHandler.<RegistryEntry<PaintingVariant>>create(PaintingVariant.ENTRY_PACKET_CODEC);
      ARMADILLO_STATE = TrackedDataHandler.<ArmadilloEntity.State>create(ArmadilloEntity.State.PACKET_CODEC);
      SNIFFER_STATE = TrackedDataHandler.<SnifferEntity.State>create(SnifferEntity.State.PACKET_CODEC);
      VECTOR_3F = TrackedDataHandler.<Vector3f>create(PacketCodecs.VECTOR_3F);
      QUATERNION_F = TrackedDataHandler.<Quaternionf>create(PacketCodecs.QUATERNION_F);
      register(BYTE);
      register(INTEGER);
      register(LONG);
      register(FLOAT);
      register(STRING);
      register(TEXT_COMPONENT);
      register(OPTIONAL_TEXT_COMPONENT);
      register(ITEM_STACK);
      register(BOOLEAN);
      register(ROTATION);
      register(BLOCK_POS);
      register(OPTIONAL_BLOCK_POS);
      register(FACING);
      register(LAZY_ENTITY_REFERENCE);
      register(BLOCK_STATE);
      register(OPTIONAL_BLOCK_STATE);
      register(NBT_COMPOUND);
      register(PARTICLE);
      register(PARTICLE_LIST);
      register(VILLAGER_DATA);
      register(OPTIONAL_INT);
      register(ENTITY_POSE);
      register(CAT_VARIANT);
      register(COW_VARIANT);
      register(WOLF_VARIANT);
      register(WOLF_SOUND_VARIANT);
      register(FROG_VARIANT);
      register(PIG_VARIANT);
      register(CHICKEN_VARIANT);
      register(OPTIONAL_GLOBAL_POS);
      register(PAINTING_VARIANT);
      register(SNIFFER_STATE);
      register(ARMADILLO_STATE);
      register(VECTOR_3F);
      register(QUATERNION_F);
   }
}
