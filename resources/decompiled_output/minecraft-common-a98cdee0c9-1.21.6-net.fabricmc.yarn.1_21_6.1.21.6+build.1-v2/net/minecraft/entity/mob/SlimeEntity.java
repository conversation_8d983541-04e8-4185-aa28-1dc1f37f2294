package net.minecraft.entity.mob;

import com.google.common.annotations.VisibleForTesting;
import java.util.EnumSet;
import net.minecraft.enchantment.EnchantmentHelper;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityData;
import net.minecraft.entity.EntityDimensions;
import net.minecraft.entity.EntityPose;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.SpawnReason;
import net.minecraft.entity.ai.control.MoveControl;
import net.minecraft.entity.ai.goal.ActiveTargetGoal;
import net.minecraft.entity.ai.goal.Goal;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.conversion.EntityConversionContext;
import net.minecraft.entity.conversion.EntityConversionType;
import net.minecraft.entity.damage.DamageSource;
import net.minecraft.entity.data.DataTracker;
import net.minecraft.entity.data.TrackedData;
import net.minecraft.entity.data.TrackedDataHandlerRegistry;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.particle.ParticleEffect;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.registry.tag.BiomeTags;
import net.minecraft.scoreboard.Team;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvent;
import net.minecraft.sound.SoundEvents;
import net.minecraft.storage.ReadView;
import net.minecraft.storage.WriteView;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.random.ChunkRandom;
import net.minecraft.util.math.random.Random;
import net.minecraft.world.Difficulty;
import net.minecraft.world.LocalDifficulty;
import net.minecraft.world.ServerWorldAccess;
import net.minecraft.world.StructureWorldAccess;
import net.minecraft.world.World;
import net.minecraft.world.WorldAccess;
import org.jetbrains.annotations.Nullable;

public class SlimeEntity extends MobEntity implements Monster {
   private static final TrackedData<Integer> SLIME_SIZE;
   public static final int MIN_SIZE = 1;
   public static final int MAX_SIZE = 127;
   public static final int field_50136 = 4;
   private static final boolean DEFAULT_ON_GROUND_LAST_TICK = false;
   public float targetStretch;
   public float stretch;
   public float lastStretch;
   private boolean onGroundLastTick = false;

   public SlimeEntity(EntityType<? extends SlimeEntity> entityType, World world) {
      super(entityType, world);
      this.reinitDimensions();
      this.moveControl = new SlimeMoveControl(this);
   }

   protected void initGoals() {
      this.goalSelector.add(1, new SwimmingGoal(this));
      this.goalSelector.add(2, new FaceTowardTargetGoal(this));
      this.goalSelector.add(3, new RandomLookGoal(this));
      this.goalSelector.add(5, new MoveGoal(this));
      this.targetSelector.add(1, new ActiveTargetGoal(this, PlayerEntity.class, 10, true, false, (target, world) -> Math.abs(target.getY() - this.getY()) <= (double)4.0F));
      this.targetSelector.add(3, new ActiveTargetGoal(this, IronGolemEntity.class, true));
   }

   public SoundCategory getSoundCategory() {
      return SoundCategory.HOSTILE;
   }

   protected void initDataTracker(DataTracker.Builder builder) {
      super.initDataTracker(builder);
      builder.add(SLIME_SIZE, 1);
   }

   @VisibleForTesting
   public void setSize(int size, boolean heal) {
      int i = MathHelper.clamp(size, 1, 127);
      this.dataTracker.set(SLIME_SIZE, i);
      this.refreshPosition();
      this.calculateDimensions();
      this.getAttributeInstance(EntityAttributes.MAX_HEALTH).setBaseValue((double)(i * i));
      this.getAttributeInstance(EntityAttributes.MOVEMENT_SPEED).setBaseValue((double)(0.2F + 0.1F * (float)i));
      this.getAttributeInstance(EntityAttributes.ATTACK_DAMAGE).setBaseValue((double)i);
      if (heal) {
         this.setHealth(this.getMaxHealth());
      }

      this.experiencePoints = i;
   }

   public int getSize() {
      return (Integer)this.dataTracker.get(SLIME_SIZE);
   }

   protected void writeCustomData(WriteView view) {
      super.writeCustomData(view);
      view.putInt("Size", this.getSize() - 1);
      view.putBoolean("wasOnGround", this.onGroundLastTick);
   }

   protected void readCustomData(ReadView view) {
      this.setSize(view.getInt("Size", 0) + 1, false);
      super.readCustomData(view);
      this.onGroundLastTick = view.getBoolean("wasOnGround", false);
   }

   public boolean isSmall() {
      return this.getSize() <= 1;
   }

   protected ParticleEffect getParticles() {
      return ParticleTypes.ITEM_SLIME;
   }

   protected boolean isDisallowedInPeaceful() {
      return this.getSize() > 0;
   }

   public void tick() {
      this.lastStretch = this.stretch;
      this.stretch += (this.targetStretch - this.stretch) * 0.5F;
      super.tick();
      if (this.isOnGround() && !this.onGroundLastTick) {
         float f = this.getDimensions(this.getPose()).width() * 2.0F;
         float g = f / 2.0F;

         for(int i = 0; (float)i < f * 16.0F; ++i) {
            float h = this.random.nextFloat() * ((float)Math.PI * 2F);
            float j = this.random.nextFloat() * 0.5F + 0.5F;
            float k = MathHelper.sin(h) * g * j;
            float l = MathHelper.cos(h) * g * j;
            this.getWorld().addParticleClient(this.getParticles(), this.getX() + (double)k, this.getY(), this.getZ() + (double)l, (double)0.0F, (double)0.0F, (double)0.0F);
         }

         this.playSound(this.getSquishSound(), this.getSoundVolume(), ((this.random.nextFloat() - this.random.nextFloat()) * 0.2F + 1.0F) / 0.8F);
         this.targetStretch = -0.5F;
      } else if (!this.isOnGround() && this.onGroundLastTick) {
         this.targetStretch = 1.0F;
      }

      this.onGroundLastTick = this.isOnGround();
      this.updateStretch();
   }

   protected void updateStretch() {
      this.targetStretch *= 0.6F;
   }

   protected int getTicksUntilNextJump() {
      return this.random.nextInt(20) + 10;
   }

   public void calculateDimensions() {
      double d = this.getX();
      double e = this.getY();
      double f = this.getZ();
      super.calculateDimensions();
      this.setPosition(d, e, f);
   }

   public void onTrackedDataSet(TrackedData<?> data) {
      if (SLIME_SIZE.equals(data)) {
         this.calculateDimensions();
         this.setYaw(this.headYaw);
         this.bodyYaw = this.headYaw;
         if (this.isTouchingWater() && this.random.nextInt(20) == 0) {
            this.onSwimmingStart();
         }
      }

      super.onTrackedDataSet(data);
   }

   public EntityType<? extends SlimeEntity> getType() {
      return super.getType();
   }

   public void remove(Entity.RemovalReason reason) {
      int i = this.getSize();
      if (!this.getWorld().isClient && i > 1 && this.isDead()) {
         float f = this.getDimensions(this.getPose()).width();
         float g = f / 2.0F;
         int j = i / 2;
         int k = 2 + this.random.nextInt(3);
         Team team = this.getScoreboardTeam();

         for(int l = 0; l < k; ++l) {
            float h = ((float)(l % 2) - 0.5F) * g;
            float m = ((float)(l / 2) - 0.5F) * g;
            this.convertTo(this.getType(), new EntityConversionContext(EntityConversionType.SPLIT_ON_DEATH, false, false, team), SpawnReason.TRIGGERED, (newSlime) -> {
               newSlime.setSize(j, true);
               newSlime.refreshPositionAndAngles(this.getX() + (double)h, this.getY() + (double)0.5F, this.getZ() + (double)m, this.random.nextFloat() * 360.0F, 0.0F);
            });
         }
      }

      super.remove(reason);
   }

   public void pushAwayFrom(Entity entity) {
      super.pushAwayFrom(entity);
      if (entity instanceof IronGolemEntity && this.canAttack()) {
         this.damage((LivingEntity)entity);
      }

   }

   public void onPlayerCollision(PlayerEntity player) {
      if (this.canAttack()) {
         this.damage(player);
      }

   }

   protected void damage(LivingEntity target) {
      World var3 = this.getWorld();
      if (var3 instanceof ServerWorld serverWorld) {
         if (this.isAlive() && this.isInAttackRange(target) && this.canSee(target)) {
            DamageSource damageSource = this.getDamageSources().mobAttack(this);
            if (target.damage(serverWorld, damageSource, this.getDamageAmount())) {
               this.playSound(SoundEvents.ENTITY_SLIME_ATTACK, 1.0F, (this.random.nextFloat() - this.random.nextFloat()) * 0.2F + 1.0F);
               EnchantmentHelper.onTargetDamaged(serverWorld, target, damageSource);
            }
         }
      }

   }

   protected Vec3d getPassengerAttachmentPos(Entity passenger, EntityDimensions dimensions, float scaleFactor) {
      return new Vec3d((double)0.0F, (double)dimensions.height() - (double)0.015625F * (double)this.getSize() * (double)scaleFactor, (double)0.0F);
   }

   protected boolean canAttack() {
      return !this.isSmall() && this.canActVoluntarily();
   }

   protected float getDamageAmount() {
      return (float)this.getAttributeValue(EntityAttributes.ATTACK_DAMAGE);
   }

   protected SoundEvent getHurtSound(DamageSource source) {
      return this.isSmall() ? SoundEvents.ENTITY_SLIME_HURT_SMALL : SoundEvents.ENTITY_SLIME_HURT;
   }

   protected SoundEvent getDeathSound() {
      return this.isSmall() ? SoundEvents.ENTITY_SLIME_DEATH_SMALL : SoundEvents.ENTITY_SLIME_DEATH;
   }

   protected SoundEvent getSquishSound() {
      return this.isSmall() ? SoundEvents.ENTITY_SLIME_SQUISH_SMALL : SoundEvents.ENTITY_SLIME_SQUISH;
   }

   public static boolean canSpawn(EntityType<SlimeEntity> type, WorldAccess world, SpawnReason spawnReason, BlockPos pos, Random random) {
      if (world.getDifficulty() != Difficulty.PEACEFUL) {
         if (SpawnReason.isAnySpawner(spawnReason)) {
            return canMobSpawn(type, world, spawnReason, pos, random);
         }

         if (world.getBiome(pos).isIn(BiomeTags.ALLOWS_SURFACE_SLIME_SPAWNS) && pos.getY() > 50 && pos.getY() < 70 && random.nextFloat() < 0.5F && random.nextFloat() < world.getMoonSize() && world.getLightLevel(pos) <= random.nextInt(8)) {
            return canMobSpawn(type, world, spawnReason, pos, random);
         }

         if (!(world instanceof StructureWorldAccess)) {
            return false;
         }

         ChunkPos chunkPos = new ChunkPos(pos);
         boolean bl = ChunkRandom.getSlimeRandom(chunkPos.x, chunkPos.z, ((StructureWorldAccess)world).getSeed(), 987234911L).nextInt(10) == 0;
         if (random.nextInt(10) == 0 && bl && pos.getY() < 40) {
            return canMobSpawn(type, world, spawnReason, pos, random);
         }
      }

      return false;
   }

   protected float getSoundVolume() {
      return 0.4F * (float)this.getSize();
   }

   public int getMaxLookPitchChange() {
      return 0;
   }

   protected boolean makesJumpSound() {
      return this.getSize() > 0;
   }

   public void jump() {
      Vec3d vec3d = this.getVelocity();
      this.setVelocity(vec3d.x, (double)this.getJumpVelocity(), vec3d.z);
      this.velocityDirty = true;
   }

   @Nullable
   public EntityData initialize(ServerWorldAccess world, LocalDifficulty difficulty, SpawnReason spawnReason, @Nullable EntityData entityData) {
      Random random = world.getRandom();
      int i = random.nextInt(3);
      if (i < 2 && random.nextFloat() < 0.5F * difficulty.getClampedLocalDifficulty()) {
         ++i;
      }

      int j = 1 << i;
      this.setSize(j, true);
      return super.initialize(world, difficulty, spawnReason, entityData);
   }

   float getJumpSoundPitch() {
      float f = this.isSmall() ? 1.4F : 0.8F;
      return ((this.random.nextFloat() - this.random.nextFloat()) * 0.2F + 1.0F) * f;
   }

   protected SoundEvent getJumpSound() {
      return this.isSmall() ? SoundEvents.ENTITY_SLIME_JUMP_SMALL : SoundEvents.ENTITY_SLIME_JUMP;
   }

   public EntityDimensions getBaseDimensions(EntityPose pose) {
      return super.getBaseDimensions(pose).scaled((float)this.getSize());
   }

   static {
      SLIME_SIZE = DataTracker.<Integer>registerData(SlimeEntity.class, TrackedDataHandlerRegistry.INTEGER);
   }

   static class SlimeMoveControl extends MoveControl {
      private float targetYaw;
      private int ticksUntilJump;
      private final SlimeEntity slime;
      private boolean jumpOften;

      public SlimeMoveControl(SlimeEntity slime) {
         super(slime);
         this.slime = slime;
         this.targetYaw = 180.0F * slime.getYaw() / (float)Math.PI;
      }

      public void look(float targetYaw, boolean jumpOften) {
         this.targetYaw = targetYaw;
         this.jumpOften = jumpOften;
      }

      public void move(double speed) {
         this.speed = speed;
         this.state = MoveControl.State.MOVE_TO;
      }

      public void tick() {
         this.entity.setYaw(this.wrapDegrees(this.entity.getYaw(), this.targetYaw, 90.0F));
         this.entity.headYaw = this.entity.getYaw();
         this.entity.bodyYaw = this.entity.getYaw();
         if (this.state != MoveControl.State.MOVE_TO) {
            this.entity.setForwardSpeed(0.0F);
         } else {
            this.state = MoveControl.State.WAIT;
            if (this.entity.isOnGround()) {
               this.entity.setMovementSpeed((float)(this.speed * this.entity.getAttributeValue(EntityAttributes.MOVEMENT_SPEED)));
               if (this.ticksUntilJump-- <= 0) {
                  this.ticksUntilJump = this.slime.getTicksUntilNextJump();
                  if (this.jumpOften) {
                     this.ticksUntilJump /= 3;
                  }

                  this.slime.getJumpControl().setActive();
                  if (this.slime.makesJumpSound()) {
                     this.slime.playSound(this.slime.getJumpSound(), this.slime.getSoundVolume(), this.slime.getJumpSoundPitch());
                  }
               } else {
                  this.slime.sidewaysSpeed = 0.0F;
                  this.slime.forwardSpeed = 0.0F;
                  this.entity.setMovementSpeed(0.0F);
               }
            } else {
               this.entity.setMovementSpeed((float)(this.speed * this.entity.getAttributeValue(EntityAttributes.MOVEMENT_SPEED)));
            }

         }
      }
   }

   static class FaceTowardTargetGoal extends Goal {
      private final SlimeEntity slime;
      private int ticksLeft;

      public FaceTowardTargetGoal(SlimeEntity slime) {
         this.slime = slime;
         this.setControls(EnumSet.of(Goal.Control.LOOK));
      }

      public boolean canStart() {
         LivingEntity livingEntity = this.slime.getTarget();
         if (livingEntity == null) {
            return false;
         } else {
            return !this.slime.canTarget(livingEntity) ? false : this.slime.getMoveControl() instanceof SlimeMoveControl;
         }
      }

      public void start() {
         this.ticksLeft = toGoalTicks(300);
         super.start();
      }

      public boolean shouldContinue() {
         LivingEntity livingEntity = this.slime.getTarget();
         if (livingEntity == null) {
            return false;
         } else if (!this.slime.canTarget(livingEntity)) {
            return false;
         } else {
            return --this.ticksLeft > 0;
         }
      }

      public boolean shouldRunEveryTick() {
         return true;
      }

      public void tick() {
         LivingEntity livingEntity = this.slime.getTarget();
         if (livingEntity != null) {
            this.slime.lookAtEntity(livingEntity, 10.0F, 10.0F);
         }

         MoveControl var3 = this.slime.getMoveControl();
         if (var3 instanceof SlimeMoveControl slimeMoveControl) {
            slimeMoveControl.look(this.slime.getYaw(), this.slime.canAttack());
         }

      }
   }

   static class RandomLookGoal extends Goal {
      private final SlimeEntity slime;
      private float targetYaw;
      private int timer;

      public RandomLookGoal(SlimeEntity slime) {
         this.slime = slime;
         this.setControls(EnumSet.of(Goal.Control.LOOK));
      }

      public boolean canStart() {
         return this.slime.getTarget() == null && (this.slime.isOnGround() || this.slime.isTouchingWater() || this.slime.isInLava() || this.slime.hasStatusEffect(StatusEffects.LEVITATION)) && this.slime.getMoveControl() instanceof SlimeMoveControl;
      }

      public void tick() {
         if (--this.timer <= 0) {
            this.timer = this.getTickCount(40 + this.slime.getRandom().nextInt(60));
            this.targetYaw = (float)this.slime.getRandom().nextInt(360);
         }

         MoveControl var2 = this.slime.getMoveControl();
         if (var2 instanceof SlimeMoveControl slimeMoveControl) {
            slimeMoveControl.look(this.targetYaw, false);
         }

      }
   }

   static class SwimmingGoal extends Goal {
      private final SlimeEntity slime;

      public SwimmingGoal(SlimeEntity slime) {
         this.slime = slime;
         this.setControls(EnumSet.of(Goal.Control.JUMP, Goal.Control.MOVE));
         slime.getNavigation().setCanSwim(true);
      }

      public boolean canStart() {
         return (this.slime.isTouchingWater() || this.slime.isInLava()) && this.slime.getMoveControl() instanceof SlimeMoveControl;
      }

      public boolean shouldRunEveryTick() {
         return true;
      }

      public void tick() {
         if (this.slime.getRandom().nextFloat() < 0.8F) {
            this.slime.getJumpControl().setActive();
         }

         MoveControl var2 = this.slime.getMoveControl();
         if (var2 instanceof SlimeMoveControl slimeMoveControl) {
            slimeMoveControl.move(1.2);
         }

      }
   }

   static class MoveGoal extends Goal {
      private final SlimeEntity slime;

      public MoveGoal(SlimeEntity slime) {
         this.slime = slime;
         this.setControls(EnumSet.of(Goal.Control.JUMP, Goal.Control.MOVE));
      }

      public boolean canStart() {
         return !this.slime.hasVehicle();
      }

      public void tick() {
         MoveControl var2 = this.slime.getMoveControl();
         if (var2 instanceof SlimeMoveControl slimeMoveControl) {
            slimeMoveControl.move((double)1.0F);
         }

      }
   }
}
