package net.minecraft.entity.mob;

import com.mojang.serialization.Dynamic;
import java.util.List;
import java.util.Optional;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.block.CreakingHeartBlock;
import net.minecraft.block.entity.BlockEntity;
import net.minecraft.block.entity.CreakingHeartBlockEntity;
import net.minecraft.block.enums.CreakingHeartState;
import net.minecraft.entity.AnimationState;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.brain.Brain;
import net.minecraft.entity.ai.brain.MemoryModuleType;
import net.minecraft.entity.ai.control.BodyControl;
import net.minecraft.entity.ai.control.JumpControl;
import net.minecraft.entity.ai.control.LookControl;
import net.minecraft.entity.ai.control.MoveControl;
import net.minecraft.entity.ai.pathing.EntityNavigation;
import net.minecraft.entity.ai.pathing.LandPathNodeMaker;
import net.minecraft.entity.ai.pathing.MobNavigation;
import net.minecraft.entity.ai.pathing.PathContext;
import net.minecraft.entity.ai.pathing.PathNodeNavigator;
import net.minecraft.entity.ai.pathing.PathNodeType;
import net.minecraft.entity.attribute.DefaultAttributeContainer;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.damage.DamageSource;
import net.minecraft.entity.data.DataTracker;
import net.minecraft.entity.data.TrackedData;
import net.minecraft.entity.data.TrackedDataHandlerRegistry;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.projectile.ProjectileEntity;
import net.minecraft.particle.BlockStateParticleEffect;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.registry.tag.DamageTypeTags;
import net.minecraft.server.network.DebugInfoSender;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundEvent;
import net.minecraft.sound.SoundEvents;
import net.minecraft.storage.ReadView;
import net.minecraft.storage.WriteView;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.Vec3i;
import net.minecraft.util.profiler.Profiler;
import net.minecraft.util.profiler.Profilers;
import net.minecraft.world.World;
import net.minecraft.world.WorldView;
import net.minecraft.world.event.GameEvent;
import org.jetbrains.annotations.Nullable;

public class CreakingEntity extends HostileEntity {
   private static final TrackedData<Boolean> UNROOTED;
   private static final TrackedData<Boolean> ACTIVE;
   private static final TrackedData<Boolean> CRUMBLING;
   private static final TrackedData<Optional<BlockPos>> HOME_POS;
   private static final int field_54573 = 15;
   private static final int field_54574 = 1;
   private static final float ATTACK_DAMAGE = 3.0F;
   private static final float field_54576 = 32.0F;
   private static final float field_54577 = 144.0F;
   public static final int field_54566 = 40;
   private static final float field_54578 = 0.4F;
   public static final float field_54567 = 0.3F;
   public static final int field_54569 = 16545810;
   public static final int field_54580 = 6250335;
   public static final int field_55485 = 8;
   public static final int field_55486 = 45;
   private static final int field_55488 = 4;
   private int attackAnimationTimer;
   public final AnimationState attackAnimationState = new AnimationState();
   public final AnimationState invulnerableAnimationState = new AnimationState();
   public final AnimationState crumblingAnimationState = new AnimationState();
   private int invulnerableAnimationTimer;
   private boolean glowingEyesWhileCrumbling;
   private int nextEyeFlickerTime;
   private int playerIntersectionTimer;

   public CreakingEntity(EntityType<? extends CreakingEntity> entityType, World world) {
      super(entityType, world);
      this.lookControl = new CreakingLookControl(this);
      this.moveControl = new CreakingMoveControl(this);
      this.jumpControl = new CreakingJumpControl(this);
      MobNavigation mobNavigation = (MobNavigation)this.getNavigation();
      mobNavigation.setCanSwim(true);
      this.experiencePoints = 0;
   }

   public void initHomePos(BlockPos homePos) {
      this.setHomePos(homePos);
      this.setPathfindingPenalty(PathNodeType.DAMAGE_OTHER, 8.0F);
      this.setPathfindingPenalty(PathNodeType.POWDER_SNOW, 8.0F);
      this.setPathfindingPenalty(PathNodeType.LAVA, 8.0F);
      this.setPathfindingPenalty(PathNodeType.DAMAGE_FIRE, 0.0F);
      this.setPathfindingPenalty(PathNodeType.DANGER_FIRE, 0.0F);
   }

   public boolean isTransient() {
      return this.getHomePos() != null;
   }

   protected BodyControl createBodyControl() {
      return new CreakingBodyControl(this);
   }

   protected Brain.Profile<CreakingEntity> createBrainProfile() {
      return CreakingBrain.createBrainProfile();
   }

   protected Brain<?> deserializeBrain(Dynamic<?> dynamic) {
      return CreakingBrain.create(this, this.createBrainProfile().deserialize(dynamic));
   }

   protected void initDataTracker(DataTracker.Builder builder) {
      super.initDataTracker(builder);
      builder.add(UNROOTED, true);
      builder.add(ACTIVE, false);
      builder.add(CRUMBLING, false);
      builder.add(HOME_POS, Optional.empty());
   }

   public static DefaultAttributeContainer.Builder createCreakingAttributes() {
      return HostileEntity.createHostileAttributes().add(EntityAttributes.MAX_HEALTH, (double)1.0F).add(EntityAttributes.MOVEMENT_SPEED, (double)0.4F).add(EntityAttributes.ATTACK_DAMAGE, (double)3.0F).add(EntityAttributes.FOLLOW_RANGE, (double)32.0F).add(EntityAttributes.STEP_HEIGHT, (double)1.0625F);
   }

   public boolean isUnrooted() {
      return (Boolean)this.dataTracker.get(UNROOTED);
   }

   public boolean tryAttack(ServerWorld world, Entity target) {
      if (!(target instanceof LivingEntity)) {
         return false;
      } else {
         this.attackAnimationTimer = 15;
         this.getWorld().sendEntityStatus(this, (byte)4);
         return super.tryAttack(world, target);
      }
   }

   public boolean damage(ServerWorld world, DamageSource source, float amount) {
      BlockPos blockPos = this.getHomePos();
      if (blockPos != null && !source.isIn(DamageTypeTags.BYPASSES_INVULNERABILITY)) {
         if (!this.isInvulnerableTo(world, source) && this.invulnerableAnimationTimer <= 0 && !this.isDead()) {
            PlayerEntity playerEntity = this.becomeAngryAndGetPlayer(source);
            Entity entity = source.getSource();
            if (!(entity instanceof LivingEntity) && !(entity instanceof ProjectileEntity) && playerEntity == null) {
               return false;
            } else {
               this.invulnerableAnimationTimer = 8;
               this.getWorld().sendEntityStatus(this, (byte)66);
               this.emitGameEvent(GameEvent.ENTITY_ACTION);
               BlockEntity var8 = this.getWorld().getBlockEntity(blockPos);
               if (var8 instanceof CreakingHeartBlockEntity) {
                  CreakingHeartBlockEntity creakingHeartBlockEntity = (CreakingHeartBlockEntity)var8;
                  if (creakingHeartBlockEntity.isPuppet(this)) {
                     if (playerEntity != null) {
                        creakingHeartBlockEntity.onPuppetDamage();
                     }

                     this.playHurtSound(source);
                  }
               }

               return true;
            }
         } else {
            return false;
         }
      } else {
         return super.damage(world, source, amount);
      }
   }

   public PlayerEntity becomeAngryAndGetPlayer(DamageSource damageSource) {
      this.becomeAngry(damageSource);
      return this.setAttackingPlayer(damageSource);
   }

   public boolean isPushable() {
      return super.isPushable() && this.isUnrooted();
   }

   public void addVelocity(double deltaX, double deltaY, double deltaZ) {
      if (this.isUnrooted()) {
         super.addVelocity(deltaX, deltaY, deltaZ);
      }
   }

   public Brain<CreakingEntity> getBrain() {
      return super.getBrain();
   }

   protected void mobTick(ServerWorld world) {
      Profiler profiler = Profilers.get();
      profiler.push("creakingBrain");
      this.getBrain().tick((ServerWorld)this.getWorld(), this);
      profiler.pop();
      CreakingBrain.updateActivities(this);
   }

   public void tickMovement() {
      if (this.invulnerableAnimationTimer > 0) {
         --this.invulnerableAnimationTimer;
      }

      if (this.attackAnimationTimer > 0) {
         --this.attackAnimationTimer;
      }

      if (!this.getWorld().isClient) {
         boolean bl = (Boolean)this.dataTracker.get(UNROOTED);
         boolean bl2 = this.shouldBeUnrooted();
         if (bl2 != bl) {
            this.emitGameEvent(GameEvent.ENTITY_ACTION);
            if (bl2) {
               this.playSound(SoundEvents.ENTITY_CREAKING_UNFREEZE);
            } else {
               this.stopMovement();
               this.playSound(SoundEvents.ENTITY_CREAKING_FREEZE);
            }
         }

         this.dataTracker.set(UNROOTED, bl2);
      }

      super.tickMovement();
   }

   public void tick() {
      if (!this.getWorld().isClient) {
         BlockPos blockPos = this.getHomePos();
         if (blockPos != null) {
            boolean var10000;
            label21: {
               BlockEntity var4 = this.getWorld().getBlockEntity(blockPos);
               if (var4 instanceof CreakingHeartBlockEntity) {
                  CreakingHeartBlockEntity creakingHeartBlockEntity = (CreakingHeartBlockEntity)var4;
                  if (creakingHeartBlockEntity.isPuppet(this)) {
                     var10000 = true;
                     break label21;
                  }
               }

               var10000 = false;
            }

            boolean bl = var10000;
            if (!bl) {
               this.setHealth(0.0F);
            }
         }
      }

      super.tick();
      if (this.getWorld().isClient) {
         this.tickAttackAnimation();
         this.updateCrumblingEyeFlicker();
      }

   }

   protected void updatePostDeath() {
      if (this.isTransient() && this.isCrumbling()) {
         ++this.deathTime;
         if (!this.getWorld().isClient() && this.deathTime > 45 && !this.isRemoved()) {
            this.finishCrumbling();
         }
      } else {
         super.updatePostDeath();
      }

   }

   protected void updateLimbs(float posDelta) {
      float f = Math.min(posDelta * 25.0F, 3.0F);
      this.limbAnimator.updateLimbs(f, 0.4F, 1.0F);
   }

   private void tickAttackAnimation() {
      this.attackAnimationState.setRunning(this.attackAnimationTimer > 0, this.age);
      this.invulnerableAnimationState.setRunning(this.invulnerableAnimationTimer > 0, this.age);
      this.crumblingAnimationState.setRunning(this.isCrumbling(), this.age);
   }

   public void finishCrumbling() {
      World var2 = this.getWorld();
      if (var2 instanceof ServerWorld serverWorld) {
         Box box = this.getBoundingBox();
         Vec3d vec3d = box.getCenter();
         double d = box.getLengthX() * 0.3;
         double e = box.getLengthY() * 0.3;
         double f = box.getLengthZ() * 0.3;
         serverWorld.spawnParticles(new BlockStateParticleEffect(ParticleTypes.BLOCK_CRUMBLE, Blocks.PALE_OAK_WOOD.getDefaultState()), vec3d.x, vec3d.y, vec3d.z, 100, d, e, f, (double)0.0F);
         serverWorld.spawnParticles(new BlockStateParticleEffect(ParticleTypes.BLOCK_CRUMBLE, (BlockState)Blocks.CREAKING_HEART.getDefaultState().with(CreakingHeartBlock.ACTIVE, CreakingHeartState.AWAKE)), vec3d.x, vec3d.y, vec3d.z, 10, d, e, f, (double)0.0F);
      }

      this.playSound(this.getDeathSound());
      this.remove(Entity.RemovalReason.DISCARDED);
   }

   public void killFromHeart(DamageSource damageSource) {
      this.becomeAngryAndGetPlayer(damageSource);
      this.onDeath(damageSource);
      this.playSound(SoundEvents.ENTITY_CREAKING_TWITCH);
   }

   public void handleStatus(byte status) {
      if (status == 66) {
         this.invulnerableAnimationTimer = 8;
         this.playHurtSound(this.getDamageSources().generic());
      } else if (status == 4) {
         this.attackAnimationTimer = 15;
         this.playAttackSound();
      } else {
         super.handleStatus(status);
      }

   }

   public boolean isFireImmune() {
      return this.isTransient() || super.isFireImmune();
   }

   protected boolean canAddPassenger(Entity passenger) {
      return !this.isTransient() && super.canAddPassenger(passenger);
   }

   protected boolean couldAcceptPassenger() {
      return !this.isTransient() && super.couldAcceptPassenger();
   }

   protected void addPassenger(Entity passenger) {
      if (this.isTransient()) {
         throw new IllegalStateException("Should never addPassenger without checking couldAcceptPassenger()");
      }
   }

   public boolean canUsePortals(boolean allowVehicles) {
      return !this.isTransient() && super.canUsePortals(allowVehicles);
   }

   protected EntityNavigation createNavigation(World world) {
      return new CreakingNavigation(this, world);
   }

   public boolean isStuckWithPlayer() {
      List<PlayerEntity> list = (List)this.brain.getOptionalRegisteredMemory(MemoryModuleType.NEAREST_PLAYERS).orElse(List.of());
      if (list.isEmpty()) {
         this.playerIntersectionTimer = 0;
         return false;
      } else {
         Box box = this.getBoundingBox();

         for(PlayerEntity playerEntity : list) {
            if (box.contains(playerEntity.getEyePos())) {
               ++this.playerIntersectionTimer;
               return this.playerIntersectionTimer > 4;
            }
         }

         this.playerIntersectionTimer = 0;
         return false;
      }
   }

   protected void readCustomData(ReadView view) {
      super.readCustomData(view);
      view.read("home_pos", BlockPos.CODEC).ifPresent(this::initHomePos);
   }

   protected void writeCustomData(WriteView view) {
      super.writeCustomData(view);
      view.putNullable("home_pos", BlockPos.CODEC, this.getHomePos());
   }

   public void setHomePos(BlockPos pos) {
      this.dataTracker.set(HOME_POS, Optional.of(pos));
   }

   @Nullable
   public BlockPos getHomePos() {
      return (BlockPos)((Optional)this.dataTracker.get(HOME_POS)).orElse((Object)null);
   }

   public void setCrumbling() {
      this.dataTracker.set(CRUMBLING, true);
   }

   public boolean isCrumbling() {
      return (Boolean)this.dataTracker.get(CRUMBLING);
   }

   public boolean hasGlowingEyesWhileCrumbling() {
      return this.glowingEyesWhileCrumbling;
   }

   public void updateCrumblingEyeFlicker() {
      if (this.deathTime > this.nextEyeFlickerTime) {
         this.nextEyeFlickerTime = this.deathTime + this.getRandom().nextBetween(this.glowingEyesWhileCrumbling ? 2 : this.deathTime / 4, this.glowingEyesWhileCrumbling ? 8 : this.deathTime / 2);
         this.glowingEyesWhileCrumbling = !this.glowingEyesWhileCrumbling;
      }

   }

   public void playAttackSound() {
      this.playSound(SoundEvents.ENTITY_CREAKING_ATTACK);
   }

   protected SoundEvent getAmbientSound() {
      return this.isActive() ? null : SoundEvents.ENTITY_CREAKING_AMBIENT;
   }

   protected SoundEvent getHurtSound(DamageSource source) {
      return this.isTransient() ? SoundEvents.ENTITY_CREAKING_SWAY : super.getHurtSound(source);
   }

   protected SoundEvent getDeathSound() {
      return SoundEvents.ENTITY_CREAKING_DEATH;
   }

   protected void playStepSound(BlockPos pos, BlockState state) {
      this.playSound(SoundEvents.ENTITY_CREAKING_STEP, 0.15F, 1.0F);
   }

   @Nullable
   public LivingEntity getTarget() {
      return this.getTargetInBrain();
   }

   protected void sendAiDebugData() {
      super.sendAiDebugData();
      DebugInfoSender.sendBrainDebugData(this);
   }

   public void takeKnockback(double strength, double x, double z) {
      if (this.isUnrooted()) {
         super.takeKnockback(strength, x, z);
      }
   }

   public boolean shouldBeUnrooted() {
      List<PlayerEntity> list = (List)this.brain.getOptionalRegisteredMemory(MemoryModuleType.NEAREST_PLAYERS).orElse(List.of());
      boolean bl = this.isActive();
      if (list.isEmpty()) {
         if (bl) {
            this.deactivate();
         }

         return true;
      } else {
         boolean bl2 = false;

         for(PlayerEntity playerEntity : list) {
            if (this.canTarget(playerEntity) && !this.isTeammate(playerEntity)) {
               bl2 = true;
               if ((!bl || LivingEntity.NOT_WEARING_GAZE_DISGUISE_PREDICATE.test(playerEntity)) && this.isEntityLookingAtMe(playerEntity, (double)0.5F, false, true, new double[]{this.getEyeY(), this.getY() + (double)0.5F * (double)this.getScale(), (this.getEyeY() + this.getY()) / (double)2.0F})) {
                  if (bl) {
                     return false;
                  }

                  if (playerEntity.squaredDistanceTo(this) < (double)144.0F) {
                     this.activate(playerEntity);
                     return false;
                  }
               }
            }
         }

         if (!bl2 && bl) {
            this.deactivate();
         }

         return true;
      }
   }

   public void activate(PlayerEntity player) {
      this.getBrain().remember(MemoryModuleType.ATTACK_TARGET, player);
      this.emitGameEvent(GameEvent.ENTITY_ACTION);
      this.playSound(SoundEvents.ENTITY_CREAKING_ACTIVATE);
      this.setActive(true);
   }

   public void deactivate() {
      this.getBrain().forget(MemoryModuleType.ATTACK_TARGET);
      this.emitGameEvent(GameEvent.ENTITY_ACTION);
      this.playSound(SoundEvents.ENTITY_CREAKING_DEACTIVATE);
      this.setActive(false);
   }

   public void setActive(boolean active) {
      this.dataTracker.set(ACTIVE, active);
   }

   public boolean isActive() {
      return (Boolean)this.dataTracker.get(ACTIVE);
   }

   public float getPathfindingFavor(BlockPos pos, WorldView world) {
      return 0.0F;
   }

   static {
      UNROOTED = DataTracker.<Boolean>registerData(CreakingEntity.class, TrackedDataHandlerRegistry.BOOLEAN);
      ACTIVE = DataTracker.<Boolean>registerData(CreakingEntity.class, TrackedDataHandlerRegistry.BOOLEAN);
      CRUMBLING = DataTracker.<Boolean>registerData(CreakingEntity.class, TrackedDataHandlerRegistry.BOOLEAN);
      HOME_POS = DataTracker.<Optional<BlockPos>>registerData(CreakingEntity.class, TrackedDataHandlerRegistry.OPTIONAL_BLOCK_POS);
   }

   class CreakingLookControl extends LookControl {
      public CreakingLookControl(final CreakingEntity creaking) {
         super(creaking);
      }

      public void tick() {
         if (CreakingEntity.this.isUnrooted()) {
            super.tick();
         }

      }
   }

   class CreakingMoveControl extends MoveControl {
      public CreakingMoveControl(final CreakingEntity creaking) {
         super(creaking);
      }

      public void tick() {
         if (CreakingEntity.this.isUnrooted()) {
            super.tick();
         }

      }
   }

   class CreakingJumpControl extends JumpControl {
      public CreakingJumpControl(final CreakingEntity creaking) {
         super(creaking);
      }

      public void tick() {
         if (CreakingEntity.this.isUnrooted()) {
            super.tick();
         } else {
            CreakingEntity.this.setJumping(false);
         }

      }
   }

   class CreakingBodyControl extends BodyControl {
      public CreakingBodyControl(final CreakingEntity creaking) {
         super(creaking);
      }

      public void tick() {
         if (CreakingEntity.this.isUnrooted()) {
            super.tick();
         }

      }
   }

   class CreakingLandPathNodeMaker extends LandPathNodeMaker {
      private static final int field_54896 = 1024;

      public PathNodeType getDefaultNodeType(PathContext context, int x, int y, int z) {
         BlockPos blockPos = CreakingEntity.this.getHomePos();
         if (blockPos == null) {
            return super.getDefaultNodeType(context, x, y, z);
         } else {
            double d = blockPos.getSquaredDistance(new Vec3i(x, y, z));
            return d > (double)1024.0F && d >= blockPos.getSquaredDistance(context.getEntityPos()) ? PathNodeType.BLOCKED : super.getDefaultNodeType(context, x, y, z);
         }
      }
   }

   class CreakingNavigation extends MobNavigation {
      CreakingNavigation(final CreakingEntity creaking, final World world) {
         super(creaking, world);
      }

      public void tick() {
         if (CreakingEntity.this.isUnrooted()) {
            super.tick();
         }

      }

      protected PathNodeNavigator createPathNodeNavigator(int range) {
         this.nodeMaker = CreakingEntity.this.new CreakingLandPathNodeMaker();
         this.nodeMaker.setCanEnterOpenDoors(true);
         return new PathNodeNavigator(this.nodeMaker, range);
      }
   }
}
