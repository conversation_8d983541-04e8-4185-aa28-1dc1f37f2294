package net.minecraft.network.state;

import io.netty.buffer.ByteBuf;
import net.minecraft.network.NetworkPhase;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.network.listener.ClientQueryPacketListener;
import net.minecraft.network.listener.ServerQueryPacketListener;
import net.minecraft.network.packet.PingPackets;
import net.minecraft.network.packet.StatusPackets;
import net.minecraft.network.packet.c2s.query.QueryPingC2SPacket;
import net.minecraft.network.packet.c2s.query.QueryRequestC2SPacket;
import net.minecraft.network.packet.s2c.query.PingResultS2CPacket;
import net.minecraft.network.packet.s2c.query.QueryResponseS2CPacket;

public class QueryStates {
   public static final NetworkStateFactory<ServerQueryPacketListener, ByteBuf> C2S_FACTORY;
   public static final NetworkState<ServerQueryPacketListener> C2S;
   public static final NetworkStateFactory<ClientQueryPacketListener, PacketByteBuf> S2C_FACTORY;
   public static final NetworkState<ClientQueryPacketListener> S2C;

   static {
      C2S_FACTORY = NetworkStateBuilder.<ServerQueryPacketListener, ByteBuf>c2s(NetworkPhase.STATUS, (builder) -> builder.add(StatusPackets.STATUS_REQUEST, QueryRequestC2SPacket.CODEC).add(PingPackets.PING_REQUEST, QueryPingC2SPacket.CODEC));
      C2S = C2S_FACTORY.bind((buf) -> buf);
      S2C_FACTORY = NetworkStateBuilder.<ClientQueryPacketListener, PacketByteBuf>s2c(NetworkPhase.STATUS, (builder) -> builder.add(StatusPackets.STATUS_RESPONSE, QueryResponseS2CPacket.CODEC).add(PingPackets.PONG_RESPONSE, PingResultS2CPacket.CODEC));
      S2C = S2C_FACTORY.bind(PacketByteBuf::new);
   }
}
