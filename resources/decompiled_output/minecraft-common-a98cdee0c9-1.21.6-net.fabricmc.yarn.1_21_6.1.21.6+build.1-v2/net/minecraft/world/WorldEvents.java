package net.minecraft.world;

public class WorldEvents {
   public static final int DISPENSER_DISPENSES = 1000;
   public static final int DISPENSER_FAILS = 1001;
   public static final int DISPENSER_LAUNCHES_PROJECTILE = 1002;
   public static final int FIREWORK_ROCKET_SHOOTS = 1004;
   public static final int FIRE_EXTINGUISHED = 1009;
   public static final int JUKEBOX_STARTS_PLAYING = 1010;
   public static final int JUKEBOX_STOPS_PLAYING = 1011;
   public static final int GHAST_WARNS = 1015;
   public static final int GHAST_SHOOTS = 1016;
   public static final int ENDER_DRAGON_SHOOTS = 1017;
   public static final int BLAZE_SHOOTS = 1018;
   public static final int ZOMBIE_ATTACKS_WOODEN_DOOR = 1019;
   public static final int ZOMBIE_ATTACKS_IRON_DOOR = 1020;
   public static final int ZOMBIE_BREAKS_WOODEN_DOOR = 1021;
   public static final int WITHER_BREAKS_BLOCK = 1022;
   public static final int WITHER_SPAWNS = 1023;
   public static final int WITHER_SHOOTS = 1024;
   public static final int BAT_TAKES_OFF = 1025;
   public static final int ZOMBIE_INFECTS_VILLAGER = 1026;
   public static final int ZOMBIE_VILLAGER_CURED = 1027;
   public static final int ENDER_DRAGON_DIES = 1028;
   public static final int ANVIL_DESTROYED = 1029;
   public static final int ANVIL_USED = 1030;
   public static final int ANVIL_LANDS = 1031;
   public static final int TRAVEL_THROUGH_PORTAL = 1032;
   public static final int CHORUS_FLOWER_GROWS = 1033;
   public static final int CHORUS_FLOWER_DIES = 1034;
   public static final int BREWING_STAND_BREWS = 1035;
   public static final int END_PORTAL_OPENED = 1038;
   public static final int PHANTOM_BITES = 1039;
   public static final int ZOMBIE_CONVERTS_TO_DROWNED = 1040;
   public static final int HUSK_CONVERTS_TO_ZOMBIE = 1041;
   public static final int GRINDSTONE_USED = 1042;
   public static final int LECTERN_BOOK_PAGE_TURNED = 1043;
   public static final int SMITHING_TABLE_USED = 1044;
   public static final int POINTED_DRIPSTONE_LANDS = 1045;
   public static final int POINTED_DRIPSTONE_DRIPS_LAVA_INTO_CAULDRON = 1046;
   public static final int POINTED_DRIPSTONE_DRIPS_WATER_INTO_CAULDRON = 1047;
   public static final int SKELETON_CONVERTS_TO_STRAY = 1048;
   public static final int CRAFTER_CRAFTS = 1049;
   public static final int CRAFTER_FAILS = 1050;
   public static final int field_51787 = 1051;
   public static final int COMPOSTER_USED = 1500;
   public static final int LAVA_EXTINGUISHED = 1501;
   public static final int REDSTONE_TORCH_BURNS_OUT = 1502;
   public static final int END_PORTAL_FRAME_FILLED = 1503;
   public static final int POINTED_DRIPSTONE_DRIPS = 1504;
   public static final int BONE_MEAL_USED = 1505;
   public static final int DISPENSER_ACTIVATED = 2000;
   public static final int BLOCK_BROKEN = 2001;
   public static final int SPLASH_POTION_SPLASHED = 2002;
   public static final int EYE_OF_ENDER_BREAKS = 2003;
   public static final int SPAWNER_SPAWNS_MOB = 2004;
   public static final int DRAGON_BREATH_CLOUD_SPAWNS = 2006;
   public static final int INSTANT_SPLASH_POTION_SPLASHED = 2007;
   public static final int ENDER_DRAGON_BREAKS_BLOCK = 2008;
   public static final int WET_SPONGE_DRIES_OUT = 2009;
   public static final int CRAFTER_SHOOTS = 2010;
   public static final int BEE_FERTILIZES_PLANT = 2011;
   public static final int TURTLE_EGG_PLACED = 2012;
   public static final int SMASH_ATTACK = 2013;
   public static final int END_GATEWAY_SPAWNS = 3000;
   public static final int ENDER_DRAGON_RESURRECTED = 3001;
   public static final int ELECTRICITY_SPARKS = 3002;
   public static final int BLOCK_WAXED = 3003;
   public static final int WAX_REMOVED = 3004;
   public static final int BLOCK_SCRAPED = 3005;
   public static final int SCULK_CHARGE = 3006;
   public static final int SCULK_SHRIEKS = 3007;
   public static final int BLOCK_FINISHED_BRUSHING = 3008;
   public static final int SNIFFER_EGG_CRACKS = 3009;
   public static final int TRIAL_SPAWNER_SPAWNS_MOB = 3011;
   public static final int TRIAL_SPAWNER_SPAWNS_MOB_AT_SPAWN_POS = 3012;
   public static final int TRIAL_SPAWNER_DETECTS_PLAYER = 3013;
   public static final int TRIAL_SPAWNER_EJECTS_ITEM = 3014;
   public static final int VAULT_ACTIVATES = 3015;
   public static final int VAULT_DEACTIVATES = 3016;
   public static final int VAULT_EJECTS_ITEM = 3017;
   public static final int COBWEB_WEAVED = 3018;
   public static final int OMINOUS_TRIAL_SPAWNER_DETECTS_PLAYER = 3019;
   public static final int TRIAL_SPAWNER_TURNS_OMINOUS = 3020;
   public static final int OMINOUS_ITEM_SPAWNER_SPAWNS_ITEM = 3021;
}
