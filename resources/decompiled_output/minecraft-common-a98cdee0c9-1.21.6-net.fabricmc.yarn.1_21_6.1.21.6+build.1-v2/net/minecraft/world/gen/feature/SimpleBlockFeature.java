package net.minecraft.world.gen.feature;

import com.mojang.serialization.Codec;
import net.minecraft.block.BlockState;
import net.minecraft.block.PaleMossCarpetBlock;
import net.minecraft.block.TallPlantBlock;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.StructureWorldAccess;
import net.minecraft.world.gen.feature.util.FeatureContext;

public class SimpleBlockFeature extends Feature<SimpleBlockFeatureConfig> {
   public SimpleBlockFeature(Codec<SimpleBlockFeatureConfig> codec) {
      super(codec);
   }

   public boolean generate(FeatureContext<SimpleBlockFeatureConfig> context) {
      SimpleBlockFeatureConfig simpleBlockFeatureConfig = context.getConfig();
      StructureWorldAccess structureWorldAccess = context.getWorld();
      BlockPos blockPos = context.getOrigin();
      BlockState blockState = simpleBlockFeatureConfig.toPlace().get(context.getRandom(), blockPos);
      if (blockState.canPlaceAt(structureWorldAccess, blockPos)) {
         if (blockState.getBlock() instanceof TallPlantBlock) {
            if (!structureWorldAccess.isAir(blockPos.up())) {
               return false;
            }

            TallPlantBlock.placeAt(structureWorldAccess, blockState, blockPos, 2);
         } else if (blockState.getBlock() instanceof PaleMossCarpetBlock) {
            PaleMossCarpetBlock.placeAt(structureWorldAccess, blockPos, structureWorldAccess.getRandom(), 2);
         } else {
            structureWorldAccess.setBlockState(blockPos, blockState, 2);
         }

         if (simpleBlockFeatureConfig.scheduleTick()) {
            structureWorldAccess.scheduleBlockTick(blockPos, structureWorldAccess.getBlockState(blockPos).getBlock(), 1);
         }

         return true;
      } else {
         return false;
      }
   }
}
