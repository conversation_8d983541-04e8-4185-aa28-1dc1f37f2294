package net.minecraft.world.gen.stateprovider;

import com.mojang.datafixers.Products;
import com.mojang.serialization.MapCodec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.List;
import net.minecraft.block.BlockState;
import net.minecraft.util.dynamic.Codecs;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.noise.DoublePerlinNoiseSampler;
import net.minecraft.util.math.random.Random;

public class NoiseBlockStateProvider extends AbstractNoiseBlockStateProvider {
   public static final MapCodec<NoiseBlockStateProvider> CODEC = RecordCodecBuilder.mapCodec((instance) -> fillNoiseCodecFields(instance).apply(instance, NoiseBlockStateProvider::new));
   protected final List<BlockState> states;

   protected static <P extends NoiseBlockStateProvider> Products.P4<RecordCodecBuilder.Mu<P>, Long, DoublePerlinNoiseSampler.NoiseParameters, Float, List<BlockState>> fillNoiseCodecFields(RecordCodecBuilder.Instance<P> instance) {
      return fillCodecFields(instance).and(Codecs.nonEmptyList(BlockState.CODEC.listOf()).fieldOf("states").forGetter((noiseBlockStateProvider) -> noiseBlockStateProvider.states));
   }

   public NoiseBlockStateProvider(long seed, DoublePerlinNoiseSampler.NoiseParameters noiseParameters, float scale, List<BlockState> states) {
      super(seed, noiseParameters, scale);
      this.states = states;
   }

   protected BlockStateProviderType<?> getType() {
      return BlockStateProviderType.NOISE_PROVIDER;
   }

   public BlockState get(Random random, BlockPos pos) {
      return this.getStateFromList(this.states, pos, (double)this.scale);
   }

   protected BlockState getStateFromList(List<BlockState> states, BlockPos pos, double scale) {
      double d = this.getNoiseValue(pos, scale);
      return this.getStateAtValue(states, d);
   }

   protected BlockState getStateAtValue(List<BlockState> states, double value) {
      double d = MathHelper.clamp(((double)1.0F + value) / (double)2.0F, (double)0.0F, 0.9999);
      return (BlockState)states.get((int)(d * (double)states.size()));
   }
}
