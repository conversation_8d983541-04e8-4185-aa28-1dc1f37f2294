package net.minecraft.world.gen.noise;

import net.minecraft.registry.Registerable;
import net.minecraft.registry.RegistryKey;
import net.minecraft.util.math.noise.DoublePerlinNoiseSampler;

public class BuiltinNoiseParameters {
   /** @deprecated */
   @Deprecated
   public static final DoublePerlinNoiseSampler.NoiseParameters OFFSET = new DoublePerlinNoiseSampler.NoiseParameters(-3, (double)1.0F, new double[]{(double)1.0F, (double)1.0F, (double)0.0F});

   public static void bootstrap(Registerable<DoublePerlinNoiseSampler.NoiseParameters> noiseParametersRegisterable) {
      register(noiseParametersRegisterable, 0, NoiseParametersKeys.TEMPERATURE, NoiseParametersKeys.VEGETATION, NoiseParametersKeys.CONTINENTALNESS, NoiseParametersKeys.EROSION);
      register(noiseParametersRegisterable, -2, NoiseParametersKeys.TEMPERATURE_LARGE, NoiseParametersKeys.VEGETATION_LARGE, NoiseParametersKeys.CONTINENTALNESS_LARGE, NoiseParametersKeys.EROSION_LARGE);
      register(noiseParametersRegisterable, NoiseParametersKeys.RIDGE, -7, (double)1.0F, (double)2.0F, (double)1.0F, (double)0.0F, (double)0.0F, (double)0.0F);
      noiseParametersRegisterable.register(NoiseParametersKeys.OFFSET, OFFSET);
      register(noiseParametersRegisterable, NoiseParametersKeys.AQUIFER_BARRIER, -3, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.AQUIFER_FLUID_LEVEL_FLOODEDNESS, -7, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.AQUIFER_LAVA, -1, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.AQUIFER_FLUID_LEVEL_SPREAD, -5, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.PILLAR, -7, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.PILLAR_RARENESS, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.PILLAR_THICKNESS, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_2D, -7, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_2D_ELEVATION, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_2D_MODULATOR, -11, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_2D_THICKNESS, -11, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_3D_1, -7, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_3D_2, -7, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_3D_RARITY, -11, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_3D_THICKNESS, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_ROUGHNESS, -5, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SPAGHETTI_ROUGHNESS_MODULATOR, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.CAVE_ENTRANCE, -7, 0.4, (double)0.5F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.CAVE_LAYER, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.CAVE_CHEESE, -8, (double)0.5F, (double)1.0F, (double)2.0F, (double)1.0F, (double)2.0F, (double)1.0F, (double)0.0F, (double)2.0F, (double)0.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.ORE_VEININESS, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.ORE_VEIN_A, -7, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.ORE_VEIN_B, -7, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.ORE_GAP, -5, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.NOODLE, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.NOODLE_THICKNESS, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.NOODLE_RIDGE_A, -7, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.NOODLE_RIDGE_B, -7, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.JAGGED, -16, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SURFACE, -6, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SURFACE_SECONDARY, -6, (double)1.0F, (double)1.0F, (double)0.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.CLAY_BANDS_OFFSET, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.BADLANDS_PILLAR, -2, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.BADLANDS_PILLAR_ROOF, -8, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.BADLANDS_SURFACE, -6, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.ICEBERG_PILLAR, -6, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.ICEBERG_PILLAR_ROOF, -3, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.ICEBERG_SURFACE, -6, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SURFACE_SWAMP, -2, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.CALCITE, -9, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.GRAVEL, -8, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.POWDER_SNOW, -6, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.PACKED_ICE, -7, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.ICE, -4, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, NoiseParametersKeys.SOUL_SAND_LAYER, -8, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)0.0F, (double)0.0F, (double)0.0F, (double)0.0F, 0.013333333333333334);
      register(noiseParametersRegisterable, NoiseParametersKeys.GRAVEL_LAYER, -8, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)0.0F, (double)0.0F, (double)0.0F, (double)0.0F, 0.013333333333333334);
      register(noiseParametersRegisterable, NoiseParametersKeys.PATCH, -5, (double)1.0F, (double)0.0F, (double)0.0F, (double)0.0F, (double)0.0F, 0.013333333333333334);
      register(noiseParametersRegisterable, NoiseParametersKeys.NETHERRACK, -3, (double)1.0F, (double)0.0F, (double)0.0F, 0.35);
      register(noiseParametersRegisterable, NoiseParametersKeys.NETHER_WART, -3, (double)1.0F, (double)0.0F, (double)0.0F, 0.9);
      register(noiseParametersRegisterable, NoiseParametersKeys.NETHER_STATE_SELECTOR, -4, (double)1.0F);
   }

   private static void register(Registerable<DoublePerlinNoiseSampler.NoiseParameters> noiseParametersRegisterable, int octaveOffset, RegistryKey<DoublePerlinNoiseSampler.NoiseParameters> temperatureKey, RegistryKey<DoublePerlinNoiseSampler.NoiseParameters> vegetationKey, RegistryKey<DoublePerlinNoiseSampler.NoiseParameters> continentalnessKey, RegistryKey<DoublePerlinNoiseSampler.NoiseParameters> erosionKey) {
      register(noiseParametersRegisterable, temperatureKey, -10 + octaveOffset, (double)1.5F, (double)0.0F, (double)1.0F, (double)0.0F, (double)0.0F, (double)0.0F);
      register(noiseParametersRegisterable, vegetationKey, -8 + octaveOffset, (double)1.0F, (double)1.0F, (double)0.0F, (double)0.0F, (double)0.0F, (double)0.0F);
      register(noiseParametersRegisterable, continentalnessKey, -9 + octaveOffset, (double)1.0F, (double)1.0F, (double)2.0F, (double)2.0F, (double)2.0F, (double)1.0F, (double)1.0F, (double)1.0F, (double)1.0F);
      register(noiseParametersRegisterable, erosionKey, -9 + octaveOffset, (double)1.0F, (double)1.0F, (double)0.0F, (double)1.0F, (double)1.0F);
   }

   private static void register(Registerable<DoublePerlinNoiseSampler.NoiseParameters> noiseParametersRegisterable, RegistryKey<DoublePerlinNoiseSampler.NoiseParameters> key, int firstOctave, double firstAmplitude, double... amplitudes) {
      noiseParametersRegisterable.register(key, new DoublePerlinNoiseSampler.NoiseParameters(firstOctave, firstAmplitude, amplitudes));
   }
}
