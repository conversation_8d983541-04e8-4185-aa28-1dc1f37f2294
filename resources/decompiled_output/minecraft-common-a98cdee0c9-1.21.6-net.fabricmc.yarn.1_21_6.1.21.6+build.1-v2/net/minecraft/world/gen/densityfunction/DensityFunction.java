package net.minecraft.world.gen.densityfunction;

import com.mojang.serialization.Codec;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.registry.entry.RegistryElementCodec;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.util.dynamic.CodecHolder;
import net.minecraft.util.math.noise.DoublePerlinNoiseSampler;
import net.minecraft.world.gen.chunk.Blender;
import org.jetbrains.annotations.Nullable;

public interface DensityFunction {
   Codec<DensityFunction> CODEC = DensityFunctionTypes.CODEC;
   Codec<RegistryEntry<DensityFunction>> REGISTRY_ENTRY_CODEC = RegistryElementCodec.<RegistryEntry<DensityFunction>>of(RegistryKeys.DENSITY_FUNCTION, CODEC);
   Codec<DensityFunction> FUNCTION_CODEC = REGISTRY_ENTRY_CODEC.xmap(DensityFunctionTypes.RegistryEntryHolder::new, (function) -> {
      if (function instanceof DensityFunctionTypes.RegistryEntryHolder registryEntryHolder) {
         return registryEntryHolder.function();
      } else {
         return new RegistryEntry.Direct(function);
      }
   });

   double sample(NoisePos pos);

   void fill(double[] densities, EachApplier applier);

   DensityFunction apply(DensityFunctionVisitor visitor);

   double minValue();

   double maxValue();

   CodecHolder<? extends DensityFunction> getCodecHolder();

   default DensityFunction clamp(double min, double max) {
      return new DensityFunctionTypes.Clamp(this, min, max);
   }

   default DensityFunction abs() {
      return DensityFunctionTypes.unary(this, DensityFunctionTypes.UnaryOperation.Type.ABS);
   }

   default DensityFunction square() {
      return DensityFunctionTypes.unary(this, DensityFunctionTypes.UnaryOperation.Type.SQUARE);
   }

   default DensityFunction cube() {
      return DensityFunctionTypes.unary(this, DensityFunctionTypes.UnaryOperation.Type.CUBE);
   }

   default DensityFunction halfNegative() {
      return DensityFunctionTypes.unary(this, DensityFunctionTypes.UnaryOperation.Type.HALF_NEGATIVE);
   }

   default DensityFunction quarterNegative() {
      return DensityFunctionTypes.unary(this, DensityFunctionTypes.UnaryOperation.Type.QUARTER_NEGATIVE);
   }

   default DensityFunction squeeze() {
      return DensityFunctionTypes.unary(this, DensityFunctionTypes.UnaryOperation.Type.SQUEEZE);
   }

   public static record Noise(RegistryEntry<DoublePerlinNoiseSampler.NoiseParameters> noiseData, @Nullable DoublePerlinNoiseSampler noise) {
      public static final Codec<Noise> CODEC;

      public Noise(RegistryEntry<DoublePerlinNoiseSampler.NoiseParameters> noiseData) {
         this(noiseData, (DoublePerlinNoiseSampler)null);
      }

      public double sample(double x, double y, double z) {
         return this.noise == null ? (double)0.0F : this.noise.sample(x, y, z);
      }

      public double getMaxValue() {
         return this.noise == null ? (double)2.0F : this.noise.getMaxValue();
      }

      static {
         CODEC = DoublePerlinNoiseSampler.NoiseParameters.REGISTRY_ENTRY_CODEC.xmap((noiseData) -> new Noise(noiseData, (DoublePerlinNoiseSampler)null), Noise::noiseData);
      }
   }

   public interface DensityFunctionVisitor {
      DensityFunction apply(DensityFunction densityFunction);

      default Noise apply(Noise noiseDensityFunction) {
         return noiseDensityFunction;
      }
   }

   public interface Base extends DensityFunction {
      default void fill(double[] densities, EachApplier applier) {
         applier.fill(densities, this);
      }

      default DensityFunction apply(DensityFunctionVisitor visitor) {
         return visitor.apply((DensityFunction)this);
      }
   }

   public interface NoisePos {
      int blockX();

      int blockY();

      int blockZ();

      default Blender getBlender() {
         return Blender.getNoBlending();
      }
   }

   public static record UnblendedNoisePos(int blockX, int blockY, int blockZ) implements NoisePos {
   }

   public interface EachApplier {
      NoisePos at(int index);

      void fill(double[] densities, DensityFunction densityFunction);
   }
}
