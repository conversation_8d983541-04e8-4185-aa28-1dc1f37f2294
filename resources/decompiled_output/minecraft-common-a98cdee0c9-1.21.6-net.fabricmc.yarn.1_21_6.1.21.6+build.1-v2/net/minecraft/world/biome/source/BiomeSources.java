package net.minecraft.world.biome.source;

import com.mojang.serialization.MapCodec;
import net.minecraft.registry.Registry;

public class BiomeSources {
   public static MapCodec<? extends BiomeSource> registerAndGetDefault(Registry<MapCodec<? extends BiomeSource>> registry) {
      Registry.register(registry, (String)"fixed", FixedBiomeSource.CODEC);
      Registry.register(registry, (String)"multi_noise", MultiNoiseBiomeSource.CODEC);
      Registry.register(registry, (String)"checkerboard", CheckerboardBiomeSource.CODEC);
      return (MapCodec)Registry.register(registry, (String)"the_end", TheEndBiomeSource.CODEC);
   }
}
