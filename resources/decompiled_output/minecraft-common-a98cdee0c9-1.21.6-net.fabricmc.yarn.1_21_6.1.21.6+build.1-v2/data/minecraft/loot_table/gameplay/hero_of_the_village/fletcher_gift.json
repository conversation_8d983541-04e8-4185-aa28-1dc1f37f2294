{"type": "minecraft:gift", "pools": [{"bonus_rolls": 0.0, "entries": [{"type": "minecraft:item", "name": "minecraft:arrow", "weight": 26}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:swiftness"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:slowness"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:strength"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:healing"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:harming"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:leaping"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:regeneration"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:fire_resistance"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:water_breathing"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:invisibility"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:night_vision"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:weakness"}], "name": "minecraft:tipped_arrow"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 1.0, "min": 0.0}, "function": "minecraft:set_count"}, {"function": "minecraft:set_potion", "id": "minecraft:poison"}], "name": "minecraft:tipped_arrow"}], "rolls": 1.0}], "random_sequence": "minecraft:gameplay/hero_of_the_village/fletcher_gift"}