{"parent": "minecraft:adventure/root", "criteria": {"minecraft:blaze": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:blaze"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:bogged": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:bogged"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:breeze": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:breeze"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:cave_spider": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:cave_spider"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:creaking": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:creaking"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:creeper": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:creeper"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:drowned": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:drowned"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:elder_guardian": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:elder_guardian"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:ender_dragon": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:ender_dragon"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:enderman": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:enderman"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:endermite": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:endermite"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:evoker": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:evoker"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:ghast": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:ghast"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:guardian": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:guardian"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:hoglin": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:hoglin"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:husk": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:husk"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:magma_cube": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:magma_cube"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:phantom": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:phantom"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:piglin": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:piglin"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:piglin_brute": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:piglin_brute"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:pillager": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:pillager"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:ravager": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:ravager"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:shulker": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:shulker"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:silverfish": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:silverfish"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:skeleton": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:skeleton"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:slime": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:slime"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:spider": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:spider"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:stray": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:stray"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:vex": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:vex"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:vindicator": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:vindicator"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:witch": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:witch"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:wither": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:wither"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:wither_skeleton": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:wither_skeleton"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:zoglin": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:zoglin"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:zombie": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:zombie"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:zombie_villager": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:zombie_villager"}}]}, "trigger": "minecraft:player_killed_entity"}, "minecraft:zombified_piglin": {"conditions": {"entity": [{"condition": "minecraft:entity_properties", "entity": "this", "predicate": {"type": "minecraft:zombified_piglin"}}]}, "trigger": "minecraft:player_killed_entity"}}, "display": {"description": {"translate": "advancements.adventure.kill_a_mob.description"}, "icon": {"count": 1, "id": "minecraft:iron_sword"}, "title": {"translate": "advancements.adventure.kill_a_mob.title"}}, "requirements": [["minecraft:blaze", "minecraft:bogged", "minecraft:breeze", "minecraft:cave_spider", "minecraft:creaking", "minecraft:creeper", "minecraft:drowned", "minecraft:elder_guardian", "minecraft:ender_dragon", "minecraft:enderman", "minecraft:endermite", "minecraft:evoker", "minecraft:ghast", "minecraft:guardian", "minecraft:hoglin", "minecraft:husk", "minecraft:magma_cube", "minecraft:phantom", "minecraft:piglin", "minecraft:piglin_brute", "minecraft:pillager", "minecraft:ravager", "minecraft:shulker", "minecraft:silverfish", "minecraft:skeleton", "minecraft:slime", "minecraft:spider", "minecraft:stray", "minecraft:vex", "minecraft:vindicator", "minecraft:witch", "minecraft:wither_skeleton", "minecraft:wither", "minecraft:zoglin", "minecraft:zombie_villager", "minecraft:zombie", "minecraft:zombified_piglin"]], "sends_telemetry_event": true}