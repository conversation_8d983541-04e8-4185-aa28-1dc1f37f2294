package net.minecraft.util;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.io.Files;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.mojang.authlib.GameProfile;
import com.mojang.authlib.GameProfileRepository;
import com.mojang.logging.LogUtils;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.Reader;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;

public class UserCache {
   private static final Logger LOGGER = LogUtils.getLogger();
   private static final int MAX_SAVED_ENTRIES = 1000;
   private static final int field_29789 = 1;
   private static boolean useRemote;
   private final Map<String, Entry> byName = Maps.newConcurrentMap();
   private final Map<UUID, Entry> byUuid = Maps.newConcurrentMap();
   private final Map<String, CompletableFuture<Optional<GameProfile>>> pendingRequests = Maps.newConcurrentMap();
   private final GameProfileRepository profileRepository;
   private final Gson gson = (new GsonBuilder()).create();
   private final File cacheFile;
   private final AtomicLong accessCount = new AtomicLong();
   @Nullable
   private Executor executor;

   public UserCache(GameProfileRepository profileRepository, File cacheFile) {
      this.profileRepository = profileRepository;
      this.cacheFile = cacheFile;
      Lists.reverse(this.load()).forEach(this::add);
   }

   private void add(Entry entry) {
      GameProfile gameProfile = entry.getProfile();
      entry.setLastAccessed(this.incrementAndGetAccessCount());
      this.byName.put(gameProfile.getName().toLowerCase(Locale.ROOT), entry);
      this.byUuid.put(gameProfile.getId(), entry);
   }

   private static Optional<GameProfile> findProfileByName(GameProfileRepository repository, String name) {
      if (!StringHelper.isValidPlayerName(name)) {
         return getOfflinePlayerProfile(name);
      } else {
         Optional<GameProfile> optional = repository.findProfileByName(name);
         return optional.isEmpty() ? getOfflinePlayerProfile(name) : optional;
      }
   }

   private static Optional<GameProfile> getOfflinePlayerProfile(String name) {
      return shouldUseRemote() ? Optional.empty() : Optional.of(Uuids.getOfflinePlayerProfile(name));
   }

   public static void setUseRemote(boolean value) {
      useRemote = value;
   }

   private static boolean shouldUseRemote() {
      return useRemote;
   }

   public void add(GameProfile profile) {
      Calendar calendar = Calendar.getInstance();
      calendar.setTime(new Date());
      calendar.add(2, 1);
      Date date = calendar.getTime();
      Entry entry = new Entry(profile, date);
      this.add(entry);
      this.save();
   }

   private long incrementAndGetAccessCount() {
      return this.accessCount.incrementAndGet();
   }

   public Optional<GameProfile> findByName(String name) {
      String string = name.toLowerCase(Locale.ROOT);
      Entry entry = (Entry)this.byName.get(string);
      boolean bl = false;
      if (entry != null && (new Date()).getTime() >= entry.expirationDate.getTime()) {
         this.byUuid.remove(entry.getProfile().getId());
         this.byName.remove(entry.getProfile().getName().toLowerCase(Locale.ROOT));
         bl = true;
         entry = null;
      }

      Optional<GameProfile> optional;
      if (entry != null) {
         entry.setLastAccessed(this.incrementAndGetAccessCount());
         optional = Optional.of(entry.getProfile());
      } else {
         optional = findProfileByName(this.profileRepository, string);
         if (optional.isPresent()) {
            this.add((GameProfile)optional.get());
            bl = false;
         }
      }

      if (bl) {
         this.save();
      }

      return optional;
   }

   public CompletableFuture<Optional<GameProfile>> findByNameAsync(String username) {
      if (this.executor == null) {
         throw new IllegalStateException("No executor");
      } else {
         CompletableFuture<Optional<GameProfile>> completableFuture = (CompletableFuture)this.pendingRequests.get(username);
         if (completableFuture != null) {
            return completableFuture;
         } else {
            CompletableFuture<Optional<GameProfile>> completableFuture2 = CompletableFuture.supplyAsync(() -> this.findByName(username), Util.getMainWorkerExecutor().named("getProfile")).whenCompleteAsync((profile, throwable) -> this.pendingRequests.remove(username), this.executor);
            this.pendingRequests.put(username, completableFuture2);
            return completableFuture2;
         }
      }
   }

   public Optional<GameProfile> getByUuid(UUID uuid) {
      Entry entry = (Entry)this.byUuid.get(uuid);
      if (entry == null) {
         return Optional.empty();
      } else {
         entry.setLastAccessed(this.incrementAndGetAccessCount());
         return Optional.of(entry.getProfile());
      }
   }

   public void setExecutor(Executor executor) {
      this.executor = executor;
   }

   public void clearExecutor() {
      this.executor = null;
   }

   private static DateFormat getDateFormat() {
      return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss Z", Locale.ROOT);
   }

   public List<Entry> load() {
      List<Entry> list = Lists.newArrayList();

      try {
         Reader reader = Files.newReader(this.cacheFile, StandardCharsets.UTF_8);

         Object var9;
         label61: {
            try {
               JsonArray jsonArray = (JsonArray)this.gson.fromJson(reader, JsonArray.class);
               if (jsonArray == null) {
                  var9 = list;
                  break label61;
               }

               DateFormat dateFormat = getDateFormat();
               jsonArray.forEach((json) -> {
                  Optional var10000 = entryFromJson(json, dateFormat);
                  Objects.requireNonNull(list);
                  var10000.ifPresent(list::add);
               });
            } catch (Throwable var6) {
               if (reader != null) {
                  try {
                     reader.close();
                  } catch (Throwable var5) {
                     var6.addSuppressed(var5);
                  }
               }

               throw var6;
            }

            if (reader != null) {
               reader.close();
            }

            return list;
         }

         if (reader != null) {
            reader.close();
         }

         return (List<Entry>)var9;
      } catch (FileNotFoundException var7) {
      } catch (JsonParseException | IOException exception) {
         LOGGER.warn("Failed to load profile cache {}", this.cacheFile, exception);
      }

      return list;
   }

   public void save() {
      JsonArray jsonArray = new JsonArray();
      DateFormat dateFormat = getDateFormat();
      this.getLastAccessedEntries(1000).forEach((entry) -> jsonArray.add(entryToJson(entry, dateFormat)));
      String string = this.gson.toJson(jsonArray);

      try {
         Writer writer = Files.newWriter(this.cacheFile, StandardCharsets.UTF_8);

         try {
            writer.write(string);
         } catch (Throwable var8) {
            if (writer != null) {
               try {
                  writer.close();
               } catch (Throwable var7) {
                  var8.addSuppressed(var7);
               }
            }

            throw var8;
         }

         if (writer != null) {
            writer.close();
         }
      } catch (IOException var9) {
      }

   }

   private Stream<Entry> getLastAccessedEntries(int limit) {
      return ImmutableList.copyOf(this.byUuid.values()).stream().sorted(Comparator.comparing(Entry::getLastAccessed).reversed()).limit((long)limit);
   }

   private static JsonElement entryToJson(Entry entry, DateFormat dateFormat) {
      JsonObject jsonObject = new JsonObject();
      jsonObject.addProperty("name", entry.getProfile().getName());
      jsonObject.addProperty("uuid", entry.getProfile().getId().toString());
      jsonObject.addProperty("expiresOn", dateFormat.format(entry.getExpirationDate()));
      return jsonObject;
   }

   private static Optional<Entry> entryFromJson(JsonElement json, DateFormat dateFormat) {
      if (json.isJsonObject()) {
         JsonObject jsonObject = json.getAsJsonObject();
         JsonElement jsonElement = jsonObject.get("name");
         JsonElement jsonElement2 = jsonObject.get("uuid");
         JsonElement jsonElement3 = jsonObject.get("expiresOn");
         if (jsonElement != null && jsonElement2 != null) {
            String string = jsonElement2.getAsString();
            String string2 = jsonElement.getAsString();
            Date date = null;
            if (jsonElement3 != null) {
               try {
                  date = dateFormat.parse(jsonElement3.getAsString());
               } catch (ParseException var12) {
               }
            }

            if (string2 != null && string != null && date != null) {
               UUID uUID;
               try {
                  uUID = UUID.fromString(string);
               } catch (Throwable var11) {
                  return Optional.empty();
               }

               return Optional.of(new Entry(new GameProfile(uUID, string2), date));
            } else {
               return Optional.empty();
            }
         } else {
            return Optional.empty();
         }
      } else {
         return Optional.empty();
      }
   }

   static class Entry {
      private final GameProfile profile;
      final Date expirationDate;
      private volatile long lastAccessed;

      Entry(GameProfile profile, Date expirationDate) {
         this.profile = profile;
         this.expirationDate = expirationDate;
      }

      public GameProfile getProfile() {
         return this.profile;
      }

      public Date getExpirationDate() {
         return this.expirationDate;
      }

      public void setLastAccessed(long lastAccessed) {
         this.lastAccessed = lastAccessed;
      }

      public long getLastAccessed() {
         return this.lastAccessed;
      }
   }
}
