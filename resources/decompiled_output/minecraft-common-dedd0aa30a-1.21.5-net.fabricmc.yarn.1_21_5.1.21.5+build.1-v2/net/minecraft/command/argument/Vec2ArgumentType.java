package net.minecraft.command.argument;

import com.mojang.brigadier.StringReader;
import com.mojang.brigadier.arguments.ArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.exceptions.SimpleCommandExceptionType;
import com.mojang.brigadier.suggestion.Suggestions;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import net.minecraft.command.CommandSource;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.text.Text;
import net.minecraft.util.math.Vec2f;
import net.minecraft.util.math.Vec3d;

public class Vec2ArgumentType implements ArgumentType<PosArgument> {
   private static final Collection<String> EXAMPLES = Arrays.asList("0 0", "~ ~", "0.1 -0.5", "~1 ~-2");
   public static final SimpleCommandExceptionType INCOMPLETE_EXCEPTION = new SimpleCommandExceptionType(Text.translatable("argument.pos2d.incomplete"));
   private final boolean centerIntegers;

   public Vec2ArgumentType(boolean centerIntegers) {
      this.centerIntegers = centerIntegers;
   }

   public static Vec2ArgumentType vec2() {
      return new Vec2ArgumentType(true);
   }

   public static Vec2ArgumentType vec2(boolean centerIntegers) {
      return new Vec2ArgumentType(centerIntegers);
   }

   public static Vec2f getVec2(CommandContext<ServerCommandSource> context, String name) {
      Vec3d vec3d = ((PosArgument)context.getArgument(name, PosArgument.class)).getPos((ServerCommandSource)context.getSource());
      return new Vec2f((float)vec3d.x, (float)vec3d.z);
   }

   public PosArgument parse(StringReader stringReader) throws CommandSyntaxException {
      int i = stringReader.getCursor();
      if (!stringReader.canRead()) {
         throw INCOMPLETE_EXCEPTION.createWithContext(stringReader);
      } else {
         CoordinateArgument coordinateArgument = CoordinateArgument.parse(stringReader, this.centerIntegers);
         if (stringReader.canRead() && stringReader.peek() == ' ') {
            stringReader.skip();
            CoordinateArgument coordinateArgument2 = CoordinateArgument.parse(stringReader, this.centerIntegers);
            return new DefaultPosArgument(coordinateArgument, new CoordinateArgument(true, (double)0.0F), coordinateArgument2);
         } else {
            stringReader.setCursor(i);
            throw INCOMPLETE_EXCEPTION.createWithContext(stringReader);
         }
      }
   }

   public <S> CompletableFuture<Suggestions> listSuggestions(CommandContext<S> context, SuggestionsBuilder builder) {
      if (!(context.getSource() instanceof CommandSource)) {
         return Suggestions.empty();
      } else {
         String string = builder.getRemaining();
         Collection<CommandSource.RelativePosition> collection;
         if (!string.isEmpty() && string.charAt(0) == '^') {
            collection = Collections.singleton(CommandSource.RelativePosition.ZERO_LOCAL);
         } else {
            collection = ((CommandSource)context.getSource()).getPositionSuggestions();
         }

         return CommandSource.suggestColumnPositions(string, collection, builder, CommandManager.getCommandValidator(this::parse));
      }
   }

   public Collection<String> getExamples() {
      return EXAMPLES;
   }
}
