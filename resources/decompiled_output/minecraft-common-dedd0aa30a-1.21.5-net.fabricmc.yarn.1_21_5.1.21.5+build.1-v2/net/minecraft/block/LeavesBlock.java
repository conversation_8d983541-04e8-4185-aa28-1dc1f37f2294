package net.minecraft.block;

import com.mojang.serialization.MapCodec;
import java.util.OptionalInt;
import net.minecraft.fluid.FluidState;
import net.minecraft.fluid.Fluids;
import net.minecraft.item.ItemPlacementContext;
import net.minecraft.particle.ParticleEffect;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.particle.ParticleUtil;
import net.minecraft.registry.tag.BlockTags;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.state.StateManager;
import net.minecraft.state.property.BooleanProperty;
import net.minecraft.state.property.IntProperty;
import net.minecraft.state.property.Properties;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.random.Random;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.util.shape.VoxelShapes;
import net.minecraft.world.BlockView;
import net.minecraft.world.World;
import net.minecraft.world.WorldAccess;
import net.minecraft.world.WorldView;
import net.minecraft.world.tick.ScheduledTickView;

public abstract class LeavesBlock extends Block implements Waterloggable {
   public static final int MAX_DISTANCE = 7;
   public static final IntProperty DISTANCE;
   public static final BooleanProperty PERSISTENT;
   public static final BooleanProperty WATERLOGGED;
   protected final float leafParticleChance;
   private static final int field_31112 = 1;

   public abstract MapCodec<? extends LeavesBlock> getCodec();

   public LeavesBlock(float leafParticleChance, AbstractBlock.Settings settings) {
      super(settings);
      this.leafParticleChance = leafParticleChance;
      this.setDefaultState((BlockState)((BlockState)((BlockState)((BlockState)this.stateManager.getDefaultState()).with(DISTANCE, 7)).with(PERSISTENT, false)).with(WATERLOGGED, false));
   }

   protected VoxelShape getSidesShape(BlockState state, BlockView world, BlockPos pos) {
      return VoxelShapes.empty();
   }

   protected boolean hasRandomTicks(BlockState state) {
      return (Integer)state.get(DISTANCE) == 7 && !(Boolean)state.get(PERSISTENT);
   }

   protected void randomTick(BlockState state, ServerWorld world, BlockPos pos, Random random) {
      if (this.shouldDecay(state)) {
         dropStacks(state, world, pos);
         world.removeBlock(pos, false);
      }

   }

   protected boolean shouldDecay(BlockState state) {
      return !(Boolean)state.get(PERSISTENT) && (Integer)state.get(DISTANCE) == 7;
   }

   protected void scheduledTick(BlockState state, ServerWorld world, BlockPos pos, Random random) {
      world.setBlockState(pos, updateDistanceFromLogs(state, world, pos), 3);
   }

   protected int getOpacity(BlockState state) {
      return 1;
   }

   protected BlockState getStateForNeighborUpdate(BlockState state, WorldView world, ScheduledTickView tickView, BlockPos pos, Direction direction, BlockPos neighborPos, BlockState neighborState, Random random) {
      if ((Boolean)state.get(WATERLOGGED)) {
         tickView.scheduleFluidTick(pos, Fluids.WATER, Fluids.WATER.getTickRate(world));
      }

      int i = getDistanceFromLog(neighborState) + 1;
      if (i != 1 || (Integer)state.get(DISTANCE) != i) {
         tickView.scheduleBlockTick(pos, this, 1);
      }

      return state;
   }

   private static BlockState updateDistanceFromLogs(BlockState state, WorldAccess world, BlockPos pos) {
      int i = 7;
      BlockPos.Mutable mutable = new BlockPos.Mutable();

      for(Direction direction : Direction.values()) {
         mutable.set(pos, (Direction)direction);
         i = Math.min(i, getDistanceFromLog(world.getBlockState(mutable)) + 1);
         if (i == 1) {
            break;
         }
      }

      return (BlockState)state.with(DISTANCE, i);
   }

   private static int getDistanceFromLog(BlockState state) {
      return getOptionalDistanceFromLog(state).orElse(7);
   }

   public static OptionalInt getOptionalDistanceFromLog(BlockState state) {
      if (state.isIn(BlockTags.LOGS)) {
         return OptionalInt.of(0);
      } else {
         return state.contains(DISTANCE) ? OptionalInt.of((Integer)state.get(DISTANCE)) : OptionalInt.empty();
      }
   }

   protected FluidState getFluidState(BlockState state) {
      return (Boolean)state.get(WATERLOGGED) ? Fluids.WATER.getStill(false) : super.getFluidState(state);
   }

   public void randomDisplayTick(BlockState state, World world, BlockPos pos, Random random) {
      super.randomDisplayTick(state, world, pos, random);
      BlockPos blockPos = pos.down();
      BlockState blockState = world.getBlockState(blockPos);
      spawnWaterParticle(world, pos, random, blockState, blockPos);
      this.spawnLeafParticle(world, pos, random, blockState, blockPos);
   }

   private static void spawnWaterParticle(World world, BlockPos pos, Random random, BlockState state, BlockPos posBelow) {
      if (world.hasRain(pos.up())) {
         if (random.nextInt(15) == 1) {
            if (!state.isOpaque() || !state.isSideSolidFullSquare(world, posBelow, Direction.UP)) {
               ParticleUtil.spawnParticle(world, pos, (Random)random, (ParticleEffect)ParticleTypes.DRIPPING_WATER);
            }
         }
      }
   }

   private void spawnLeafParticle(World world, BlockPos pos, Random random, BlockState state, BlockPos posBelow) {
      if (!(random.nextFloat() >= this.leafParticleChance)) {
         if (!isFaceFullSquare(state.getCollisionShape(world, posBelow), Direction.UP)) {
            this.spawnLeafParticle(world, pos, random);
         }
      }
   }

   protected abstract void spawnLeafParticle(World world, BlockPos pos, Random random);

   protected void appendProperties(StateManager.Builder<Block, BlockState> builder) {
      builder.add(DISTANCE, PERSISTENT, WATERLOGGED);
   }

   public BlockState getPlacementState(ItemPlacementContext ctx) {
      FluidState fluidState = ctx.getWorld().getFluidState(ctx.getBlockPos());
      BlockState blockState = (BlockState)((BlockState)this.getDefaultState().with(PERSISTENT, true)).with(WATERLOGGED, fluidState.getFluid() == Fluids.WATER);
      return updateDistanceFromLogs(blockState, ctx.getWorld(), ctx.getBlockPos());
   }

   static {
      DISTANCE = Properties.DISTANCE_1_7;
      PERSISTENT = Properties.PERSISTENT;
      WATERLOGGED = Properties.WATERLOGGED;
   }
}
