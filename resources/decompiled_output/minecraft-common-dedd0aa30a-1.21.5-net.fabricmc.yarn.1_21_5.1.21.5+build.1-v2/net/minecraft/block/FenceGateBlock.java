package net.minecraft.block;

import com.google.common.collect.Maps;
import com.mojang.serialization.MapCodec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.Map;
import java.util.function.BiConsumer;
import net.minecraft.entity.Entity;
import net.minecraft.entity.ai.pathing.NavigationType;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemPlacementContext;
import net.minecraft.item.ItemStack;
import net.minecraft.registry.tag.BlockTags;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundCategory;
import net.minecraft.state.StateManager;
import net.minecraft.state.property.BooleanProperty;
import net.minecraft.state.property.Properties;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Util;
import net.minecraft.util.function.BooleanBiFunction;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.random.Random;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.util.shape.VoxelShapes;
import net.minecraft.world.BlockView;
import net.minecraft.world.World;
import net.minecraft.world.WorldView;
import net.minecraft.world.block.WireOrientation;
import net.minecraft.world.event.GameEvent;
import net.minecraft.world.explosion.Explosion;
import net.minecraft.world.tick.ScheduledTickView;
import org.jetbrains.annotations.Nullable;

public class FenceGateBlock extends HorizontalFacingBlock {
   public static final MapCodec<FenceGateBlock> CODEC = RecordCodecBuilder.mapCodec((instance) -> instance.group(WoodType.CODEC.fieldOf("wood_type").forGetter((block) -> block.type), createSettingsCodec()).apply(instance, FenceGateBlock::new));
   public static final BooleanProperty OPEN;
   public static final BooleanProperty POWERED;
   public static final BooleanProperty IN_WALL;
   private static final Map<Direction.Axis, VoxelShape> REGULAR_OUTLINE_SHAPES;
   private static final Map<Direction.Axis, VoxelShape> IN_WALL_OUTLINE_SHAPES;
   private static final Map<Direction.Axis, VoxelShape> CLOSED_COLLISION_SHAPES;
   private static final Map<Direction.Axis, VoxelShape> CLOSED_SIDES_SHAPES;
   private static final Map<Direction.Axis, VoxelShape> REGULAR_CULLING_SHAPES;
   private static final Map<Direction.Axis, VoxelShape> IN_WALL_CULLING_SHAPES;
   private final WoodType type;

   public MapCodec<FenceGateBlock> getCodec() {
      return CODEC;
   }

   public FenceGateBlock(WoodType type, AbstractBlock.Settings settings) {
      super(settings.sounds(type.soundType()));
      this.type = type;
      this.setDefaultState((BlockState)((BlockState)((BlockState)((BlockState)this.stateManager.getDefaultState()).with(OPEN, false)).with(POWERED, false)).with(IN_WALL, false));
   }

   protected VoxelShape getOutlineShape(BlockState state, BlockView world, BlockPos pos, ShapeContext context) {
      Direction.Axis axis = ((Direction)state.get(FACING)).getAxis();
      return (VoxelShape)((Boolean)state.get(IN_WALL) ? IN_WALL_OUTLINE_SHAPES : REGULAR_OUTLINE_SHAPES).get(axis);
   }

   protected BlockState getStateForNeighborUpdate(BlockState state, WorldView world, ScheduledTickView tickView, BlockPos pos, Direction direction, BlockPos neighborPos, BlockState neighborState, Random random) {
      Direction.Axis axis = direction.getAxis();
      if (((Direction)state.get(FACING)).rotateYClockwise().getAxis() != axis) {
         return super.getStateForNeighborUpdate(state, world, tickView, pos, direction, neighborPos, neighborState, random);
      } else {
         boolean bl = this.isWall(neighborState) || this.isWall(world.getBlockState(pos.offset(direction.getOpposite())));
         return (BlockState)state.with(IN_WALL, bl);
      }
   }

   protected VoxelShape getSidesShape(BlockState state, BlockView world, BlockPos pos) {
      Direction.Axis axis = ((Direction)state.get(FACING)).getAxis();
      return (Boolean)state.get(OPEN) ? VoxelShapes.empty() : (VoxelShape)CLOSED_SIDES_SHAPES.get(axis);
   }

   protected VoxelShape getCollisionShape(BlockState state, BlockView world, BlockPos pos, ShapeContext context) {
      Direction.Axis axis = ((Direction)state.get(FACING)).getAxis();
      return (Boolean)state.get(OPEN) ? VoxelShapes.empty() : (VoxelShape)CLOSED_COLLISION_SHAPES.get(axis);
   }

   protected VoxelShape getCullingShape(BlockState state) {
      Direction.Axis axis = ((Direction)state.get(FACING)).getAxis();
      return (VoxelShape)((Boolean)state.get(IN_WALL) ? IN_WALL_CULLING_SHAPES : REGULAR_CULLING_SHAPES).get(axis);
   }

   protected boolean canPathfindThrough(BlockState state, NavigationType type) {
      switch (type) {
         case LAND -> {
            return (Boolean)state.get(OPEN);
         }
         case WATER -> {
            return false;
         }
         case AIR -> {
            return (Boolean)state.get(OPEN);
         }
         default -> {
            return false;
         }
      }
   }

   public BlockState getPlacementState(ItemPlacementContext ctx) {
      World world = ctx.getWorld();
      BlockPos blockPos = ctx.getBlockPos();
      boolean bl = world.isReceivingRedstonePower(blockPos);
      Direction direction = ctx.getHorizontalPlayerFacing();
      Direction.Axis axis = direction.getAxis();
      boolean bl2 = axis == Direction.Axis.Z && (this.isWall(world.getBlockState(blockPos.west())) || this.isWall(world.getBlockState(blockPos.east()))) || axis == Direction.Axis.X && (this.isWall(world.getBlockState(blockPos.north())) || this.isWall(world.getBlockState(blockPos.south())));
      return (BlockState)((BlockState)((BlockState)((BlockState)this.getDefaultState().with(FACING, direction)).with(OPEN, bl)).with(POWERED, bl)).with(IN_WALL, bl2);
   }

   private boolean isWall(BlockState state) {
      return state.isIn(BlockTags.WALLS);
   }

   protected ActionResult onUse(BlockState state, World world, BlockPos pos, PlayerEntity player, BlockHitResult hit) {
      if ((Boolean)state.get(OPEN)) {
         state = (BlockState)state.with(OPEN, false);
         world.setBlockState(pos, state, 10);
      } else {
         Direction direction = player.getHorizontalFacing();
         if (state.get(FACING) == direction.getOpposite()) {
            state = (BlockState)state.with(FACING, direction);
         }

         state = (BlockState)state.with(OPEN, true);
         world.setBlockState(pos, state, 10);
      }

      boolean bl = (Boolean)state.get(OPEN);
      world.playSound(player, pos, bl ? this.type.fenceGateOpen() : this.type.fenceGateClose(), SoundCategory.BLOCKS, 1.0F, world.getRandom().nextFloat() * 0.1F + 0.9F);
      world.emitGameEvent(player, bl ? GameEvent.BLOCK_OPEN : GameEvent.BLOCK_CLOSE, pos);
      return ActionResult.SUCCESS;
   }

   protected void onExploded(BlockState state, ServerWorld world, BlockPos pos, Explosion explosion, BiConsumer<ItemStack, BlockPos> stackMerger) {
      if (explosion.canTriggerBlocks() && !(Boolean)state.get(POWERED)) {
         boolean bl = (Boolean)state.get(OPEN);
         world.setBlockState(pos, (BlockState)state.with(OPEN, !bl));
         world.playSound((Entity)null, pos, bl ? this.type.fenceGateClose() : this.type.fenceGateOpen(), SoundCategory.BLOCKS, 1.0F, world.getRandom().nextFloat() * 0.1F + 0.9F);
         world.emitGameEvent(bl ? GameEvent.BLOCK_CLOSE : GameEvent.BLOCK_OPEN, pos, GameEvent.Emitter.of(state));
      }

      super.onExploded(state, world, pos, explosion, stackMerger);
   }

   protected void neighborUpdate(BlockState state, World world, BlockPos pos, Block sourceBlock, @Nullable WireOrientation wireOrientation, boolean notify) {
      if (!world.isClient) {
         boolean bl = world.isReceivingRedstonePower(pos);
         if ((Boolean)state.get(POWERED) != bl) {
            world.setBlockState(pos, (BlockState)((BlockState)state.with(POWERED, bl)).with(OPEN, bl), 2);
            if ((Boolean)state.get(OPEN) != bl) {
               world.playSound((Entity)null, pos, bl ? this.type.fenceGateOpen() : this.type.fenceGateClose(), SoundCategory.BLOCKS, 1.0F, world.getRandom().nextFloat() * 0.1F + 0.9F);
               world.emitGameEvent((Entity)null, bl ? GameEvent.BLOCK_OPEN : GameEvent.BLOCK_CLOSE, pos);
            }
         }

      }
   }

   protected void appendProperties(StateManager.Builder<Block, BlockState> builder) {
      builder.add(FACING, OPEN, POWERED, IN_WALL);
   }

   public static boolean canWallConnect(BlockState state, Direction side) {
      return ((Direction)state.get(FACING)).getAxis() == side.rotateYClockwise().getAxis();
   }

   static {
      OPEN = Properties.OPEN;
      POWERED = Properties.POWERED;
      IN_WALL = Properties.IN_WALL;
      REGULAR_OUTLINE_SHAPES = VoxelShapes.createHorizontalAxisShapeMap(Block.createCuboidShape((double)16.0F, (double)16.0F, (double)4.0F));
      IN_WALL_OUTLINE_SHAPES = Maps.newEnumMap(Util.transformMapValues(REGULAR_OUTLINE_SHAPES, (shape) -> VoxelShapes.combineAndSimplify(shape, Block.createColumnShape((double)16.0F, (double)13.0F, (double)16.0F), BooleanBiFunction.ONLY_FIRST)));
      CLOSED_COLLISION_SHAPES = VoxelShapes.createHorizontalAxisShapeMap(Block.createColumnShape((double)16.0F, (double)4.0F, (double)0.0F, (double)24.0F));
      CLOSED_SIDES_SHAPES = VoxelShapes.createHorizontalAxisShapeMap(Block.createColumnShape((double)16.0F, (double)4.0F, (double)5.0F, (double)24.0F));
      REGULAR_CULLING_SHAPES = VoxelShapes.createHorizontalAxisShapeMap(VoxelShapes.union(Block.createCuboidShape((double)0.0F, (double)5.0F, (double)7.0F, (double)2.0F, (double)16.0F, (double)9.0F), Block.createCuboidShape((double)14.0F, (double)5.0F, (double)7.0F, (double)16.0F, (double)16.0F, (double)9.0F)));
      IN_WALL_CULLING_SHAPES = Maps.newEnumMap(Util.transformMapValues(REGULAR_CULLING_SHAPES, (shape) -> shape.offset((double)0.0F, (double)-0.1875F, (double)0.0F).simplify()));
   }
}
