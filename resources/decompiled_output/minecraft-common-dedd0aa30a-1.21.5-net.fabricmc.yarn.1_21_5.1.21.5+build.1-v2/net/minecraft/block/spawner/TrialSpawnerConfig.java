package net.minecraft.block.spawner;

import com.mojang.serialization.Codec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.Optional;
import net.minecraft.entity.EntityType;
import net.minecraft.loot.LootTable;
import net.minecraft.loot.LootTables;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.registry.Registries;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.registry.entry.RegistryElementCodec;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.util.collection.Pool;

public record TrialSpawnerConfig(int spawnRange, float totalMobs, float simultaneousMobs, float totalMobsAddedPerPlayer, float simultaneousMobsAddedPerPlayer, int ticksBetweenSpawn, Pool<MobSpawnerEntry> spawnPotentials, Pool<RegistryKey<LootTable>> lootTablesToEject, RegistryKey<LootTable> itemsToDropWhenOminous) {
   public static final TrialSpawnerConfig DEFAULT = builder().build();
   public static final Codec<TrialSpawnerConfig> CODEC = RecordCodecBuilder.create((instance) -> instance.group(Codec.intRange(1, 128).optionalFieldOf("spawn_range", DEFAULT.spawnRange).forGetter(TrialSpawnerConfig::spawnRange), Codec.floatRange(0.0F, Float.MAX_VALUE).optionalFieldOf("total_mobs", DEFAULT.totalMobs).forGetter(TrialSpawnerConfig::totalMobs), Codec.floatRange(0.0F, Float.MAX_VALUE).optionalFieldOf("simultaneous_mobs", DEFAULT.simultaneousMobs).forGetter(TrialSpawnerConfig::simultaneousMobs), Codec.floatRange(0.0F, Float.MAX_VALUE).optionalFieldOf("total_mobs_added_per_player", DEFAULT.totalMobsAddedPerPlayer).forGetter(TrialSpawnerConfig::totalMobsAddedPerPlayer), Codec.floatRange(0.0F, Float.MAX_VALUE).optionalFieldOf("simultaneous_mobs_added_per_player", DEFAULT.simultaneousMobsAddedPerPlayer).forGetter(TrialSpawnerConfig::simultaneousMobsAddedPerPlayer), Codec.intRange(0, Integer.MAX_VALUE).optionalFieldOf("ticks_between_spawn", DEFAULT.ticksBetweenSpawn).forGetter(TrialSpawnerConfig::ticksBetweenSpawn), MobSpawnerEntry.DATA_POOL_CODEC.optionalFieldOf("spawn_potentials", Pool.empty()).forGetter(TrialSpawnerConfig::spawnPotentials), Pool.createCodec(LootTable.TABLE_KEY).optionalFieldOf("loot_tables_to_eject", DEFAULT.lootTablesToEject).forGetter(TrialSpawnerConfig::lootTablesToEject), LootTable.TABLE_KEY.optionalFieldOf("items_to_drop_when_ominous", DEFAULT.itemsToDropWhenOminous).forGetter(TrialSpawnerConfig::itemsToDropWhenOminous)).apply(instance, TrialSpawnerConfig::new));
   public static final Codec<RegistryEntry<TrialSpawnerConfig>> ENTRY_CODEC;

   public int getTotalMobs(int additionalPlayers) {
      return (int)Math.floor((double)(this.totalMobs + this.totalMobsAddedPerPlayer * (float)additionalPlayers));
   }

   public int getSimultaneousMobs(int additionalPlayers) {
      return (int)Math.floor((double)(this.simultaneousMobs + this.simultaneousMobsAddedPerPlayer * (float)additionalPlayers));
   }

   public long getCooldownLength() {
      return 160L;
   }

   public static Builder builder() {
      return new Builder();
   }

   public TrialSpawnerConfig withSpawnPotential(EntityType<?> entityType) {
      NbtCompound nbtCompound = new NbtCompound();
      nbtCompound.putString("id", Registries.ENTITY_TYPE.getId(entityType).toString());
      MobSpawnerEntry mobSpawnerEntry = new MobSpawnerEntry(nbtCompound, Optional.empty(), Optional.empty());
      return new TrialSpawnerConfig(this.spawnRange, this.totalMobs, this.simultaneousMobs, this.totalMobsAddedPerPlayer, this.simultaneousMobsAddedPerPlayer, this.ticksBetweenSpawn, Pool.of(mobSpawnerEntry), this.lootTablesToEject, this.itemsToDropWhenOminous);
   }

   static {
      ENTRY_CODEC = RegistryElementCodec.<RegistryEntry<TrialSpawnerConfig>>of(RegistryKeys.TRIAL_SPAWNER, CODEC);
   }

   public static class Builder {
      private int spawnRange = 4;
      private float totalMobs = 6.0F;
      private float simultaneousMobs = 2.0F;
      private float totalMobsAddedPerPlayer = 2.0F;
      private float simultaneousMobsAddedPerPlayer = 1.0F;
      private int ticksBetweenSpawn = 40;
      private Pool<MobSpawnerEntry> spawnPotentials = Pool.<MobSpawnerEntry>empty();
      private Pool<RegistryKey<LootTable>> lootTablesToEject;
      private RegistryKey<LootTable> itemsToDropWhenOminous;

      public Builder() {
         this.lootTablesToEject = Pool.<RegistryKey<LootTable>>builder().add(LootTables.TRIAL_CHAMBER_CONSUMABLES_SPAWNER).add(LootTables.TRIAL_CHAMBER_KEY_SPAWNER).build();
         this.itemsToDropWhenOminous = LootTables.TRIAL_CHAMBER_ITEMS_TO_DROP_WHEN_OMINOUS_SPAWNER;
      }

      public Builder spawnRange(int spawnRange) {
         this.spawnRange = spawnRange;
         return this;
      }

      public Builder totalMobs(float totalMobs) {
         this.totalMobs = totalMobs;
         return this;
      }

      public Builder simultaneousMobs(float simultaneousMobs) {
         this.simultaneousMobs = simultaneousMobs;
         return this;
      }

      public Builder totalMobsAddedPerPlayer(float totalMobsAddedPerPlayer) {
         this.totalMobsAddedPerPlayer = totalMobsAddedPerPlayer;
         return this;
      }

      public Builder simultaneousMobsAddedPerPlayer(float simultaneousMobsAddedPerPlayer) {
         this.simultaneousMobsAddedPerPlayer = simultaneousMobsAddedPerPlayer;
         return this;
      }

      public Builder ticksBetweenSpawn(int ticksBetweenSpawn) {
         this.ticksBetweenSpawn = ticksBetweenSpawn;
         return this;
      }

      public Builder spawnPotentials(Pool<MobSpawnerEntry> spawnPotentials) {
         this.spawnPotentials = spawnPotentials;
         return this;
      }

      public Builder lootTablesToEject(Pool<RegistryKey<LootTable>> lootTablesToEject) {
         this.lootTablesToEject = lootTablesToEject;
         return this;
      }

      public Builder itemsToDropWhenOminous(RegistryKey<LootTable> itemsToDropWhenOminous) {
         this.itemsToDropWhenOminous = itemsToDropWhenOminous;
         return this;
      }

      public TrialSpawnerConfig build() {
         return new TrialSpawnerConfig(this.spawnRange, this.totalMobs, this.simultaneousMobs, this.totalMobsAddedPerPlayer, this.simultaneousMobsAddedPerPlayer, this.ticksBetweenSpawn, this.spawnPotentials, this.lootTablesToEject, this.itemsToDropWhenOminous);
      }
   }
}
