package net.minecraft.entity.ai.brain.task;

import com.mojang.datafixers.util.Pair;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.brain.Brain;
import net.minecraft.entity.ai.brain.MemoryModuleState;
import net.minecraft.entity.ai.brain.MemoryModuleType;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.collection.WeightedList;

public class CompositeTask<E extends LivingEntity> implements Task<E> {
   private final Map<MemoryModuleType<?>, MemoryModuleState> requiredMemoryState;
   private final Set<MemoryModuleType<?>> memoriesToForgetWhenStopped;
   private final Order order;
   private final RunMode runMode;
   private final WeightedList<Task<? super E>> tasks = new WeightedList<Task<? super E>>();
   private MultiTickTask.Status status;

   public CompositeTask(Map<MemoryModuleType<?>, MemoryModuleState> requiredMemoryState, Set<MemoryModuleType<?>> memoriesToForgetWhenStopped, Order order, RunMode runMode, List<Pair<? extends Task<? super E>, Integer>> tasks) {
      this.status = MultiTickTask.Status.STOPPED;
      this.requiredMemoryState = requiredMemoryState;
      this.memoriesToForgetWhenStopped = memoriesToForgetWhenStopped;
      this.order = order;
      this.runMode = runMode;
      tasks.forEach((task) -> this.tasks.add((Task)task.getFirst(), (Integer)task.getSecond()));
   }

   public MultiTickTask.Status getStatus() {
      return this.status;
   }

   private boolean shouldStart(E entity) {
      for(Map.Entry<MemoryModuleType<?>, MemoryModuleState> entry : this.requiredMemoryState.entrySet()) {
         MemoryModuleType<?> memoryModuleType = (MemoryModuleType)entry.getKey();
         MemoryModuleState memoryModuleState = (MemoryModuleState)entry.getValue();
         if (!entity.getBrain().isMemoryInState(memoryModuleType, memoryModuleState)) {
            return false;
         }
      }

      return true;
   }

   public final boolean tryStarting(ServerWorld world, E entity, long time) {
      if (this.shouldStart(entity)) {
         this.status = MultiTickTask.Status.RUNNING;
         this.order.apply(this.tasks);
         this.runMode.run(this.tasks.stream(), world, entity, time);
         return true;
      } else {
         return false;
      }
   }

   public final void tick(ServerWorld world, E entity, long time) {
      this.tasks.stream().filter((task) -> task.getStatus() == MultiTickTask.Status.RUNNING).forEach((task) -> task.tick(world, entity, time));
      if (this.tasks.stream().noneMatch((task) -> task.getStatus() == MultiTickTask.Status.RUNNING)) {
         this.stop(world, entity, time);
      }

   }

   public final void stop(ServerWorld world, E entity, long time) {
      this.status = MultiTickTask.Status.STOPPED;
      this.tasks.stream().filter((task) -> task.getStatus() == MultiTickTask.Status.RUNNING).forEach((task) -> task.stop(world, entity, time));
      Set var10000 = this.memoriesToForgetWhenStopped;
      Brain var10001 = ((LivingEntity)entity).getBrain();
      Objects.requireNonNull(var10001);
      var10000.forEach(var10001::forget);
   }

   public String getName() {
      return this.getClass().getSimpleName();
   }

   public String toString() {
      Set<? extends Task<? super E>> set = (Set)this.tasks.stream().filter((task) -> task.getStatus() == MultiTickTask.Status.RUNNING).collect(Collectors.toSet());
      String var10000 = this.getClass().getSimpleName();
      return "(" + var10000 + "): " + String.valueOf(set);
   }

   public static enum Order {
      ORDERED((list) -> {
      }),
      SHUFFLED(WeightedList::shuffle);

      private final Consumer<WeightedList<?>> listModifier;

      private Order(final Consumer<WeightedList<?>> listModifier) {
         this.listModifier = listModifier;
      }

      public void apply(WeightedList<?> list) {
         this.listModifier.accept(list);
      }
   }

   public static enum RunMode {
      RUN_ONE {
         public <E extends LivingEntity> void run(Stream<Task<? super E>> tasks, ServerWorld world, E entity, long time) {
            tasks.filter((task) -> task.getStatus() == MultiTickTask.Status.STOPPED).filter((task) -> task.tryStarting(world, entity, time)).findFirst();
         }
      },
      TRY_ALL {
         public <E extends LivingEntity> void run(Stream<Task<? super E>> tasks, ServerWorld world, E entity, long time) {
            tasks.filter((task) -> task.getStatus() == MultiTickTask.Status.STOPPED).forEach((task) -> task.tryStarting(world, entity, time));
         }
      };

      public abstract <E extends LivingEntity> void run(Stream<Task<? super E>> tasks, ServerWorld world, E entity, long time);
   }
}
