package net.minecraft.item.map;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mojang.datafixers.util.Pair;
import com.mojang.logging.LogUtils;
import com.mojang.serialization.Codec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import io.netty.buffer.ByteBuf;
import java.nio.ByteBuffer;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.component.type.MapColorComponent;
import net.minecraft.component.type.MapDecorationsComponent;
import net.minecraft.component.type.MapIdComponent;
import net.minecraft.datafixer.DataFixTypes;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.decoration.ItemFrameEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.network.codec.PacketCodec;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.s2c.play.MapUpdateS2CPacket;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.registry.tag.ItemTags;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.MathHelper;
import net.minecraft.world.BlockView;
import net.minecraft.world.PersistentState;
import net.minecraft.world.PersistentStateType;
import net.minecraft.world.World;
import net.minecraft.world.WorldAccess;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;

public class MapState extends PersistentState {
   private static final Logger LOGGER = LogUtils.getLogger();
   private static final int SIZE = 128;
   private static final int SIZE_HALF = 64;
   public static final int MAX_SCALE = 4;
   public static final int MAX_DECORATIONS = 256;
   private static final String FRAME_PREFIX = "frame-";
   public static final Codec<MapState> CODEC = RecordCodecBuilder.create((instance) -> instance.group(World.CODEC.fieldOf("dimension").forGetter((mapState) -> mapState.dimension), Codec.INT.fieldOf("xCenter").forGetter((mapState) -> mapState.centerX), Codec.INT.fieldOf("zCenter").forGetter((mapState) -> mapState.centerZ), Codec.BYTE.optionalFieldOf("scale", (byte)0).forGetter((mapState) -> mapState.scale), Codec.BYTE_BUFFER.fieldOf("colors").forGetter((mapState) -> ByteBuffer.wrap(mapState.colors)), Codec.BOOL.optionalFieldOf("trackingPosition", true).forGetter((mapState) -> mapState.showDecorations), Codec.BOOL.optionalFieldOf("unlimitedTracking", false).forGetter((mapState) -> mapState.unlimitedTracking), Codec.BOOL.optionalFieldOf("locked", false).forGetter((mapState) -> mapState.locked), MapBannerMarker.CODEC.listOf().optionalFieldOf("banners", List.of()).forGetter((mapState) -> List.copyOf(mapState.banners.values())), MapFrameMarker.CODEC.listOf().optionalFieldOf("frames", List.of()).forGetter((mapState) -> List.copyOf(mapState.frames.values()))).apply(instance, MapState::new));
   public final int centerX;
   public final int centerZ;
   public final RegistryKey<World> dimension;
   private final boolean showDecorations;
   private final boolean unlimitedTracking;
   public final byte scale;
   public byte[] colors;
   public final boolean locked;
   private final List<PlayerUpdateTracker> updateTrackers;
   private final Map<PlayerEntity, PlayerUpdateTracker> updateTrackersByPlayer;
   private final Map<String, MapBannerMarker> banners;
   final Map<String, MapDecoration> decorations;
   private final Map<String, MapFrameMarker> frames;
   private int decorationCount;

   public static PersistentStateType<MapState> createStateType(MapIdComponent mapId) {
      return new PersistentStateType<MapState>(mapId.asString(), () -> {
         throw new IllegalStateException("Should never create an empty map saved data");
      }, CODEC, DataFixTypes.SAVED_DATA_MAP_DATA);
   }

   private MapState(int centerX, int centerZ, byte scale, boolean showDecorations, boolean unlimitedTracking, boolean locked, RegistryKey<World> dimension) {
      this.colors = new byte[16384];
      this.updateTrackers = Lists.newArrayList();
      this.updateTrackersByPlayer = Maps.newHashMap();
      this.banners = Maps.newHashMap();
      this.decorations = Maps.newLinkedHashMap();
      this.frames = Maps.newHashMap();
      this.scale = scale;
      this.centerX = centerX;
      this.centerZ = centerZ;
      this.dimension = dimension;
      this.showDecorations = showDecorations;
      this.unlimitedTracking = unlimitedTracking;
      this.locked = locked;
   }

   private MapState(RegistryKey<World> dimension, int centerX, int centerZ, byte scale, ByteBuffer colors, boolean showDecorations, boolean unlimitedTracking, boolean locked, List<MapBannerMarker> banners, List<MapFrameMarker> frames) {
      this(centerX, centerZ, (byte)MathHelper.clamp(scale, 0, 4), showDecorations, unlimitedTracking, locked, dimension);
      if (colors.array().length == 16384) {
         this.colors = colors.array();
      }

      for(MapBannerMarker mapBannerMarker : banners) {
         this.banners.put(mapBannerMarker.getKey(), mapBannerMarker);
         this.addDecoration(mapBannerMarker.getDecorationType(), (WorldAccess)null, mapBannerMarker.getKey(), (double)mapBannerMarker.pos().getX(), (double)mapBannerMarker.pos().getZ(), (double)180.0F, (Text)mapBannerMarker.name().orElse((Object)null));
      }

      for(MapFrameMarker mapFrameMarker : frames) {
         this.frames.put(mapFrameMarker.getKey(), mapFrameMarker);
         this.addDecoration(MapDecorationTypes.FRAME, (WorldAccess)null, getFrameDecorationKey(mapFrameMarker.entityId()), (double)mapFrameMarker.pos().getX(), (double)mapFrameMarker.pos().getZ(), (double)mapFrameMarker.rotation(), (Text)null);
      }

   }

   public static MapState of(double centerX, double centerZ, byte scale, boolean showDecorations, boolean unlimitedTracking, RegistryKey<World> dimension) {
      int i = 128 * (1 << scale);
      int j = MathHelper.floor((centerX + (double)64.0F) / (double)i);
      int k = MathHelper.floor((centerZ + (double)64.0F) / (double)i);
      int l = j * i + i / 2 - 64;
      int m = k * i + i / 2 - 64;
      return new MapState(l, m, scale, showDecorations, unlimitedTracking, false, dimension);
   }

   public static MapState of(byte scale, boolean locked, RegistryKey<World> dimension) {
      return new MapState(0, 0, scale, false, false, locked, dimension);
   }

   public MapState copy() {
      MapState mapState = new MapState(this.centerX, this.centerZ, this.scale, this.showDecorations, this.unlimitedTracking, true, this.dimension);
      mapState.banners.putAll(this.banners);
      mapState.decorations.putAll(this.decorations);
      mapState.decorationCount = this.decorationCount;
      System.arraycopy(this.colors, 0, mapState.colors, 0, this.colors.length);
      return mapState;
   }

   public MapState zoomOut() {
      return of((double)this.centerX, (double)this.centerZ, (byte)MathHelper.clamp(this.scale + 1, 0, 4), this.showDecorations, this.unlimitedTracking, this.dimension);
   }

   private static Predicate<ItemStack> getEqualPredicate(ItemStack stack) {
      MapIdComponent mapIdComponent = (MapIdComponent)stack.get(DataComponentTypes.MAP_ID);
      return (other) -> {
         if (other == stack) {
            return true;
         } else {
            return other.isOf(stack.getItem()) && Objects.equals(mapIdComponent, other.get(DataComponentTypes.MAP_ID));
         }
      };
   }

   public void update(PlayerEntity player, ItemStack stack) {
      if (!this.updateTrackersByPlayer.containsKey(player)) {
         PlayerUpdateTracker playerUpdateTracker = new PlayerUpdateTracker(player);
         this.updateTrackersByPlayer.put(player, playerUpdateTracker);
         this.updateTrackers.add(playerUpdateTracker);
      }

      Predicate<ItemStack> predicate = getEqualPredicate(stack);
      if (!player.getInventory().contains(predicate)) {
         this.removeDecoration(player.getName().getString());
      }

      for(int i = 0; i < this.updateTrackers.size(); ++i) {
         PlayerUpdateTracker playerUpdateTracker2 = (PlayerUpdateTracker)this.updateTrackers.get(i);
         PlayerEntity playerEntity = playerUpdateTracker2.player;
         String string = playerEntity.getName().getString();
         if (!playerEntity.isRemoved() && (playerEntity.getInventory().contains(predicate) || stack.isInFrame())) {
            if (!stack.isInFrame() && playerEntity.getWorld().getRegistryKey() == this.dimension && this.showDecorations) {
               this.addDecoration(MapDecorationTypes.PLAYER, playerEntity.getWorld(), string, playerEntity.getX(), playerEntity.getZ(), (double)playerEntity.getYaw(), (Text)null);
            }
         } else {
            this.updateTrackersByPlayer.remove(playerEntity);
            this.updateTrackers.remove(playerUpdateTracker2);
            this.removeDecoration(string);
         }

         if (!playerEntity.equals(player) && hasMapInvisibilityEquipment(playerEntity)) {
            this.removeDecoration(string);
         }
      }

      if (stack.isInFrame() && this.showDecorations) {
         ItemFrameEntity itemFrameEntity = stack.getFrame();
         BlockPos blockPos = itemFrameEntity.getAttachedBlockPos();
         MapFrameMarker mapFrameMarker = (MapFrameMarker)this.frames.get(MapFrameMarker.getKey(blockPos));
         if (mapFrameMarker != null && itemFrameEntity.getId() != mapFrameMarker.entityId() && this.frames.containsKey(mapFrameMarker.getKey())) {
            this.removeDecoration(getFrameDecorationKey(mapFrameMarker.entityId()));
         }

         MapFrameMarker mapFrameMarker2 = new MapFrameMarker(blockPos, itemFrameEntity.getHorizontalFacing().getHorizontalQuarterTurns() * 90, itemFrameEntity.getId());
         this.addDecoration(MapDecorationTypes.FRAME, player.getWorld(), getFrameDecorationKey(itemFrameEntity.getId()), (double)blockPos.getX(), (double)blockPos.getZ(), (double)(itemFrameEntity.getHorizontalFacing().getHorizontalQuarterTurns() * 90), (Text)null);
         MapFrameMarker mapFrameMarker3 = (MapFrameMarker)this.frames.put(mapFrameMarker2.getKey(), mapFrameMarker2);
         if (!mapFrameMarker2.equals(mapFrameMarker3)) {
            this.markDirty();
         }
      }

      MapDecorationsComponent mapDecorationsComponent = (MapDecorationsComponent)stack.getOrDefault(DataComponentTypes.MAP_DECORATIONS, MapDecorationsComponent.DEFAULT);
      if (!this.decorations.keySet().containsAll(mapDecorationsComponent.decorations().keySet())) {
         mapDecorationsComponent.decorations().forEach((id, decoration) -> {
            if (!this.decorations.containsKey(id)) {
               this.addDecoration(decoration.type(), player.getWorld(), id, decoration.x(), decoration.z(), (double)decoration.rotation(), (Text)null);
            }

         });
      }

   }

   private static boolean hasMapInvisibilityEquipment(PlayerEntity player) {
      for(EquipmentSlot equipmentSlot : EquipmentSlot.values()) {
         if (equipmentSlot != EquipmentSlot.MAINHAND && equipmentSlot != EquipmentSlot.OFFHAND && player.getEquippedStack(equipmentSlot).isIn(ItemTags.MAP_INVISIBILITY_EQUIPMENT)) {
            return true;
         }
      }

      return false;
   }

   private void removeDecoration(String id) {
      MapDecoration mapDecoration = (MapDecoration)this.decorations.remove(id);
      if (mapDecoration != null && ((MapDecorationType)mapDecoration.type().value()).trackCount()) {
         --this.decorationCount;
      }

      this.markDecorationsDirty();
   }

   public static void addDecorationsNbt(ItemStack stack, BlockPos pos, String id, RegistryEntry<MapDecorationType> decorationType) {
      MapDecorationsComponent.Decoration decoration = new MapDecorationsComponent.Decoration(decorationType, (double)pos.getX(), (double)pos.getZ(), 180.0F);
      stack.apply(DataComponentTypes.MAP_DECORATIONS, MapDecorationsComponent.DEFAULT, (decorations) -> decorations.with(id, decoration));
      if (((MapDecorationType)decorationType.value()).hasMapColor()) {
         stack.set(DataComponentTypes.MAP_COLOR, new MapColorComponent(((MapDecorationType)decorationType.value()).mapColor()));
      }

   }

   private void addDecoration(RegistryEntry<MapDecorationType> type, @Nullable WorldAccess world, String key, double x, double z, double rotation, @Nullable Text text) {
      int i = 1 << this.scale;
      float f = (float)(x - (double)this.centerX) / (float)i;
      float g = (float)(z - (double)this.centerZ) / (float)i;
      Marker marker = this.getMarker(type, world, rotation, f, g);
      if (marker == null) {
         this.removeDecoration(key);
      } else {
         MapDecoration mapDecoration = new MapDecoration(marker.type(), marker.x(), marker.y(), marker.rot(), Optional.ofNullable(text));
         MapDecoration mapDecoration2 = (MapDecoration)this.decorations.put(key, mapDecoration);
         if (!mapDecoration.equals(mapDecoration2)) {
            if (mapDecoration2 != null && ((MapDecorationType)mapDecoration2.type().value()).trackCount()) {
               --this.decorationCount;
            }

            if (((MapDecorationType)marker.type().value()).trackCount()) {
               ++this.decorationCount;
            }

            this.markDecorationsDirty();
         }

      }
   }

   @Nullable
   private Marker getMarker(RegistryEntry<MapDecorationType> type, @Nullable WorldAccess world, double rotation, float dx, float dz) {
      byte b = offsetToMarkerPosition(dx);
      byte c = offsetToMarkerPosition(dz);
      if (type.matches(MapDecorationTypes.PLAYER)) {
         Pair<RegistryEntry<MapDecorationType>, Byte> pair = this.getPlayerMarkerAndRotation(type, world, rotation, dx, dz);
         return pair == null ? null : new Marker((RegistryEntry)pair.getFirst(), b, c, (Byte)pair.getSecond());
      } else {
         return !isInBounds(dx, dz) && !this.unlimitedTracking ? null : new Marker(type, b, c, this.getPlayerMarkerRotation(world, rotation));
      }
   }

   @Nullable
   private Pair<RegistryEntry<MapDecorationType>, Byte> getPlayerMarkerAndRotation(RegistryEntry<MapDecorationType> type, @Nullable WorldAccess world, double rotation, float dx, float dz) {
      if (isInBounds(dx, dz)) {
         return Pair.of(type, this.getPlayerMarkerRotation(world, rotation));
      } else {
         RegistryEntry<MapDecorationType> registryEntry = this.getPlayerMarker(dx, dz);
         return registryEntry == null ? null : Pair.of(registryEntry, (byte)0);
      }
   }

   private byte getPlayerMarkerRotation(@Nullable WorldAccess world, double rotation) {
      if (this.dimension == World.NETHER && world != null) {
         int i = (int)(world.getLevelProperties().getTimeOfDay() / 10L);
         return (byte)(i * i * 34187121 + i * 121 >> 15 & 15);
      } else {
         double d = rotation < (double)0.0F ? rotation - (double)8.0F : rotation + (double)8.0F;
         return (byte)((int)(d * (double)16.0F / (double)360.0F));
      }
   }

   private static boolean isInBounds(float dx, float dz) {
      int i = 63;
      return dx >= -63.0F && dz >= -63.0F && dx <= 63.0F && dz <= 63.0F;
   }

   @Nullable
   private RegistryEntry<MapDecorationType> getPlayerMarker(float dx, float dz) {
      int i = 320;
      boolean bl = Math.abs(dx) < 320.0F && Math.abs(dz) < 320.0F;
      if (bl) {
         return MapDecorationTypes.PLAYER_OFF_MAP;
      } else {
         return this.unlimitedTracking ? MapDecorationTypes.PLAYER_OFF_LIMITS : null;
      }
   }

   private static byte offsetToMarkerPosition(float d) {
      int i = 63;
      if (d <= -63.0F) {
         return -128;
      } else {
         return d >= 63.0F ? 127 : (byte)((int)((double)(d * 2.0F) + (double)0.5F));
      }
   }

   @Nullable
   public Packet<?> getPlayerMarkerPacket(MapIdComponent mapId, PlayerEntity player) {
      PlayerUpdateTracker playerUpdateTracker = (PlayerUpdateTracker)this.updateTrackersByPlayer.get(player);
      return playerUpdateTracker == null ? null : playerUpdateTracker.getPacket(mapId);
   }

   private void markDirty(int x, int z) {
      this.markDirty();

      for(PlayerUpdateTracker playerUpdateTracker : this.updateTrackers) {
         playerUpdateTracker.markDirty(x, z);
      }

   }

   private void markDecorationsDirty() {
      this.updateTrackers.forEach(PlayerUpdateTracker::markDecorationsDirty);
   }

   public PlayerUpdateTracker getPlayerSyncData(PlayerEntity player) {
      PlayerUpdateTracker playerUpdateTracker = (PlayerUpdateTracker)this.updateTrackersByPlayer.get(player);
      if (playerUpdateTracker == null) {
         playerUpdateTracker = new PlayerUpdateTracker(player);
         this.updateTrackersByPlayer.put(player, playerUpdateTracker);
         this.updateTrackers.add(playerUpdateTracker);
      }

      return playerUpdateTracker;
   }

   public boolean addBanner(WorldAccess world, BlockPos pos) {
      double d = (double)pos.getX() + (double)0.5F;
      double e = (double)pos.getZ() + (double)0.5F;
      int i = 1 << this.scale;
      double f = (d - (double)this.centerX) / (double)i;
      double g = (e - (double)this.centerZ) / (double)i;
      int j = 63;
      if (f >= (double)-63.0F && g >= (double)-63.0F && f <= (double)63.0F && g <= (double)63.0F) {
         MapBannerMarker mapBannerMarker = MapBannerMarker.fromWorldBlock(world, pos);
         if (mapBannerMarker == null) {
            return false;
         }

         if (this.banners.remove(mapBannerMarker.getKey(), mapBannerMarker)) {
            this.removeDecoration(mapBannerMarker.getKey());
            this.markDirty();
            return true;
         }

         if (!this.decorationCountNotLessThan(256)) {
            this.banners.put(mapBannerMarker.getKey(), mapBannerMarker);
            this.addDecoration(mapBannerMarker.getDecorationType(), world, mapBannerMarker.getKey(), d, e, (double)180.0F, (Text)mapBannerMarker.name().orElse((Object)null));
            this.markDirty();
            return true;
         }
      }

      return false;
   }

   public void removeBanner(BlockView world, int x, int z) {
      Iterator<MapBannerMarker> iterator = this.banners.values().iterator();

      while(iterator.hasNext()) {
         MapBannerMarker mapBannerMarker = (MapBannerMarker)iterator.next();
         if (mapBannerMarker.pos().getX() == x && mapBannerMarker.pos().getZ() == z) {
            MapBannerMarker mapBannerMarker2 = MapBannerMarker.fromWorldBlock(world, mapBannerMarker.pos());
            if (!mapBannerMarker.equals(mapBannerMarker2)) {
               iterator.remove();
               this.removeDecoration(mapBannerMarker.getKey());
               this.markDirty();
            }
         }
      }

   }

   public Collection<MapBannerMarker> getBanners() {
      return this.banners.values();
   }

   public void removeFrame(BlockPos pos, int id) {
      this.removeDecoration(getFrameDecorationKey(id));
      this.frames.remove(MapFrameMarker.getKey(pos));
      this.markDirty();
   }

   public boolean putColor(int x, int z, byte color) {
      byte b = this.colors[x + z * 128];
      if (b != color) {
         this.setColor(x, z, color);
         return true;
      } else {
         return false;
      }
   }

   public void setColor(int x, int z, byte color) {
      this.colors[x + z * 128] = color;
      this.markDirty(x, z);
   }

   public boolean hasExplorationMapDecoration() {
      for(MapDecoration mapDecoration : this.decorations.values()) {
         if (((MapDecorationType)mapDecoration.type().value()).explorationMapElement()) {
            return true;
         }
      }

      return false;
   }

   public void replaceDecorations(List<MapDecoration> decorations) {
      this.decorations.clear();
      this.decorationCount = 0;

      for(int i = 0; i < decorations.size(); ++i) {
         MapDecoration mapDecoration = (MapDecoration)decorations.get(i);
         this.decorations.put("icon-" + i, mapDecoration);
         if (((MapDecorationType)mapDecoration.type().value()).trackCount()) {
            ++this.decorationCount;
         }
      }

   }

   public Iterable<MapDecoration> getDecorations() {
      return this.decorations.values();
   }

   public boolean decorationCountNotLessThan(int decorationCount) {
      return this.decorationCount >= decorationCount;
   }

   private static String getFrameDecorationKey(int id) {
      return "frame-" + id;
   }

   public static record UpdateData(int startX, int startZ, int width, int height, byte[] colors) {
      public static final PacketCodec<ByteBuf, Optional<UpdateData>> CODEC = PacketCodec.<ByteBuf, Optional<UpdateData>>ofStatic(UpdateData::encode, UpdateData::decode);

      private static void encode(ByteBuf buf, Optional<UpdateData> updateData) {
         if (updateData.isPresent()) {
            UpdateData updateData2 = (UpdateData)updateData.get();
            buf.writeByte(updateData2.width);
            buf.writeByte(updateData2.height);
            buf.writeByte(updateData2.startX);
            buf.writeByte(updateData2.startZ);
            PacketByteBuf.writeByteArray(buf, updateData2.colors);
         } else {
            buf.writeByte(0);
         }

      }

      private static Optional<UpdateData> decode(ByteBuf buf) {
         int i = buf.readUnsignedByte();
         if (i > 0) {
            int j = buf.readUnsignedByte();
            int k = buf.readUnsignedByte();
            int l = buf.readUnsignedByte();
            byte[] bs = PacketByteBuf.readByteArray(buf);
            return Optional.of(new UpdateData(k, l, i, j, bs));
         } else {
            return Optional.empty();
         }
      }

      public void setColorsTo(MapState mapState) {
         for(int i = 0; i < this.width; ++i) {
            for(int j = 0; j < this.height; ++j) {
               mapState.setColor(this.startX + i, this.startZ + j, this.colors[i + j * this.width]);
            }
         }

      }
   }

   public class PlayerUpdateTracker {
      public final PlayerEntity player;
      private boolean dirty = true;
      private int startX;
      private int startZ;
      private int endX = 127;
      private int endZ = 127;
      private boolean decorationsDirty = true;
      private int emptyPacketsRequested;
      public int field_131;

      PlayerUpdateTracker(final PlayerEntity player) {
         this.player = player;
      }

      private UpdateData getMapUpdateData() {
         int i = this.startX;
         int j = this.startZ;
         int k = this.endX + 1 - this.startX;
         int l = this.endZ + 1 - this.startZ;
         byte[] bs = new byte[k * l];

         for(int m = 0; m < k; ++m) {
            for(int n = 0; n < l; ++n) {
               bs[m + n * k] = MapState.this.colors[i + m + (j + n) * 128];
            }
         }

         return new UpdateData(i, j, k, l, bs);
      }

      @Nullable
      Packet<?> getPacket(MapIdComponent mapId) {
         UpdateData updateData;
         if (this.dirty) {
            this.dirty = false;
            updateData = this.getMapUpdateData();
         } else {
            updateData = null;
         }

         Collection<MapDecoration> collection;
         if (this.decorationsDirty && this.emptyPacketsRequested++ % 5 == 0) {
            this.decorationsDirty = false;
            collection = MapState.this.decorations.values();
         } else {
            collection = null;
         }

         return collection == null && updateData == null ? null : new MapUpdateS2CPacket(mapId, MapState.this.scale, MapState.this.locked, collection, updateData);
      }

      void markDirty(int startX, int startZ) {
         if (this.dirty) {
            this.startX = Math.min(this.startX, startX);
            this.startZ = Math.min(this.startZ, startZ);
            this.endX = Math.max(this.endX, startX);
            this.endZ = Math.max(this.endZ, startZ);
         } else {
            this.dirty = true;
            this.startX = startX;
            this.startZ = startZ;
            this.endX = startX;
            this.endZ = startZ;
         }

      }

      private void markDecorationsDirty() {
         this.decorationsDirty = true;
      }
   }

   static record Marker(RegistryEntry<MapDecorationType> type, byte x, byte y, byte rot) {
   }
}
