package net.minecraft.item;

import com.mojang.datafixers.util.Pair;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import net.minecraft.block.Blocks;
import net.minecraft.block.LightBlock;
import net.minecraft.block.SuspiciousStewIngredient;
import net.minecraft.block.TestBlock;
import net.minecraft.block.enums.TestBlockMode;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.component.type.FireworksComponent;
import net.minecraft.component.type.OminousBottleAmplifierComponent;
import net.minecraft.component.type.PotionContentsComponent;
import net.minecraft.enchantment.Enchantment;
import net.minecraft.enchantment.EnchantmentHelper;
import net.minecraft.enchantment.EnchantmentLevelEntry;
import net.minecraft.entity.decoration.painting.PaintingVariant;
import net.minecraft.nbt.NbtElement;
import net.minecraft.nbt.NbtOps;
import net.minecraft.potion.Potion;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.registry.RegistryOps;
import net.minecraft.registry.RegistryWrapper;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.registry.tag.InstrumentTags;
import net.minecraft.registry.tag.PaintingVariantTags;
import net.minecraft.registry.tag.TagKey;
import net.minecraft.resource.featuretoggle.FeatureSet;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;
import net.minecraft.village.raid.Raid;
import org.jetbrains.annotations.Nullable;

public class ItemGroups {
   private static final Identifier INVENTORY_TAB_TEXTURE_ID = ItemGroup.getTabTextureId("inventory");
   private static final Identifier ITEM_SEARCH_TAB_TEXTURE_ID = ItemGroup.getTabTextureId("item_search");
   public static final RegistryKey<ItemGroup> BUILDING_BLOCKS = register("building_blocks");
   public static final RegistryKey<ItemGroup> COLORED_BLOCKS = register("colored_blocks");
   public static final RegistryKey<ItemGroup> NATURAL = register("natural_blocks");
   public static final RegistryKey<ItemGroup> FUNCTIONAL = register("functional_blocks");
   public static final RegistryKey<ItemGroup> REDSTONE = register("redstone_blocks");
   public static final RegistryKey<ItemGroup> HOTBAR = register("hotbar");
   public static final RegistryKey<ItemGroup> SEARCH = register("search");
   public static final RegistryKey<ItemGroup> TOOLS = register("tools_and_utilities");
   public static final RegistryKey<ItemGroup> COMBAT = register("combat");
   public static final RegistryKey<ItemGroup> FOOD_AND_DRINK = register("food_and_drinks");
   public static final RegistryKey<ItemGroup> INGREDIENTS = register("ingredients");
   public static final RegistryKey<ItemGroup> SPAWN_EGGS = register("spawn_eggs");
   public static final RegistryKey<ItemGroup> OPERATOR = register("op_blocks");
   public static final RegistryKey<ItemGroup> INVENTORY = register("inventory");
   private static final Comparator<RegistryEntry<PaintingVariant>> PAINTING_VARIANT_COMPARATOR = Comparator.comparing(RegistryEntry::value, Comparator.comparingInt(PaintingVariant::getArea).thenComparing(PaintingVariant::width));
   @Nullable
   private static ItemGroup.DisplayContext displayContext;

   private static RegistryKey<ItemGroup> register(String id) {
      return RegistryKey.of(RegistryKeys.ITEM_GROUP, Identifier.ofVanilla(id));
   }

   public static ItemGroup registerAndGetDefault(Registry<ItemGroup> registry) {
      Registry.register(registry, (RegistryKey)BUILDING_BLOCKS, ItemGroup.create(ItemGroup.Row.TOP, 0).displayName(Text.translatable("itemGroup.buildingBlocks")).icon(() -> new ItemStack(Blocks.BRICKS)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.OAK_LOG);
         entries.add((ItemConvertible)Items.OAK_WOOD);
         entries.add((ItemConvertible)Items.STRIPPED_OAK_LOG);
         entries.add((ItemConvertible)Items.STRIPPED_OAK_WOOD);
         entries.add((ItemConvertible)Items.OAK_PLANKS);
         entries.add((ItemConvertible)Items.OAK_STAIRS);
         entries.add((ItemConvertible)Items.OAK_SLAB);
         entries.add((ItemConvertible)Items.OAK_FENCE);
         entries.add((ItemConvertible)Items.OAK_FENCE_GATE);
         entries.add((ItemConvertible)Items.OAK_DOOR);
         entries.add((ItemConvertible)Items.OAK_TRAPDOOR);
         entries.add((ItemConvertible)Items.OAK_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.OAK_BUTTON);
         entries.add((ItemConvertible)Items.SPRUCE_LOG);
         entries.add((ItemConvertible)Items.SPRUCE_WOOD);
         entries.add((ItemConvertible)Items.STRIPPED_SPRUCE_LOG);
         entries.add((ItemConvertible)Items.STRIPPED_SPRUCE_WOOD);
         entries.add((ItemConvertible)Items.SPRUCE_PLANKS);
         entries.add((ItemConvertible)Items.SPRUCE_STAIRS);
         entries.add((ItemConvertible)Items.SPRUCE_SLAB);
         entries.add((ItemConvertible)Items.SPRUCE_FENCE);
         entries.add((ItemConvertible)Items.SPRUCE_FENCE_GATE);
         entries.add((ItemConvertible)Items.SPRUCE_DOOR);
         entries.add((ItemConvertible)Items.SPRUCE_TRAPDOOR);
         entries.add((ItemConvertible)Items.SPRUCE_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.SPRUCE_BUTTON);
         entries.add((ItemConvertible)Items.BIRCH_LOG);
         entries.add((ItemConvertible)Items.BIRCH_WOOD);
         entries.add((ItemConvertible)Items.STRIPPED_BIRCH_LOG);
         entries.add((ItemConvertible)Items.STRIPPED_BIRCH_WOOD);
         entries.add((ItemConvertible)Items.BIRCH_PLANKS);
         entries.add((ItemConvertible)Items.BIRCH_STAIRS);
         entries.add((ItemConvertible)Items.BIRCH_SLAB);
         entries.add((ItemConvertible)Items.BIRCH_FENCE);
         entries.add((ItemConvertible)Items.BIRCH_FENCE_GATE);
         entries.add((ItemConvertible)Items.BIRCH_DOOR);
         entries.add((ItemConvertible)Items.BIRCH_TRAPDOOR);
         entries.add((ItemConvertible)Items.BIRCH_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.BIRCH_BUTTON);
         entries.add((ItemConvertible)Items.JUNGLE_LOG);
         entries.add((ItemConvertible)Items.JUNGLE_WOOD);
         entries.add((ItemConvertible)Items.STRIPPED_JUNGLE_LOG);
         entries.add((ItemConvertible)Items.STRIPPED_JUNGLE_WOOD);
         entries.add((ItemConvertible)Items.JUNGLE_PLANKS);
         entries.add((ItemConvertible)Items.JUNGLE_STAIRS);
         entries.add((ItemConvertible)Items.JUNGLE_SLAB);
         entries.add((ItemConvertible)Items.JUNGLE_FENCE);
         entries.add((ItemConvertible)Items.JUNGLE_FENCE_GATE);
         entries.add((ItemConvertible)Items.JUNGLE_DOOR);
         entries.add((ItemConvertible)Items.JUNGLE_TRAPDOOR);
         entries.add((ItemConvertible)Items.JUNGLE_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.JUNGLE_BUTTON);
         entries.add((ItemConvertible)Items.ACACIA_LOG);
         entries.add((ItemConvertible)Items.ACACIA_WOOD);
         entries.add((ItemConvertible)Items.STRIPPED_ACACIA_LOG);
         entries.add((ItemConvertible)Items.STRIPPED_ACACIA_WOOD);
         entries.add((ItemConvertible)Items.ACACIA_PLANKS);
         entries.add((ItemConvertible)Items.ACACIA_STAIRS);
         entries.add((ItemConvertible)Items.ACACIA_SLAB);
         entries.add((ItemConvertible)Items.ACACIA_FENCE);
         entries.add((ItemConvertible)Items.ACACIA_FENCE_GATE);
         entries.add((ItemConvertible)Items.ACACIA_DOOR);
         entries.add((ItemConvertible)Items.ACACIA_TRAPDOOR);
         entries.add((ItemConvertible)Items.ACACIA_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.ACACIA_BUTTON);
         entries.add((ItemConvertible)Items.DARK_OAK_LOG);
         entries.add((ItemConvertible)Items.DARK_OAK_WOOD);
         entries.add((ItemConvertible)Items.STRIPPED_DARK_OAK_LOG);
         entries.add((ItemConvertible)Items.STRIPPED_DARK_OAK_WOOD);
         entries.add((ItemConvertible)Items.DARK_OAK_PLANKS);
         entries.add((ItemConvertible)Items.DARK_OAK_STAIRS);
         entries.add((ItemConvertible)Items.DARK_OAK_SLAB);
         entries.add((ItemConvertible)Items.DARK_OAK_FENCE);
         entries.add((ItemConvertible)Items.DARK_OAK_FENCE_GATE);
         entries.add((ItemConvertible)Items.DARK_OAK_DOOR);
         entries.add((ItemConvertible)Items.DARK_OAK_TRAPDOOR);
         entries.add((ItemConvertible)Items.DARK_OAK_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.DARK_OAK_BUTTON);
         entries.add((ItemConvertible)Items.MANGROVE_LOG);
         entries.add((ItemConvertible)Items.MANGROVE_WOOD);
         entries.add((ItemConvertible)Items.STRIPPED_MANGROVE_LOG);
         entries.add((ItemConvertible)Items.STRIPPED_MANGROVE_WOOD);
         entries.add((ItemConvertible)Items.MANGROVE_PLANKS);
         entries.add((ItemConvertible)Items.MANGROVE_STAIRS);
         entries.add((ItemConvertible)Items.MANGROVE_SLAB);
         entries.add((ItemConvertible)Items.MANGROVE_FENCE);
         entries.add((ItemConvertible)Items.MANGROVE_FENCE_GATE);
         entries.add((ItemConvertible)Items.MANGROVE_DOOR);
         entries.add((ItemConvertible)Items.MANGROVE_TRAPDOOR);
         entries.add((ItemConvertible)Items.MANGROVE_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.MANGROVE_BUTTON);
         entries.add((ItemConvertible)Items.CHERRY_LOG);
         entries.add((ItemConvertible)Items.CHERRY_WOOD);
         entries.add((ItemConvertible)Items.STRIPPED_CHERRY_LOG);
         entries.add((ItemConvertible)Items.STRIPPED_CHERRY_WOOD);
         entries.add((ItemConvertible)Items.CHERRY_PLANKS);
         entries.add((ItemConvertible)Items.CHERRY_STAIRS);
         entries.add((ItemConvertible)Items.CHERRY_SLAB);
         entries.add((ItemConvertible)Items.CHERRY_FENCE);
         entries.add((ItemConvertible)Items.CHERRY_FENCE_GATE);
         entries.add((ItemConvertible)Items.CHERRY_DOOR);
         entries.add((ItemConvertible)Items.CHERRY_TRAPDOOR);
         entries.add((ItemConvertible)Items.CHERRY_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.CHERRY_BUTTON);
         entries.add((ItemConvertible)Items.PALE_OAK_LOG);
         entries.add((ItemConvertible)Items.PALE_OAK_WOOD);
         entries.add((ItemConvertible)Items.STRIPPED_PALE_OAK_LOG);
         entries.add((ItemConvertible)Items.STRIPPED_PALE_OAK_WOOD);
         entries.add((ItemConvertible)Items.PALE_OAK_PLANKS);
         entries.add((ItemConvertible)Items.PALE_OAK_STAIRS);
         entries.add((ItemConvertible)Items.PALE_OAK_SLAB);
         entries.add((ItemConvertible)Items.PALE_OAK_FENCE);
         entries.add((ItemConvertible)Items.PALE_OAK_FENCE_GATE);
         entries.add((ItemConvertible)Items.PALE_OAK_DOOR);
         entries.add((ItemConvertible)Items.PALE_OAK_TRAPDOOR);
         entries.add((ItemConvertible)Items.PALE_OAK_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.PALE_OAK_BUTTON);
         entries.add((ItemConvertible)Items.BAMBOO_BLOCK);
         entries.add((ItemConvertible)Items.STRIPPED_BAMBOO_BLOCK);
         entries.add((ItemConvertible)Items.BAMBOO_PLANKS);
         entries.add((ItemConvertible)Items.BAMBOO_MOSAIC);
         entries.add((ItemConvertible)Items.BAMBOO_STAIRS);
         entries.add((ItemConvertible)Items.BAMBOO_MOSAIC_STAIRS);
         entries.add((ItemConvertible)Items.BAMBOO_SLAB);
         entries.add((ItemConvertible)Items.BAMBOO_MOSAIC_SLAB);
         entries.add((ItemConvertible)Items.BAMBOO_FENCE);
         entries.add((ItemConvertible)Items.BAMBOO_FENCE_GATE);
         entries.add((ItemConvertible)Items.BAMBOO_DOOR);
         entries.add((ItemConvertible)Items.BAMBOO_TRAPDOOR);
         entries.add((ItemConvertible)Items.BAMBOO_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.BAMBOO_BUTTON);
         entries.add((ItemConvertible)Items.CRIMSON_STEM);
         entries.add((ItemConvertible)Items.CRIMSON_HYPHAE);
         entries.add((ItemConvertible)Items.STRIPPED_CRIMSON_STEM);
         entries.add((ItemConvertible)Items.STRIPPED_CRIMSON_HYPHAE);
         entries.add((ItemConvertible)Items.CRIMSON_PLANKS);
         entries.add((ItemConvertible)Items.CRIMSON_STAIRS);
         entries.add((ItemConvertible)Items.CRIMSON_SLAB);
         entries.add((ItemConvertible)Items.CRIMSON_FENCE);
         entries.add((ItemConvertible)Items.CRIMSON_FENCE_GATE);
         entries.add((ItemConvertible)Items.CRIMSON_DOOR);
         entries.add((ItemConvertible)Items.CRIMSON_TRAPDOOR);
         entries.add((ItemConvertible)Items.CRIMSON_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.CRIMSON_BUTTON);
         entries.add((ItemConvertible)Items.WARPED_STEM);
         entries.add((ItemConvertible)Items.WARPED_HYPHAE);
         entries.add((ItemConvertible)Items.STRIPPED_WARPED_STEM);
         entries.add((ItemConvertible)Items.STRIPPED_WARPED_HYPHAE);
         entries.add((ItemConvertible)Items.WARPED_PLANKS);
         entries.add((ItemConvertible)Items.WARPED_STAIRS);
         entries.add((ItemConvertible)Items.WARPED_SLAB);
         entries.add((ItemConvertible)Items.WARPED_FENCE);
         entries.add((ItemConvertible)Items.WARPED_FENCE_GATE);
         entries.add((ItemConvertible)Items.WARPED_DOOR);
         entries.add((ItemConvertible)Items.WARPED_TRAPDOOR);
         entries.add((ItemConvertible)Items.WARPED_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.WARPED_BUTTON);
         entries.add((ItemConvertible)Items.STONE);
         entries.add((ItemConvertible)Items.STONE_STAIRS);
         entries.add((ItemConvertible)Items.STONE_SLAB);
         entries.add((ItemConvertible)Items.STONE_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.STONE_BUTTON);
         entries.add((ItemConvertible)Items.COBBLESTONE);
         entries.add((ItemConvertible)Items.COBBLESTONE_STAIRS);
         entries.add((ItemConvertible)Items.COBBLESTONE_SLAB);
         entries.add((ItemConvertible)Items.COBBLESTONE_WALL);
         entries.add((ItemConvertible)Items.MOSSY_COBBLESTONE);
         entries.add((ItemConvertible)Items.MOSSY_COBBLESTONE_STAIRS);
         entries.add((ItemConvertible)Items.MOSSY_COBBLESTONE_SLAB);
         entries.add((ItemConvertible)Items.MOSSY_COBBLESTONE_WALL);
         entries.add((ItemConvertible)Items.SMOOTH_STONE);
         entries.add((ItemConvertible)Items.SMOOTH_STONE_SLAB);
         entries.add((ItemConvertible)Items.STONE_BRICKS);
         entries.add((ItemConvertible)Items.CRACKED_STONE_BRICKS);
         entries.add((ItemConvertible)Items.STONE_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.STONE_BRICK_SLAB);
         entries.add((ItemConvertible)Items.STONE_BRICK_WALL);
         entries.add((ItemConvertible)Items.CHISELED_STONE_BRICKS);
         entries.add((ItemConvertible)Items.MOSSY_STONE_BRICKS);
         entries.add((ItemConvertible)Items.MOSSY_STONE_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.MOSSY_STONE_BRICK_SLAB);
         entries.add((ItemConvertible)Items.MOSSY_STONE_BRICK_WALL);
         entries.add((ItemConvertible)Items.GRANITE);
         entries.add((ItemConvertible)Items.GRANITE_STAIRS);
         entries.add((ItemConvertible)Items.GRANITE_SLAB);
         entries.add((ItemConvertible)Items.GRANITE_WALL);
         entries.add((ItemConvertible)Items.POLISHED_GRANITE);
         entries.add((ItemConvertible)Items.POLISHED_GRANITE_STAIRS);
         entries.add((ItemConvertible)Items.POLISHED_GRANITE_SLAB);
         entries.add((ItemConvertible)Items.DIORITE);
         entries.add((ItemConvertible)Items.DIORITE_STAIRS);
         entries.add((ItemConvertible)Items.DIORITE_SLAB);
         entries.add((ItemConvertible)Items.DIORITE_WALL);
         entries.add((ItemConvertible)Items.POLISHED_DIORITE);
         entries.add((ItemConvertible)Items.POLISHED_DIORITE_STAIRS);
         entries.add((ItemConvertible)Items.POLISHED_DIORITE_SLAB);
         entries.add((ItemConvertible)Items.ANDESITE);
         entries.add((ItemConvertible)Items.ANDESITE_STAIRS);
         entries.add((ItemConvertible)Items.ANDESITE_SLAB);
         entries.add((ItemConvertible)Items.ANDESITE_WALL);
         entries.add((ItemConvertible)Items.POLISHED_ANDESITE);
         entries.add((ItemConvertible)Items.POLISHED_ANDESITE_STAIRS);
         entries.add((ItemConvertible)Items.POLISHED_ANDESITE_SLAB);
         entries.add((ItemConvertible)Items.DEEPSLATE);
         entries.add((ItemConvertible)Items.COBBLED_DEEPSLATE);
         entries.add((ItemConvertible)Items.COBBLED_DEEPSLATE_STAIRS);
         entries.add((ItemConvertible)Items.COBBLED_DEEPSLATE_SLAB);
         entries.add((ItemConvertible)Items.COBBLED_DEEPSLATE_WALL);
         entries.add((ItemConvertible)Items.CHISELED_DEEPSLATE);
         entries.add((ItemConvertible)Items.POLISHED_DEEPSLATE);
         entries.add((ItemConvertible)Items.POLISHED_DEEPSLATE_STAIRS);
         entries.add((ItemConvertible)Items.POLISHED_DEEPSLATE_SLAB);
         entries.add((ItemConvertible)Items.POLISHED_DEEPSLATE_WALL);
         entries.add((ItemConvertible)Items.DEEPSLATE_BRICKS);
         entries.add((ItemConvertible)Items.CRACKED_DEEPSLATE_BRICKS);
         entries.add((ItemConvertible)Items.DEEPSLATE_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.DEEPSLATE_BRICK_SLAB);
         entries.add((ItemConvertible)Items.DEEPSLATE_BRICK_WALL);
         entries.add((ItemConvertible)Items.DEEPSLATE_TILES);
         entries.add((ItemConvertible)Items.CRACKED_DEEPSLATE_TILES);
         entries.add((ItemConvertible)Items.DEEPSLATE_TILE_STAIRS);
         entries.add((ItemConvertible)Items.DEEPSLATE_TILE_SLAB);
         entries.add((ItemConvertible)Items.DEEPSLATE_TILE_WALL);
         entries.add((ItemConvertible)Items.REINFORCED_DEEPSLATE);
         entries.add((ItemConvertible)Items.TUFF);
         entries.add((ItemConvertible)Items.TUFF_STAIRS);
         entries.add((ItemConvertible)Items.TUFF_SLAB);
         entries.add((ItemConvertible)Items.TUFF_WALL);
         entries.add((ItemConvertible)Items.CHISELED_TUFF);
         entries.add((ItemConvertible)Items.POLISHED_TUFF);
         entries.add((ItemConvertible)Items.POLISHED_TUFF_STAIRS);
         entries.add((ItemConvertible)Items.POLISHED_TUFF_SLAB);
         entries.add((ItemConvertible)Items.POLISHED_TUFF_WALL);
         entries.add((ItemConvertible)Items.TUFF_BRICKS);
         entries.add((ItemConvertible)Items.TUFF_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.TUFF_BRICK_SLAB);
         entries.add((ItemConvertible)Items.TUFF_BRICK_WALL);
         entries.add((ItemConvertible)Items.CHISELED_TUFF_BRICKS);
         entries.add((ItemConvertible)Items.BRICKS);
         entries.add((ItemConvertible)Items.BRICK_STAIRS);
         entries.add((ItemConvertible)Items.BRICK_SLAB);
         entries.add((ItemConvertible)Items.BRICK_WALL);
         entries.add((ItemConvertible)Items.PACKED_MUD);
         entries.add((ItemConvertible)Items.MUD_BRICKS);
         entries.add((ItemConvertible)Items.MUD_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.MUD_BRICK_SLAB);
         entries.add((ItemConvertible)Items.MUD_BRICK_WALL);
         entries.add((ItemConvertible)Items.RESIN_BRICKS);
         entries.add((ItemConvertible)Items.RESIN_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.RESIN_BRICK_SLAB);
         entries.add((ItemConvertible)Items.RESIN_BRICK_WALL);
         entries.add((ItemConvertible)Items.CHISELED_RESIN_BRICKS);
         entries.add((ItemConvertible)Items.SANDSTONE);
         entries.add((ItemConvertible)Items.SANDSTONE_STAIRS);
         entries.add((ItemConvertible)Items.SANDSTONE_SLAB);
         entries.add((ItemConvertible)Items.SANDSTONE_WALL);
         entries.add((ItemConvertible)Items.CHISELED_SANDSTONE);
         entries.add((ItemConvertible)Items.SMOOTH_SANDSTONE);
         entries.add((ItemConvertible)Items.SMOOTH_SANDSTONE_STAIRS);
         entries.add((ItemConvertible)Items.SMOOTH_SANDSTONE_SLAB);
         entries.add((ItemConvertible)Items.CUT_SANDSTONE);
         entries.add((ItemConvertible)Items.CUT_SANDSTONE_SLAB);
         entries.add((ItemConvertible)Items.RED_SANDSTONE);
         entries.add((ItemConvertible)Items.RED_SANDSTONE_STAIRS);
         entries.add((ItemConvertible)Items.RED_SANDSTONE_SLAB);
         entries.add((ItemConvertible)Items.RED_SANDSTONE_WALL);
         entries.add((ItemConvertible)Items.CHISELED_RED_SANDSTONE);
         entries.add((ItemConvertible)Items.SMOOTH_RED_SANDSTONE);
         entries.add((ItemConvertible)Items.SMOOTH_RED_SANDSTONE_STAIRS);
         entries.add((ItemConvertible)Items.SMOOTH_RED_SANDSTONE_SLAB);
         entries.add((ItemConvertible)Items.CUT_RED_SANDSTONE);
         entries.add((ItemConvertible)Items.CUT_RED_SANDSTONE_SLAB);
         entries.add((ItemConvertible)Items.SEA_LANTERN);
         entries.add((ItemConvertible)Items.PRISMARINE);
         entries.add((ItemConvertible)Items.PRISMARINE_STAIRS);
         entries.add((ItemConvertible)Items.PRISMARINE_SLAB);
         entries.add((ItemConvertible)Items.PRISMARINE_WALL);
         entries.add((ItemConvertible)Items.PRISMARINE_BRICKS);
         entries.add((ItemConvertible)Items.PRISMARINE_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.PRISMARINE_BRICK_SLAB);
         entries.add((ItemConvertible)Items.DARK_PRISMARINE);
         entries.add((ItemConvertible)Items.DARK_PRISMARINE_STAIRS);
         entries.add((ItemConvertible)Items.DARK_PRISMARINE_SLAB);
         entries.add((ItemConvertible)Items.NETHERRACK);
         entries.add((ItemConvertible)Items.NETHER_BRICKS);
         entries.add((ItemConvertible)Items.CRACKED_NETHER_BRICKS);
         entries.add((ItemConvertible)Items.NETHER_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.NETHER_BRICK_SLAB);
         entries.add((ItemConvertible)Items.NETHER_BRICK_WALL);
         entries.add((ItemConvertible)Items.NETHER_BRICK_FENCE);
         entries.add((ItemConvertible)Items.CHISELED_NETHER_BRICKS);
         entries.add((ItemConvertible)Items.RED_NETHER_BRICKS);
         entries.add((ItemConvertible)Items.RED_NETHER_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.RED_NETHER_BRICK_SLAB);
         entries.add((ItemConvertible)Items.RED_NETHER_BRICK_WALL);
         entries.add((ItemConvertible)Items.BASALT);
         entries.add((ItemConvertible)Items.SMOOTH_BASALT);
         entries.add((ItemConvertible)Items.POLISHED_BASALT);
         entries.add((ItemConvertible)Items.BLACKSTONE);
         entries.add((ItemConvertible)Items.GILDED_BLACKSTONE);
         entries.add((ItemConvertible)Items.BLACKSTONE_STAIRS);
         entries.add((ItemConvertible)Items.BLACKSTONE_SLAB);
         entries.add((ItemConvertible)Items.BLACKSTONE_WALL);
         entries.add((ItemConvertible)Items.CHISELED_POLISHED_BLACKSTONE);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE_STAIRS);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE_SLAB);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE_WALL);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE_BUTTON);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE_BRICKS);
         entries.add((ItemConvertible)Items.CRACKED_POLISHED_BLACKSTONE_BRICKS);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE_BRICK_SLAB);
         entries.add((ItemConvertible)Items.POLISHED_BLACKSTONE_BRICK_WALL);
         entries.add((ItemConvertible)Items.END_STONE);
         entries.add((ItemConvertible)Items.END_STONE_BRICKS);
         entries.add((ItemConvertible)Items.END_STONE_BRICK_STAIRS);
         entries.add((ItemConvertible)Items.END_STONE_BRICK_SLAB);
         entries.add((ItemConvertible)Items.END_STONE_BRICK_WALL);
         entries.add((ItemConvertible)Items.PURPUR_BLOCK);
         entries.add((ItemConvertible)Items.PURPUR_PILLAR);
         entries.add((ItemConvertible)Items.PURPUR_STAIRS);
         entries.add((ItemConvertible)Items.PURPUR_SLAB);
         entries.add((ItemConvertible)Items.COAL_BLOCK);
         entries.add((ItemConvertible)Items.IRON_BLOCK);
         entries.add((ItemConvertible)Items.IRON_BARS);
         entries.add((ItemConvertible)Items.IRON_DOOR);
         entries.add((ItemConvertible)Items.IRON_TRAPDOOR);
         entries.add((ItemConvertible)Items.HEAVY_WEIGHTED_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.CHAIN);
         entries.add((ItemConvertible)Items.GOLD_BLOCK);
         entries.add((ItemConvertible)Items.LIGHT_WEIGHTED_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.REDSTONE_BLOCK);
         entries.add((ItemConvertible)Items.EMERALD_BLOCK);
         entries.add((ItemConvertible)Items.LAPIS_BLOCK);
         entries.add((ItemConvertible)Items.DIAMOND_BLOCK);
         entries.add((ItemConvertible)Items.NETHERITE_BLOCK);
         entries.add((ItemConvertible)Items.QUARTZ_BLOCK);
         entries.add((ItemConvertible)Items.QUARTZ_STAIRS);
         entries.add((ItemConvertible)Items.QUARTZ_SLAB);
         entries.add((ItemConvertible)Items.CHISELED_QUARTZ_BLOCK);
         entries.add((ItemConvertible)Items.QUARTZ_BRICKS);
         entries.add((ItemConvertible)Items.QUARTZ_PILLAR);
         entries.add((ItemConvertible)Items.SMOOTH_QUARTZ);
         entries.add((ItemConvertible)Items.SMOOTH_QUARTZ_STAIRS);
         entries.add((ItemConvertible)Items.SMOOTH_QUARTZ_SLAB);
         entries.add((ItemConvertible)Items.AMETHYST_BLOCK);
         entries.add((ItemConvertible)Items.COPPER_BLOCK);
         entries.add((ItemConvertible)Items.CHISELED_COPPER);
         entries.add((ItemConvertible)Items.COPPER_GRATE);
         entries.add((ItemConvertible)Items.CUT_COPPER);
         entries.add((ItemConvertible)Items.CUT_COPPER_STAIRS);
         entries.add((ItemConvertible)Items.CUT_COPPER_SLAB);
         entries.add((ItemConvertible)Items.COPPER_DOOR);
         entries.add((ItemConvertible)Items.COPPER_TRAPDOOR);
         entries.add((ItemConvertible)Items.COPPER_BULB);
         entries.add((ItemConvertible)Items.EXPOSED_COPPER);
         entries.add((ItemConvertible)Items.EXPOSED_CHISELED_COPPER);
         entries.add((ItemConvertible)Items.EXPOSED_COPPER_GRATE);
         entries.add((ItemConvertible)Items.EXPOSED_CUT_COPPER);
         entries.add((ItemConvertible)Items.EXPOSED_CUT_COPPER_STAIRS);
         entries.add((ItemConvertible)Items.EXPOSED_CUT_COPPER_SLAB);
         entries.add((ItemConvertible)Items.EXPOSED_COPPER_DOOR);
         entries.add((ItemConvertible)Items.EXPOSED_COPPER_TRAPDOOR);
         entries.add((ItemConvertible)Items.EXPOSED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WEATHERED_COPPER);
         entries.add((ItemConvertible)Items.WEATHERED_CHISELED_COPPER);
         entries.add((ItemConvertible)Items.WEATHERED_COPPER_GRATE);
         entries.add((ItemConvertible)Items.WEATHERED_CUT_COPPER);
         entries.add((ItemConvertible)Items.WEATHERED_CUT_COPPER_STAIRS);
         entries.add((ItemConvertible)Items.WEATHERED_CUT_COPPER_SLAB);
         entries.add((ItemConvertible)Items.WEATHERED_COPPER_DOOR);
         entries.add((ItemConvertible)Items.WEATHERED_COPPER_TRAPDOOR);
         entries.add((ItemConvertible)Items.WEATHERED_COPPER_BULB);
         entries.add((ItemConvertible)Items.OXIDIZED_COPPER);
         entries.add((ItemConvertible)Items.OXIDIZED_CHISELED_COPPER);
         entries.add((ItemConvertible)Items.OXIDIZED_COPPER_GRATE);
         entries.add((ItemConvertible)Items.OXIDIZED_CUT_COPPER);
         entries.add((ItemConvertible)Items.OXIDIZED_CUT_COPPER_STAIRS);
         entries.add((ItemConvertible)Items.OXIDIZED_CUT_COPPER_SLAB);
         entries.add((ItemConvertible)Items.OXIDIZED_COPPER_DOOR);
         entries.add((ItemConvertible)Items.OXIDIZED_COPPER_TRAPDOOR);
         entries.add((ItemConvertible)Items.OXIDIZED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_COPPER_BLOCK);
         entries.add((ItemConvertible)Items.WAXED_CHISELED_COPPER);
         entries.add((ItemConvertible)Items.WAXED_COPPER_GRATE);
         entries.add((ItemConvertible)Items.WAXED_CUT_COPPER);
         entries.add((ItemConvertible)Items.WAXED_CUT_COPPER_STAIRS);
         entries.add((ItemConvertible)Items.WAXED_CUT_COPPER_SLAB);
         entries.add((ItemConvertible)Items.WAXED_COPPER_DOOR);
         entries.add((ItemConvertible)Items.WAXED_COPPER_TRAPDOOR);
         entries.add((ItemConvertible)Items.WAXED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_COPPER);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_CHISELED_COPPER);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_COPPER_GRATE);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_CUT_COPPER);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_CUT_COPPER_STAIRS);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_CUT_COPPER_SLAB);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_COPPER_DOOR);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_COPPER_TRAPDOOR);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_COPPER);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_CHISELED_COPPER);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_COPPER_GRATE);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_CUT_COPPER);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_CUT_COPPER_STAIRS);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_CUT_COPPER_SLAB);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_COPPER_DOOR);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_COPPER_TRAPDOOR);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_COPPER);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_CHISELED_COPPER);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_COPPER_GRATE);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_CUT_COPPER);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_CUT_COPPER_STAIRS);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_CUT_COPPER_SLAB);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_COPPER_DOOR);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_COPPER_TRAPDOOR);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_COPPER_BULB);
      }).build());
      Registry.register(registry, (RegistryKey)COLORED_BLOCKS, ItemGroup.create(ItemGroup.Row.TOP, 1).displayName(Text.translatable("itemGroup.coloredBlocks")).icon(() -> new ItemStack(Blocks.CYAN_WOOL)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.WHITE_WOOL);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_WOOL);
         entries.add((ItemConvertible)Items.GRAY_WOOL);
         entries.add((ItemConvertible)Items.BLACK_WOOL);
         entries.add((ItemConvertible)Items.BROWN_WOOL);
         entries.add((ItemConvertible)Items.RED_WOOL);
         entries.add((ItemConvertible)Items.ORANGE_WOOL);
         entries.add((ItemConvertible)Items.YELLOW_WOOL);
         entries.add((ItemConvertible)Items.LIME_WOOL);
         entries.add((ItemConvertible)Items.GREEN_WOOL);
         entries.add((ItemConvertible)Items.CYAN_WOOL);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_WOOL);
         entries.add((ItemConvertible)Items.BLUE_WOOL);
         entries.add((ItemConvertible)Items.PURPLE_WOOL);
         entries.add((ItemConvertible)Items.MAGENTA_WOOL);
         entries.add((ItemConvertible)Items.PINK_WOOL);
         entries.add((ItemConvertible)Items.WHITE_CARPET);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_CARPET);
         entries.add((ItemConvertible)Items.GRAY_CARPET);
         entries.add((ItemConvertible)Items.BLACK_CARPET);
         entries.add((ItemConvertible)Items.BROWN_CARPET);
         entries.add((ItemConvertible)Items.RED_CARPET);
         entries.add((ItemConvertible)Items.ORANGE_CARPET);
         entries.add((ItemConvertible)Items.YELLOW_CARPET);
         entries.add((ItemConvertible)Items.LIME_CARPET);
         entries.add((ItemConvertible)Items.GREEN_CARPET);
         entries.add((ItemConvertible)Items.CYAN_CARPET);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_CARPET);
         entries.add((ItemConvertible)Items.BLUE_CARPET);
         entries.add((ItemConvertible)Items.PURPLE_CARPET);
         entries.add((ItemConvertible)Items.MAGENTA_CARPET);
         entries.add((ItemConvertible)Items.PINK_CARPET);
         entries.add((ItemConvertible)Items.TERRACOTTA);
         entries.add((ItemConvertible)Items.WHITE_TERRACOTTA);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_TERRACOTTA);
         entries.add((ItemConvertible)Items.GRAY_TERRACOTTA);
         entries.add((ItemConvertible)Items.BLACK_TERRACOTTA);
         entries.add((ItemConvertible)Items.BROWN_TERRACOTTA);
         entries.add((ItemConvertible)Items.RED_TERRACOTTA);
         entries.add((ItemConvertible)Items.ORANGE_TERRACOTTA);
         entries.add((ItemConvertible)Items.YELLOW_TERRACOTTA);
         entries.add((ItemConvertible)Items.LIME_TERRACOTTA);
         entries.add((ItemConvertible)Items.GREEN_TERRACOTTA);
         entries.add((ItemConvertible)Items.CYAN_TERRACOTTA);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_TERRACOTTA);
         entries.add((ItemConvertible)Items.BLUE_TERRACOTTA);
         entries.add((ItemConvertible)Items.PURPLE_TERRACOTTA);
         entries.add((ItemConvertible)Items.MAGENTA_TERRACOTTA);
         entries.add((ItemConvertible)Items.PINK_TERRACOTTA);
         entries.add((ItemConvertible)Items.WHITE_CONCRETE);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_CONCRETE);
         entries.add((ItemConvertible)Items.GRAY_CONCRETE);
         entries.add((ItemConvertible)Items.BLACK_CONCRETE);
         entries.add((ItemConvertible)Items.BROWN_CONCRETE);
         entries.add((ItemConvertible)Items.RED_CONCRETE);
         entries.add((ItemConvertible)Items.ORANGE_CONCRETE);
         entries.add((ItemConvertible)Items.YELLOW_CONCRETE);
         entries.add((ItemConvertible)Items.LIME_CONCRETE);
         entries.add((ItemConvertible)Items.GREEN_CONCRETE);
         entries.add((ItemConvertible)Items.CYAN_CONCRETE);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_CONCRETE);
         entries.add((ItemConvertible)Items.BLUE_CONCRETE);
         entries.add((ItemConvertible)Items.PURPLE_CONCRETE);
         entries.add((ItemConvertible)Items.MAGENTA_CONCRETE);
         entries.add((ItemConvertible)Items.PINK_CONCRETE);
         entries.add((ItemConvertible)Items.WHITE_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.GRAY_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.BLACK_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.BROWN_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.RED_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.ORANGE_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.YELLOW_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.LIME_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.GREEN_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.CYAN_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.BLUE_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.PURPLE_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.MAGENTA_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.PINK_CONCRETE_POWDER);
         entries.add((ItemConvertible)Items.WHITE_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.GRAY_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.BLACK_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.BROWN_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.RED_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.ORANGE_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.YELLOW_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.LIME_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.GREEN_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.CYAN_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.BLUE_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.PURPLE_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.MAGENTA_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.PINK_GLAZED_TERRACOTTA);
         entries.add((ItemConvertible)Items.GLASS);
         entries.add((ItemConvertible)Items.TINTED_GLASS);
         entries.add((ItemConvertible)Items.WHITE_STAINED_GLASS);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_STAINED_GLASS);
         entries.add((ItemConvertible)Items.GRAY_STAINED_GLASS);
         entries.add((ItemConvertible)Items.BLACK_STAINED_GLASS);
         entries.add((ItemConvertible)Items.BROWN_STAINED_GLASS);
         entries.add((ItemConvertible)Items.RED_STAINED_GLASS);
         entries.add((ItemConvertible)Items.ORANGE_STAINED_GLASS);
         entries.add((ItemConvertible)Items.YELLOW_STAINED_GLASS);
         entries.add((ItemConvertible)Items.LIME_STAINED_GLASS);
         entries.add((ItemConvertible)Items.GREEN_STAINED_GLASS);
         entries.add((ItemConvertible)Items.CYAN_STAINED_GLASS);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_STAINED_GLASS);
         entries.add((ItemConvertible)Items.BLUE_STAINED_GLASS);
         entries.add((ItemConvertible)Items.PURPLE_STAINED_GLASS);
         entries.add((ItemConvertible)Items.MAGENTA_STAINED_GLASS);
         entries.add((ItemConvertible)Items.PINK_STAINED_GLASS);
         entries.add((ItemConvertible)Items.GLASS_PANE);
         entries.add((ItemConvertible)Items.WHITE_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.GRAY_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.BLACK_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.BROWN_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.RED_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.ORANGE_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.YELLOW_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.LIME_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.GREEN_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.CYAN_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.BLUE_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.PURPLE_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.MAGENTA_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.PINK_STAINED_GLASS_PANE);
         entries.add((ItemConvertible)Items.SHULKER_BOX);
         entries.add((ItemConvertible)Items.WHITE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_SHULKER_BOX);
         entries.add((ItemConvertible)Items.GRAY_SHULKER_BOX);
         entries.add((ItemConvertible)Items.BLACK_SHULKER_BOX);
         entries.add((ItemConvertible)Items.BROWN_SHULKER_BOX);
         entries.add((ItemConvertible)Items.RED_SHULKER_BOX);
         entries.add((ItemConvertible)Items.ORANGE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.YELLOW_SHULKER_BOX);
         entries.add((ItemConvertible)Items.LIME_SHULKER_BOX);
         entries.add((ItemConvertible)Items.GREEN_SHULKER_BOX);
         entries.add((ItemConvertible)Items.CYAN_SHULKER_BOX);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.BLUE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.PURPLE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.MAGENTA_SHULKER_BOX);
         entries.add((ItemConvertible)Items.PINK_SHULKER_BOX);
         entries.add((ItemConvertible)Items.WHITE_BED);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_BED);
         entries.add((ItemConvertible)Items.GRAY_BED);
         entries.add((ItemConvertible)Items.BLACK_BED);
         entries.add((ItemConvertible)Items.BROWN_BED);
         entries.add((ItemConvertible)Items.RED_BED);
         entries.add((ItemConvertible)Items.ORANGE_BED);
         entries.add((ItemConvertible)Items.YELLOW_BED);
         entries.add((ItemConvertible)Items.LIME_BED);
         entries.add((ItemConvertible)Items.GREEN_BED);
         entries.add((ItemConvertible)Items.CYAN_BED);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_BED);
         entries.add((ItemConvertible)Items.BLUE_BED);
         entries.add((ItemConvertible)Items.PURPLE_BED);
         entries.add((ItemConvertible)Items.MAGENTA_BED);
         entries.add((ItemConvertible)Items.PINK_BED);
         entries.add((ItemConvertible)Items.CANDLE);
         entries.add((ItemConvertible)Items.WHITE_CANDLE);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_CANDLE);
         entries.add((ItemConvertible)Items.GRAY_CANDLE);
         entries.add((ItemConvertible)Items.BLACK_CANDLE);
         entries.add((ItemConvertible)Items.BROWN_CANDLE);
         entries.add((ItemConvertible)Items.RED_CANDLE);
         entries.add((ItemConvertible)Items.ORANGE_CANDLE);
         entries.add((ItemConvertible)Items.YELLOW_CANDLE);
         entries.add((ItemConvertible)Items.LIME_CANDLE);
         entries.add((ItemConvertible)Items.GREEN_CANDLE);
         entries.add((ItemConvertible)Items.CYAN_CANDLE);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_CANDLE);
         entries.add((ItemConvertible)Items.BLUE_CANDLE);
         entries.add((ItemConvertible)Items.PURPLE_CANDLE);
         entries.add((ItemConvertible)Items.MAGENTA_CANDLE);
         entries.add((ItemConvertible)Items.PINK_CANDLE);
         entries.add((ItemConvertible)Items.WHITE_BANNER);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_BANNER);
         entries.add((ItemConvertible)Items.GRAY_BANNER);
         entries.add((ItemConvertible)Items.BLACK_BANNER);
         entries.add((ItemConvertible)Items.BROWN_BANNER);
         entries.add((ItemConvertible)Items.RED_BANNER);
         entries.add((ItemConvertible)Items.ORANGE_BANNER);
         entries.add((ItemConvertible)Items.YELLOW_BANNER);
         entries.add((ItemConvertible)Items.LIME_BANNER);
         entries.add((ItemConvertible)Items.GREEN_BANNER);
         entries.add((ItemConvertible)Items.CYAN_BANNER);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_BANNER);
         entries.add((ItemConvertible)Items.BLUE_BANNER);
         entries.add((ItemConvertible)Items.PURPLE_BANNER);
         entries.add((ItemConvertible)Items.MAGENTA_BANNER);
         entries.add((ItemConvertible)Items.PINK_BANNER);
      }).build());
      Registry.register(registry, (RegistryKey)NATURAL, ItemGroup.create(ItemGroup.Row.TOP, 2).displayName(Text.translatable("itemGroup.natural")).icon(() -> new ItemStack(Blocks.GRASS_BLOCK)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.GRASS_BLOCK);
         entries.add((ItemConvertible)Items.PODZOL);
         entries.add((ItemConvertible)Items.MYCELIUM);
         entries.add((ItemConvertible)Items.DIRT_PATH);
         entries.add((ItemConvertible)Items.DIRT);
         entries.add((ItemConvertible)Items.COARSE_DIRT);
         entries.add((ItemConvertible)Items.ROOTED_DIRT);
         entries.add((ItemConvertible)Items.FARMLAND);
         entries.add((ItemConvertible)Items.MUD);
         entries.add((ItemConvertible)Items.CLAY);
         entries.add((ItemConvertible)Items.GRAVEL);
         entries.add((ItemConvertible)Items.SAND);
         entries.add((ItemConvertible)Items.SANDSTONE);
         entries.add((ItemConvertible)Items.RED_SAND);
         entries.add((ItemConvertible)Items.RED_SANDSTONE);
         entries.add((ItemConvertible)Items.ICE);
         entries.add((ItemConvertible)Items.PACKED_ICE);
         entries.add((ItemConvertible)Items.BLUE_ICE);
         entries.add((ItemConvertible)Items.SNOW_BLOCK);
         entries.add((ItemConvertible)Items.SNOW);
         entries.add((ItemConvertible)Items.MOSS_BLOCK);
         entries.add((ItemConvertible)Items.MOSS_CARPET);
         entries.add((ItemConvertible)Items.PALE_MOSS_BLOCK);
         entries.add((ItemConvertible)Items.PALE_MOSS_CARPET);
         entries.add((ItemConvertible)Items.PALE_HANGING_MOSS);
         entries.add((ItemConvertible)Items.STONE);
         entries.add((ItemConvertible)Items.DEEPSLATE);
         entries.add((ItemConvertible)Items.GRANITE);
         entries.add((ItemConvertible)Items.DIORITE);
         entries.add((ItemConvertible)Items.ANDESITE);
         entries.add((ItemConvertible)Items.CALCITE);
         entries.add((ItemConvertible)Items.TUFF);
         entries.add((ItemConvertible)Items.DRIPSTONE_BLOCK);
         entries.add((ItemConvertible)Items.POINTED_DRIPSTONE);
         entries.add((ItemConvertible)Items.PRISMARINE);
         entries.add((ItemConvertible)Items.MAGMA_BLOCK);
         entries.add((ItemConvertible)Items.OBSIDIAN);
         entries.add((ItemConvertible)Items.CRYING_OBSIDIAN);
         entries.add((ItemConvertible)Items.NETHERRACK);
         entries.add((ItemConvertible)Items.CRIMSON_NYLIUM);
         entries.add((ItemConvertible)Items.WARPED_NYLIUM);
         entries.add((ItemConvertible)Items.SOUL_SAND);
         entries.add((ItemConvertible)Items.SOUL_SOIL);
         entries.add((ItemConvertible)Items.BONE_BLOCK);
         entries.add((ItemConvertible)Items.BLACKSTONE);
         entries.add((ItemConvertible)Items.BASALT);
         entries.add((ItemConvertible)Items.SMOOTH_BASALT);
         entries.add((ItemConvertible)Items.END_STONE);
         entries.add((ItemConvertible)Items.COAL_ORE);
         entries.add((ItemConvertible)Items.DEEPSLATE_COAL_ORE);
         entries.add((ItemConvertible)Items.IRON_ORE);
         entries.add((ItemConvertible)Items.DEEPSLATE_IRON_ORE);
         entries.add((ItemConvertible)Items.COPPER_ORE);
         entries.add((ItemConvertible)Items.DEEPSLATE_COPPER_ORE);
         entries.add((ItemConvertible)Items.GOLD_ORE);
         entries.add((ItemConvertible)Items.DEEPSLATE_GOLD_ORE);
         entries.add((ItemConvertible)Items.REDSTONE_ORE);
         entries.add((ItemConvertible)Items.DEEPSLATE_REDSTONE_ORE);
         entries.add((ItemConvertible)Items.EMERALD_ORE);
         entries.add((ItemConvertible)Items.DEEPSLATE_EMERALD_ORE);
         entries.add((ItemConvertible)Items.LAPIS_ORE);
         entries.add((ItemConvertible)Items.DEEPSLATE_LAPIS_ORE);
         entries.add((ItemConvertible)Items.DIAMOND_ORE);
         entries.add((ItemConvertible)Items.DEEPSLATE_DIAMOND_ORE);
         entries.add((ItemConvertible)Items.NETHER_GOLD_ORE);
         entries.add((ItemConvertible)Items.NETHER_QUARTZ_ORE);
         entries.add((ItemConvertible)Items.ANCIENT_DEBRIS);
         entries.add((ItemConvertible)Items.RAW_IRON_BLOCK);
         entries.add((ItemConvertible)Items.RAW_COPPER_BLOCK);
         entries.add((ItemConvertible)Items.RAW_GOLD_BLOCK);
         entries.add((ItemConvertible)Items.GLOWSTONE);
         entries.add((ItemConvertible)Items.AMETHYST_BLOCK);
         entries.add((ItemConvertible)Items.BUDDING_AMETHYST);
         entries.add((ItemConvertible)Items.SMALL_AMETHYST_BUD);
         entries.add((ItemConvertible)Items.MEDIUM_AMETHYST_BUD);
         entries.add((ItemConvertible)Items.LARGE_AMETHYST_BUD);
         entries.add((ItemConvertible)Items.AMETHYST_CLUSTER);
         entries.add((ItemConvertible)Items.OAK_LOG);
         entries.add((ItemConvertible)Items.SPRUCE_LOG);
         entries.add((ItemConvertible)Items.BIRCH_LOG);
         entries.add((ItemConvertible)Items.JUNGLE_LOG);
         entries.add((ItemConvertible)Items.ACACIA_LOG);
         entries.add((ItemConvertible)Items.DARK_OAK_LOG);
         entries.add((ItemConvertible)Items.MANGROVE_LOG);
         entries.add((ItemConvertible)Items.MANGROVE_ROOTS);
         entries.add((ItemConvertible)Items.MUDDY_MANGROVE_ROOTS);
         entries.add((ItemConvertible)Items.CHERRY_LOG);
         entries.add((ItemConvertible)Items.PALE_OAK_LOG);
         entries.add((ItemConvertible)Items.MUSHROOM_STEM);
         entries.add((ItemConvertible)Items.CRIMSON_STEM);
         entries.add((ItemConvertible)Items.WARPED_STEM);
         entries.add((ItemConvertible)Items.OAK_LEAVES);
         entries.add((ItemConvertible)Items.SPRUCE_LEAVES);
         entries.add((ItemConvertible)Items.BIRCH_LEAVES);
         entries.add((ItemConvertible)Items.JUNGLE_LEAVES);
         entries.add((ItemConvertible)Items.ACACIA_LEAVES);
         entries.add((ItemConvertible)Items.DARK_OAK_LEAVES);
         entries.add((ItemConvertible)Items.MANGROVE_LEAVES);
         entries.add((ItemConvertible)Items.CHERRY_LEAVES);
         entries.add((ItemConvertible)Items.PALE_OAK_LEAVES);
         entries.add((ItemConvertible)Items.AZALEA_LEAVES);
         entries.add((ItemConvertible)Items.FLOWERING_AZALEA_LEAVES);
         entries.add((ItemConvertible)Items.BROWN_MUSHROOM_BLOCK);
         entries.add((ItemConvertible)Items.RED_MUSHROOM_BLOCK);
         entries.add((ItemConvertible)Items.NETHER_WART_BLOCK);
         entries.add((ItemConvertible)Items.WARPED_WART_BLOCK);
         entries.add((ItemConvertible)Items.SHROOMLIGHT);
         entries.add((ItemConvertible)Items.OAK_SAPLING);
         entries.add((ItemConvertible)Items.SPRUCE_SAPLING);
         entries.add((ItemConvertible)Items.BIRCH_SAPLING);
         entries.add((ItemConvertible)Items.JUNGLE_SAPLING);
         entries.add((ItemConvertible)Items.ACACIA_SAPLING);
         entries.add((ItemConvertible)Items.DARK_OAK_SAPLING);
         entries.add((ItemConvertible)Items.MANGROVE_PROPAGULE);
         entries.add((ItemConvertible)Items.CHERRY_SAPLING);
         entries.add((ItemConvertible)Items.PALE_OAK_SAPLING);
         entries.add((ItemConvertible)Items.AZALEA);
         entries.add((ItemConvertible)Items.FLOWERING_AZALEA);
         entries.add((ItemConvertible)Items.BROWN_MUSHROOM);
         entries.add((ItemConvertible)Items.RED_MUSHROOM);
         entries.add((ItemConvertible)Items.CRIMSON_FUNGUS);
         entries.add((ItemConvertible)Items.WARPED_FUNGUS);
         entries.add((ItemConvertible)Items.SHORT_GRASS);
         entries.add((ItemConvertible)Items.FERN);
         entries.add((ItemConvertible)Items.SHORT_DRY_GRASS);
         entries.add((ItemConvertible)Items.BUSH);
         entries.add((ItemConvertible)Items.DEAD_BUSH);
         entries.add((ItemConvertible)Items.DANDELION);
         entries.add((ItemConvertible)Items.POPPY);
         entries.add((ItemConvertible)Items.BLUE_ORCHID);
         entries.add((ItemConvertible)Items.ALLIUM);
         entries.add((ItemConvertible)Items.AZURE_BLUET);
         entries.add((ItemConvertible)Items.RED_TULIP);
         entries.add((ItemConvertible)Items.ORANGE_TULIP);
         entries.add((ItemConvertible)Items.WHITE_TULIP);
         entries.add((ItemConvertible)Items.PINK_TULIP);
         entries.add((ItemConvertible)Items.OXEYE_DAISY);
         entries.add((ItemConvertible)Items.CORNFLOWER);
         entries.add((ItemConvertible)Items.LILY_OF_THE_VALLEY);
         entries.add((ItemConvertible)Items.TORCHFLOWER);
         entries.add((ItemConvertible)Items.CACTUS_FLOWER);
         entries.add((ItemConvertible)Items.CLOSED_EYEBLOSSOM);
         entries.add((ItemConvertible)Items.OPEN_EYEBLOSSOM);
         entries.add((ItemConvertible)Items.WITHER_ROSE);
         entries.add((ItemConvertible)Items.PINK_PETALS);
         entries.add((ItemConvertible)Items.WILDFLOWERS);
         entries.add((ItemConvertible)Items.LEAF_LITTER);
         entries.add((ItemConvertible)Items.SPORE_BLOSSOM);
         entries.add((ItemConvertible)Items.FIREFLY_BUSH);
         entries.add((ItemConvertible)Items.BAMBOO);
         entries.add((ItemConvertible)Items.SUGAR_CANE);
         entries.add((ItemConvertible)Items.CACTUS);
         entries.add((ItemConvertible)Items.CRIMSON_ROOTS);
         entries.add((ItemConvertible)Items.WARPED_ROOTS);
         entries.add((ItemConvertible)Items.NETHER_SPROUTS);
         entries.add((ItemConvertible)Items.WEEPING_VINES);
         entries.add((ItemConvertible)Items.TWISTING_VINES);
         entries.add((ItemConvertible)Items.VINE);
         entries.add((ItemConvertible)Items.TALL_GRASS);
         entries.add((ItemConvertible)Items.LARGE_FERN);
         entries.add((ItemConvertible)Items.TALL_DRY_GRASS);
         entries.add((ItemConvertible)Items.SUNFLOWER);
         entries.add((ItemConvertible)Items.LILAC);
         entries.add((ItemConvertible)Items.ROSE_BUSH);
         entries.add((ItemConvertible)Items.PEONY);
         entries.add((ItemConvertible)Items.PITCHER_PLANT);
         entries.add((ItemConvertible)Items.BIG_DRIPLEAF);
         entries.add((ItemConvertible)Items.SMALL_DRIPLEAF);
         entries.add((ItemConvertible)Items.CHORUS_PLANT);
         entries.add((ItemConvertible)Items.CHORUS_FLOWER);
         entries.add((ItemConvertible)Items.GLOW_LICHEN);
         entries.add((ItemConvertible)Items.HANGING_ROOTS);
         entries.add((ItemConvertible)Items.FROGSPAWN);
         entries.add((ItemConvertible)Items.TURTLE_EGG);
         entries.add((ItemConvertible)Items.SNIFFER_EGG);
         entries.add((ItemConvertible)Items.WHEAT_SEEDS);
         entries.add((ItemConvertible)Items.COCOA_BEANS);
         entries.add((ItemConvertible)Items.PUMPKIN_SEEDS);
         entries.add((ItemConvertible)Items.MELON_SEEDS);
         entries.add((ItemConvertible)Items.BEETROOT_SEEDS);
         entries.add((ItemConvertible)Items.TORCHFLOWER_SEEDS);
         entries.add((ItemConvertible)Items.PITCHER_POD);
         entries.add((ItemConvertible)Items.GLOW_BERRIES);
         entries.add((ItemConvertible)Items.SWEET_BERRIES);
         entries.add((ItemConvertible)Items.NETHER_WART);
         entries.add((ItemConvertible)Items.LILY_PAD);
         entries.add((ItemConvertible)Items.SEAGRASS);
         entries.add((ItemConvertible)Items.SEA_PICKLE);
         entries.add((ItemConvertible)Items.KELP);
         entries.add((ItemConvertible)Items.DRIED_KELP_BLOCK);
         entries.add((ItemConvertible)Items.TUBE_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.BRAIN_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.BUBBLE_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.FIRE_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.HORN_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.DEAD_TUBE_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.DEAD_BRAIN_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.DEAD_BUBBLE_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.DEAD_FIRE_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.DEAD_HORN_CORAL_BLOCK);
         entries.add((ItemConvertible)Items.TUBE_CORAL);
         entries.add((ItemConvertible)Items.BRAIN_CORAL);
         entries.add((ItemConvertible)Items.BUBBLE_CORAL);
         entries.add((ItemConvertible)Items.FIRE_CORAL);
         entries.add((ItemConvertible)Items.HORN_CORAL);
         entries.add((ItemConvertible)Items.DEAD_TUBE_CORAL);
         entries.add((ItemConvertible)Items.DEAD_BRAIN_CORAL);
         entries.add((ItemConvertible)Items.DEAD_BUBBLE_CORAL);
         entries.add((ItemConvertible)Items.DEAD_FIRE_CORAL);
         entries.add((ItemConvertible)Items.DEAD_HORN_CORAL);
         entries.add((ItemConvertible)Items.TUBE_CORAL_FAN);
         entries.add((ItemConvertible)Items.BRAIN_CORAL_FAN);
         entries.add((ItemConvertible)Items.BUBBLE_CORAL_FAN);
         entries.add((ItemConvertible)Items.FIRE_CORAL_FAN);
         entries.add((ItemConvertible)Items.HORN_CORAL_FAN);
         entries.add((ItemConvertible)Items.DEAD_TUBE_CORAL_FAN);
         entries.add((ItemConvertible)Items.DEAD_BRAIN_CORAL_FAN);
         entries.add((ItemConvertible)Items.DEAD_BUBBLE_CORAL_FAN);
         entries.add((ItemConvertible)Items.DEAD_FIRE_CORAL_FAN);
         entries.add((ItemConvertible)Items.DEAD_HORN_CORAL_FAN);
         entries.add((ItemConvertible)Items.SPONGE);
         entries.add((ItemConvertible)Items.WET_SPONGE);
         entries.add((ItemConvertible)Items.MELON);
         entries.add((ItemConvertible)Items.PUMPKIN);
         entries.add((ItemConvertible)Items.CARVED_PUMPKIN);
         entries.add((ItemConvertible)Items.JACK_O_LANTERN);
         entries.add((ItemConvertible)Items.HAY_BLOCK);
         entries.add((ItemConvertible)Items.BEE_NEST);
         entries.add((ItemConvertible)Items.HONEYCOMB_BLOCK);
         entries.add((ItemConvertible)Items.SLIME_BLOCK);
         entries.add((ItemConvertible)Items.HONEY_BLOCK);
         entries.add((ItemConvertible)Items.RESIN_BLOCK);
         entries.add((ItemConvertible)Items.OCHRE_FROGLIGHT);
         entries.add((ItemConvertible)Items.VERDANT_FROGLIGHT);
         entries.add((ItemConvertible)Items.PEARLESCENT_FROGLIGHT);
         entries.add((ItemConvertible)Items.SCULK);
         entries.add((ItemConvertible)Items.SCULK_VEIN);
         entries.add((ItemConvertible)Items.SCULK_CATALYST);
         entries.add((ItemConvertible)Items.SCULK_SHRIEKER);
         entries.add((ItemConvertible)Items.SCULK_SENSOR);
         entries.add((ItemConvertible)Items.COBWEB);
         entries.add((ItemConvertible)Items.BEDROCK);
      }).build());
      Registry.register(registry, (RegistryKey)FUNCTIONAL, ItemGroup.create(ItemGroup.Row.TOP, 3).displayName(Text.translatable("itemGroup.functional")).icon(() -> new ItemStack(Items.OAK_SIGN)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.TORCH);
         entries.add((ItemConvertible)Items.SOUL_TORCH);
         entries.add((ItemConvertible)Items.REDSTONE_TORCH);
         entries.add((ItemConvertible)Items.LANTERN);
         entries.add((ItemConvertible)Items.SOUL_LANTERN);
         entries.add((ItemConvertible)Items.CHAIN);
         entries.add((ItemConvertible)Items.END_ROD);
         entries.add((ItemConvertible)Items.SEA_LANTERN);
         entries.add((ItemConvertible)Items.REDSTONE_LAMP);
         entries.add((ItemConvertible)Items.COPPER_BULB);
         entries.add((ItemConvertible)Items.EXPOSED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WEATHERED_COPPER_BULB);
         entries.add((ItemConvertible)Items.OXIDIZED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_COPPER_BULB);
         entries.add((ItemConvertible)Items.GLOWSTONE);
         entries.add((ItemConvertible)Items.SHROOMLIGHT);
         entries.add((ItemConvertible)Items.OCHRE_FROGLIGHT);
         entries.add((ItemConvertible)Items.VERDANT_FROGLIGHT);
         entries.add((ItemConvertible)Items.PEARLESCENT_FROGLIGHT);
         entries.add((ItemConvertible)Items.CRYING_OBSIDIAN);
         entries.add((ItemConvertible)Items.GLOW_LICHEN);
         entries.add((ItemConvertible)Items.MAGMA_BLOCK);
         entries.add((ItemConvertible)Items.CRAFTING_TABLE);
         entries.add((ItemConvertible)Items.STONECUTTER);
         entries.add((ItemConvertible)Items.CARTOGRAPHY_TABLE);
         entries.add((ItemConvertible)Items.FLETCHING_TABLE);
         entries.add((ItemConvertible)Items.SMITHING_TABLE);
         entries.add((ItemConvertible)Items.GRINDSTONE);
         entries.add((ItemConvertible)Items.LOOM);
         entries.add((ItemConvertible)Items.FURNACE);
         entries.add((ItemConvertible)Items.SMOKER);
         entries.add((ItemConvertible)Items.BLAST_FURNACE);
         entries.add((ItemConvertible)Items.CAMPFIRE);
         entries.add((ItemConvertible)Items.SOUL_CAMPFIRE);
         entries.add((ItemConvertible)Items.ANVIL);
         entries.add((ItemConvertible)Items.CHIPPED_ANVIL);
         entries.add((ItemConvertible)Items.DAMAGED_ANVIL);
         entries.add((ItemConvertible)Items.COMPOSTER);
         entries.add((ItemConvertible)Items.NOTE_BLOCK);
         entries.add((ItemConvertible)Items.JUKEBOX);
         entries.add((ItemConvertible)Items.ENCHANTING_TABLE);
         entries.add((ItemConvertible)Items.END_CRYSTAL);
         entries.add((ItemConvertible)Items.BREWING_STAND);
         entries.add((ItemConvertible)Items.CAULDRON);
         entries.add((ItemConvertible)Items.BELL);
         entries.add((ItemConvertible)Items.BEACON);
         entries.add((ItemConvertible)Items.CONDUIT);
         entries.add((ItemConvertible)Items.LODESTONE);
         entries.add((ItemConvertible)Items.LADDER);
         entries.add((ItemConvertible)Items.SCAFFOLDING);
         entries.add((ItemConvertible)Items.BEE_NEST);
         entries.add((ItemConvertible)Items.BEEHIVE);
         entries.add((ItemConvertible)Items.SUSPICIOUS_SAND);
         entries.add((ItemConvertible)Items.SUSPICIOUS_GRAVEL);
         entries.add((ItemConvertible)Items.LIGHTNING_ROD);
         entries.add((ItemConvertible)Items.FLOWER_POT);
         entries.add((ItemConvertible)Items.DECORATED_POT);
         entries.add((ItemConvertible)Items.ARMOR_STAND);
         entries.add((ItemConvertible)Items.ITEM_FRAME);
         entries.add((ItemConvertible)Items.GLOW_ITEM_FRAME);
         entries.add((ItemConvertible)Items.PAINTING);
         displayContext.lookup().getOptional(RegistryKeys.PAINTING_VARIANT).ifPresent((registryWrapper) -> addPaintings(entries, displayContext.lookup(), registryWrapper, (registryEntry) -> registryEntry.isIn(PaintingVariantTags.PLACEABLE), ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS));
         entries.add((ItemConvertible)Items.BOOKSHELF);
         entries.add((ItemConvertible)Items.CHISELED_BOOKSHELF);
         entries.add((ItemConvertible)Items.LECTERN);
         entries.add((ItemConvertible)Items.TINTED_GLASS);
         entries.add((ItemConvertible)Items.OAK_SIGN);
         entries.add((ItemConvertible)Items.OAK_HANGING_SIGN);
         entries.add((ItemConvertible)Items.SPRUCE_SIGN);
         entries.add((ItemConvertible)Items.SPRUCE_HANGING_SIGN);
         entries.add((ItemConvertible)Items.BIRCH_SIGN);
         entries.add((ItemConvertible)Items.BIRCH_HANGING_SIGN);
         entries.add((ItemConvertible)Items.JUNGLE_SIGN);
         entries.add((ItemConvertible)Items.JUNGLE_HANGING_SIGN);
         entries.add((ItemConvertible)Items.ACACIA_SIGN);
         entries.add((ItemConvertible)Items.ACACIA_HANGING_SIGN);
         entries.add((ItemConvertible)Items.DARK_OAK_SIGN);
         entries.add((ItemConvertible)Items.DARK_OAK_HANGING_SIGN);
         entries.add((ItemConvertible)Items.MANGROVE_SIGN);
         entries.add((ItemConvertible)Items.MANGROVE_HANGING_SIGN);
         entries.add((ItemConvertible)Items.CHERRY_SIGN);
         entries.add((ItemConvertible)Items.CHERRY_HANGING_SIGN);
         entries.add((ItemConvertible)Items.PALE_OAK_SIGN);
         entries.add((ItemConvertible)Items.PALE_OAK_HANGING_SIGN);
         entries.add((ItemConvertible)Items.BAMBOO_SIGN);
         entries.add((ItemConvertible)Items.BAMBOO_HANGING_SIGN);
         entries.add((ItemConvertible)Items.CRIMSON_SIGN);
         entries.add((ItemConvertible)Items.CRIMSON_HANGING_SIGN);
         entries.add((ItemConvertible)Items.WARPED_SIGN);
         entries.add((ItemConvertible)Items.WARPED_HANGING_SIGN);
         entries.add((ItemConvertible)Items.CHEST);
         entries.add((ItemConvertible)Items.BARREL);
         entries.add((ItemConvertible)Items.ENDER_CHEST);
         entries.add((ItemConvertible)Items.SHULKER_BOX);
         entries.add((ItemConvertible)Items.WHITE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_SHULKER_BOX);
         entries.add((ItemConvertible)Items.GRAY_SHULKER_BOX);
         entries.add((ItemConvertible)Items.BLACK_SHULKER_BOX);
         entries.add((ItemConvertible)Items.BROWN_SHULKER_BOX);
         entries.add((ItemConvertible)Items.RED_SHULKER_BOX);
         entries.add((ItemConvertible)Items.ORANGE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.YELLOW_SHULKER_BOX);
         entries.add((ItemConvertible)Items.LIME_SHULKER_BOX);
         entries.add((ItemConvertible)Items.GREEN_SHULKER_BOX);
         entries.add((ItemConvertible)Items.CYAN_SHULKER_BOX);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.BLUE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.PURPLE_SHULKER_BOX);
         entries.add((ItemConvertible)Items.MAGENTA_SHULKER_BOX);
         entries.add((ItemConvertible)Items.PINK_SHULKER_BOX);
         entries.add((ItemConvertible)Items.RESPAWN_ANCHOR);
         entries.add((ItemConvertible)Items.WHITE_BED);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_BED);
         entries.add((ItemConvertible)Items.GRAY_BED);
         entries.add((ItemConvertible)Items.BLACK_BED);
         entries.add((ItemConvertible)Items.BROWN_BED);
         entries.add((ItemConvertible)Items.RED_BED);
         entries.add((ItemConvertible)Items.ORANGE_BED);
         entries.add((ItemConvertible)Items.YELLOW_BED);
         entries.add((ItemConvertible)Items.LIME_BED);
         entries.add((ItemConvertible)Items.GREEN_BED);
         entries.add((ItemConvertible)Items.CYAN_BED);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_BED);
         entries.add((ItemConvertible)Items.BLUE_BED);
         entries.add((ItemConvertible)Items.PURPLE_BED);
         entries.add((ItemConvertible)Items.MAGENTA_BED);
         entries.add((ItemConvertible)Items.PINK_BED);
         entries.add((ItemConvertible)Items.CANDLE);
         entries.add((ItemConvertible)Items.WHITE_CANDLE);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_CANDLE);
         entries.add((ItemConvertible)Items.GRAY_CANDLE);
         entries.add((ItemConvertible)Items.BLACK_CANDLE);
         entries.add((ItemConvertible)Items.BROWN_CANDLE);
         entries.add((ItemConvertible)Items.RED_CANDLE);
         entries.add((ItemConvertible)Items.ORANGE_CANDLE);
         entries.add((ItemConvertible)Items.YELLOW_CANDLE);
         entries.add((ItemConvertible)Items.LIME_CANDLE);
         entries.add((ItemConvertible)Items.GREEN_CANDLE);
         entries.add((ItemConvertible)Items.CYAN_CANDLE);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_CANDLE);
         entries.add((ItemConvertible)Items.BLUE_CANDLE);
         entries.add((ItemConvertible)Items.PURPLE_CANDLE);
         entries.add((ItemConvertible)Items.MAGENTA_CANDLE);
         entries.add((ItemConvertible)Items.PINK_CANDLE);
         entries.add((ItemConvertible)Items.WHITE_BANNER);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_BANNER);
         entries.add((ItemConvertible)Items.GRAY_BANNER);
         entries.add((ItemConvertible)Items.BLACK_BANNER);
         entries.add((ItemConvertible)Items.BROWN_BANNER);
         entries.add((ItemConvertible)Items.RED_BANNER);
         entries.add((ItemConvertible)Items.ORANGE_BANNER);
         entries.add((ItemConvertible)Items.YELLOW_BANNER);
         entries.add((ItemConvertible)Items.LIME_BANNER);
         entries.add((ItemConvertible)Items.GREEN_BANNER);
         entries.add((ItemConvertible)Items.CYAN_BANNER);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_BANNER);
         entries.add((ItemConvertible)Items.BLUE_BANNER);
         entries.add((ItemConvertible)Items.PURPLE_BANNER);
         entries.add((ItemConvertible)Items.MAGENTA_BANNER);
         entries.add((ItemConvertible)Items.PINK_BANNER);
         entries.add(Raid.createOminousBanner(displayContext.lookup().getOrThrow(RegistryKeys.BANNER_PATTERN)));
         entries.add((ItemConvertible)Items.SKELETON_SKULL);
         entries.add((ItemConvertible)Items.WITHER_SKELETON_SKULL);
         entries.add((ItemConvertible)Items.PLAYER_HEAD);
         entries.add((ItemConvertible)Items.ZOMBIE_HEAD);
         entries.add((ItemConvertible)Items.CREEPER_HEAD);
         entries.add((ItemConvertible)Items.PIGLIN_HEAD);
         entries.add((ItemConvertible)Items.DRAGON_HEAD);
         entries.add((ItemConvertible)Items.DRAGON_EGG);
         entries.add((ItemConvertible)Items.END_PORTAL_FRAME);
         entries.add((ItemConvertible)Items.ENDER_EYE);
         entries.add((ItemConvertible)Items.VAULT);
         entries.add((ItemConvertible)Items.INFESTED_STONE);
         entries.add((ItemConvertible)Items.INFESTED_COBBLESTONE);
         entries.add((ItemConvertible)Items.INFESTED_STONE_BRICKS);
         entries.add((ItemConvertible)Items.INFESTED_MOSSY_STONE_BRICKS);
         entries.add((ItemConvertible)Items.INFESTED_CRACKED_STONE_BRICKS);
         entries.add((ItemConvertible)Items.INFESTED_CHISELED_STONE_BRICKS);
         entries.add((ItemConvertible)Items.INFESTED_DEEPSLATE);
      }).build());
      Registry.register(registry, (RegistryKey)REDSTONE, ItemGroup.create(ItemGroup.Row.TOP, 4).displayName(Text.translatable("itemGroup.redstone")).icon(() -> new ItemStack(Items.REDSTONE)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.REDSTONE);
         entries.add((ItemConvertible)Items.REDSTONE_TORCH);
         entries.add((ItemConvertible)Items.REDSTONE_BLOCK);
         entries.add((ItemConvertible)Items.REPEATER);
         entries.add((ItemConvertible)Items.COMPARATOR);
         entries.add((ItemConvertible)Items.TARGET);
         entries.add((ItemConvertible)Items.WAXED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_EXPOSED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_WEATHERED_COPPER_BULB);
         entries.add((ItemConvertible)Items.WAXED_OXIDIZED_COPPER_BULB);
         entries.add((ItemConvertible)Items.LEVER);
         entries.add((ItemConvertible)Items.OAK_BUTTON);
         entries.add((ItemConvertible)Items.STONE_BUTTON);
         entries.add((ItemConvertible)Items.OAK_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.STONE_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.LIGHT_WEIGHTED_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.HEAVY_WEIGHTED_PRESSURE_PLATE);
         entries.add((ItemConvertible)Items.SCULK_SENSOR);
         entries.add((ItemConvertible)Items.CALIBRATED_SCULK_SENSOR);
         entries.add((ItemConvertible)Items.SCULK_SHRIEKER);
         entries.add((ItemConvertible)Items.AMETHYST_BLOCK);
         entries.add((ItemConvertible)Items.WHITE_WOOL);
         entries.add((ItemConvertible)Items.TRIPWIRE_HOOK);
         entries.add((ItemConvertible)Items.STRING);
         entries.add((ItemConvertible)Items.LECTERN);
         entries.add((ItemConvertible)Items.DAYLIGHT_DETECTOR);
         entries.add((ItemConvertible)Items.LIGHTNING_ROD);
         entries.add((ItemConvertible)Items.PISTON);
         entries.add((ItemConvertible)Items.STICKY_PISTON);
         entries.add((ItemConvertible)Items.SLIME_BLOCK);
         entries.add((ItemConvertible)Items.HONEY_BLOCK);
         entries.add((ItemConvertible)Items.DISPENSER);
         entries.add((ItemConvertible)Items.DROPPER);
         entries.add((ItemConvertible)Items.CRAFTER);
         entries.add((ItemConvertible)Items.HOPPER);
         entries.add((ItemConvertible)Items.CHEST);
         entries.add((ItemConvertible)Items.BARREL);
         entries.add((ItemConvertible)Items.CHISELED_BOOKSHELF);
         entries.add((ItemConvertible)Items.FURNACE);
         entries.add((ItemConvertible)Items.TRAPPED_CHEST);
         entries.add((ItemConvertible)Items.JUKEBOX);
         entries.add((ItemConvertible)Items.DECORATED_POT);
         entries.add((ItemConvertible)Items.OBSERVER);
         entries.add((ItemConvertible)Items.NOTE_BLOCK);
         entries.add((ItemConvertible)Items.COMPOSTER);
         entries.add((ItemConvertible)Items.CAULDRON);
         entries.add((ItemConvertible)Items.RAIL);
         entries.add((ItemConvertible)Items.POWERED_RAIL);
         entries.add((ItemConvertible)Items.DETECTOR_RAIL);
         entries.add((ItemConvertible)Items.ACTIVATOR_RAIL);
         entries.add((ItemConvertible)Items.MINECART);
         entries.add((ItemConvertible)Items.HOPPER_MINECART);
         entries.add((ItemConvertible)Items.CHEST_MINECART);
         entries.add((ItemConvertible)Items.FURNACE_MINECART);
         entries.add((ItemConvertible)Items.TNT_MINECART);
         entries.add((ItemConvertible)Items.OAK_CHEST_BOAT);
         entries.add((ItemConvertible)Items.BAMBOO_CHEST_RAFT);
         entries.add((ItemConvertible)Items.OAK_DOOR);
         entries.add((ItemConvertible)Items.IRON_DOOR);
         entries.add((ItemConvertible)Items.OAK_FENCE_GATE);
         entries.add((ItemConvertible)Items.OAK_TRAPDOOR);
         entries.add((ItemConvertible)Items.IRON_TRAPDOOR);
         entries.add((ItemConvertible)Items.TNT);
         entries.add((ItemConvertible)Items.REDSTONE_LAMP);
         entries.add((ItemConvertible)Items.BELL);
         entries.add((ItemConvertible)Items.BIG_DRIPLEAF);
         entries.add((ItemConvertible)Items.ARMOR_STAND);
         entries.add((ItemConvertible)Items.REDSTONE_ORE);
      }).build());
      Registry.register(registry, (RegistryKey)HOTBAR, ItemGroup.create(ItemGroup.Row.TOP, 5).displayName(Text.translatable("itemGroup.hotbar")).icon(() -> new ItemStack(Blocks.BOOKSHELF)).special().type(ItemGroup.Type.HOTBAR).build());
      Registry.register(registry, (RegistryKey)SEARCH, ItemGroup.create(ItemGroup.Row.TOP, 6).displayName(Text.translatable("itemGroup.search")).icon(() -> new ItemStack(Items.COMPASS)).entries((displayContext, entries) -> {
         Set<ItemStack> set = ItemStackSet.create();

         for(ItemGroup itemGroup : registry) {
            if (itemGroup.getType() != ItemGroup.Type.SEARCH) {
               set.addAll(itemGroup.getSearchTabStacks());
            }
         }

         entries.addAll(set);
      }).texture(ITEM_SEARCH_TAB_TEXTURE_ID).special().type(ItemGroup.Type.SEARCH).build());
      Registry.register(registry, (RegistryKey)TOOLS, ItemGroup.create(ItemGroup.Row.BOTTOM, 0).displayName(Text.translatable("itemGroup.tools")).icon(() -> new ItemStack(Items.DIAMOND_PICKAXE)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.WOODEN_SHOVEL);
         entries.add((ItemConvertible)Items.WOODEN_PICKAXE);
         entries.add((ItemConvertible)Items.WOODEN_AXE);
         entries.add((ItemConvertible)Items.WOODEN_HOE);
         entries.add((ItemConvertible)Items.STONE_SHOVEL);
         entries.add((ItemConvertible)Items.STONE_PICKAXE);
         entries.add((ItemConvertible)Items.STONE_AXE);
         entries.add((ItemConvertible)Items.STONE_HOE);
         entries.add((ItemConvertible)Items.IRON_SHOVEL);
         entries.add((ItemConvertible)Items.IRON_PICKAXE);
         entries.add((ItemConvertible)Items.IRON_AXE);
         entries.add((ItemConvertible)Items.IRON_HOE);
         entries.add((ItemConvertible)Items.GOLDEN_SHOVEL);
         entries.add((ItemConvertible)Items.GOLDEN_PICKAXE);
         entries.add((ItemConvertible)Items.GOLDEN_AXE);
         entries.add((ItemConvertible)Items.GOLDEN_HOE);
         entries.add((ItemConvertible)Items.DIAMOND_SHOVEL);
         entries.add((ItemConvertible)Items.DIAMOND_PICKAXE);
         entries.add((ItemConvertible)Items.DIAMOND_AXE);
         entries.add((ItemConvertible)Items.DIAMOND_HOE);
         entries.add((ItemConvertible)Items.NETHERITE_SHOVEL);
         entries.add((ItemConvertible)Items.NETHERITE_PICKAXE);
         entries.add((ItemConvertible)Items.NETHERITE_AXE);
         entries.add((ItemConvertible)Items.NETHERITE_HOE);
         entries.add((ItemConvertible)Items.BUCKET);
         entries.add((ItemConvertible)Items.WATER_BUCKET);
         entries.add((ItemConvertible)Items.COD_BUCKET);
         entries.add((ItemConvertible)Items.SALMON_BUCKET);
         entries.add((ItemConvertible)Items.TROPICAL_FISH_BUCKET);
         entries.add((ItemConvertible)Items.PUFFERFISH_BUCKET);
         entries.add((ItemConvertible)Items.AXOLOTL_BUCKET);
         entries.add((ItemConvertible)Items.TADPOLE_BUCKET);
         entries.add((ItemConvertible)Items.LAVA_BUCKET);
         entries.add((ItemConvertible)Items.POWDER_SNOW_BUCKET);
         entries.add((ItemConvertible)Items.MILK_BUCKET);
         entries.add((ItemConvertible)Items.FISHING_ROD);
         entries.add((ItemConvertible)Items.FLINT_AND_STEEL);
         entries.add((ItemConvertible)Items.FIRE_CHARGE);
         entries.add((ItemConvertible)Items.BONE_MEAL);
         entries.add((ItemConvertible)Items.SHEARS);
         entries.add((ItemConvertible)Items.BRUSH);
         entries.add((ItemConvertible)Items.NAME_TAG);
         entries.add((ItemConvertible)Items.LEAD);
         entries.add((ItemConvertible)Items.BUNDLE);
         entries.add((ItemConvertible)Items.WHITE_BUNDLE);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_BUNDLE);
         entries.add((ItemConvertible)Items.GRAY_BUNDLE);
         entries.add((ItemConvertible)Items.BLACK_BUNDLE);
         entries.add((ItemConvertible)Items.BROWN_BUNDLE);
         entries.add((ItemConvertible)Items.RED_BUNDLE);
         entries.add((ItemConvertible)Items.ORANGE_BUNDLE);
         entries.add((ItemConvertible)Items.YELLOW_BUNDLE);
         entries.add((ItemConvertible)Items.LIME_BUNDLE);
         entries.add((ItemConvertible)Items.GREEN_BUNDLE);
         entries.add((ItemConvertible)Items.CYAN_BUNDLE);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_BUNDLE);
         entries.add((ItemConvertible)Items.BLUE_BUNDLE);
         entries.add((ItemConvertible)Items.PURPLE_BUNDLE);
         entries.add((ItemConvertible)Items.MAGENTA_BUNDLE);
         entries.add((ItemConvertible)Items.PINK_BUNDLE);
         entries.add((ItemConvertible)Items.COMPASS);
         entries.add((ItemConvertible)Items.RECOVERY_COMPASS);
         entries.add((ItemConvertible)Items.CLOCK);
         entries.add((ItemConvertible)Items.SPYGLASS);
         entries.add((ItemConvertible)Items.MAP);
         entries.add((ItemConvertible)Items.WRITABLE_BOOK);
         entries.add((ItemConvertible)Items.WIND_CHARGE);
         entries.add((ItemConvertible)Items.ENDER_PEARL);
         entries.add((ItemConvertible)Items.ENDER_EYE);
         entries.add((ItemConvertible)Items.ELYTRA);
         addFireworkRockets(entries, ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS);
         entries.add((ItemConvertible)Items.SADDLE);
         entries.add((ItemConvertible)Items.CARROT_ON_A_STICK);
         entries.add((ItemConvertible)Items.WARPED_FUNGUS_ON_A_STICK);
         entries.add((ItemConvertible)Items.OAK_BOAT);
         entries.add((ItemConvertible)Items.OAK_CHEST_BOAT);
         entries.add((ItemConvertible)Items.SPRUCE_BOAT);
         entries.add((ItemConvertible)Items.SPRUCE_CHEST_BOAT);
         entries.add((ItemConvertible)Items.BIRCH_BOAT);
         entries.add((ItemConvertible)Items.BIRCH_CHEST_BOAT);
         entries.add((ItemConvertible)Items.JUNGLE_BOAT);
         entries.add((ItemConvertible)Items.JUNGLE_CHEST_BOAT);
         entries.add((ItemConvertible)Items.ACACIA_BOAT);
         entries.add((ItemConvertible)Items.ACACIA_CHEST_BOAT);
         entries.add((ItemConvertible)Items.DARK_OAK_BOAT);
         entries.add((ItemConvertible)Items.DARK_OAK_CHEST_BOAT);
         entries.add((ItemConvertible)Items.MANGROVE_BOAT);
         entries.add((ItemConvertible)Items.MANGROVE_CHEST_BOAT);
         entries.add((ItemConvertible)Items.CHERRY_BOAT);
         entries.add((ItemConvertible)Items.CHERRY_CHEST_BOAT);
         entries.add((ItemConvertible)Items.PALE_OAK_BOAT);
         entries.add((ItemConvertible)Items.PALE_OAK_CHEST_BOAT);
         entries.add((ItemConvertible)Items.BAMBOO_RAFT);
         entries.add((ItemConvertible)Items.BAMBOO_CHEST_RAFT);
         entries.add((ItemConvertible)Items.RAIL);
         entries.add((ItemConvertible)Items.POWERED_RAIL);
         entries.add((ItemConvertible)Items.DETECTOR_RAIL);
         entries.add((ItemConvertible)Items.ACTIVATOR_RAIL);
         entries.add((ItemConvertible)Items.MINECART);
         entries.add((ItemConvertible)Items.HOPPER_MINECART);
         entries.add((ItemConvertible)Items.CHEST_MINECART);
         entries.add((ItemConvertible)Items.FURNACE_MINECART);
         entries.add((ItemConvertible)Items.TNT_MINECART);
         displayContext.lookup().getOptional(RegistryKeys.INSTRUMENT).ifPresent((wrapper) -> addInstruments(entries, wrapper, Items.GOAT_HORN, InstrumentTags.GOAT_HORNS, ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS));
         entries.add((ItemConvertible)Items.MUSIC_DISC_13);
         entries.add((ItemConvertible)Items.MUSIC_DISC_CAT);
         entries.add((ItemConvertible)Items.MUSIC_DISC_BLOCKS);
         entries.add((ItemConvertible)Items.MUSIC_DISC_CHIRP);
         entries.add((ItemConvertible)Items.MUSIC_DISC_FAR);
         entries.add((ItemConvertible)Items.MUSIC_DISC_MALL);
         entries.add((ItemConvertible)Items.MUSIC_DISC_MELLOHI);
         entries.add((ItemConvertible)Items.MUSIC_DISC_STAL);
         entries.add((ItemConvertible)Items.MUSIC_DISC_STRAD);
         entries.add((ItemConvertible)Items.MUSIC_DISC_WARD);
         entries.add((ItemConvertible)Items.MUSIC_DISC_11);
         entries.add((ItemConvertible)Items.MUSIC_DISC_CREATOR_MUSIC_BOX);
         entries.add((ItemConvertible)Items.MUSIC_DISC_WAIT);
         entries.add((ItemConvertible)Items.MUSIC_DISC_CREATOR);
         entries.add((ItemConvertible)Items.MUSIC_DISC_PRECIPICE);
         entries.add((ItemConvertible)Items.MUSIC_DISC_OTHERSIDE);
         entries.add((ItemConvertible)Items.MUSIC_DISC_RELIC);
         entries.add((ItemConvertible)Items.MUSIC_DISC_5);
         entries.add((ItemConvertible)Items.MUSIC_DISC_PIGSTEP);
      }).build());
      Registry.register(registry, (RegistryKey)COMBAT, ItemGroup.create(ItemGroup.Row.BOTTOM, 1).displayName(Text.translatable("itemGroup.combat")).icon(() -> new ItemStack(Items.NETHERITE_SWORD)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.WOODEN_SWORD);
         entries.add((ItemConvertible)Items.STONE_SWORD);
         entries.add((ItemConvertible)Items.IRON_SWORD);
         entries.add((ItemConvertible)Items.GOLDEN_SWORD);
         entries.add((ItemConvertible)Items.DIAMOND_SWORD);
         entries.add((ItemConvertible)Items.NETHERITE_SWORD);
         entries.add((ItemConvertible)Items.WOODEN_AXE);
         entries.add((ItemConvertible)Items.STONE_AXE);
         entries.add((ItemConvertible)Items.IRON_AXE);
         entries.add((ItemConvertible)Items.GOLDEN_AXE);
         entries.add((ItemConvertible)Items.DIAMOND_AXE);
         entries.add((ItemConvertible)Items.NETHERITE_AXE);
         entries.add((ItemConvertible)Items.TRIDENT);
         entries.add((ItemConvertible)Items.MACE);
         entries.add((ItemConvertible)Items.SHIELD);
         entries.add((ItemConvertible)Items.LEATHER_HELMET);
         entries.add((ItemConvertible)Items.LEATHER_CHESTPLATE);
         entries.add((ItemConvertible)Items.LEATHER_LEGGINGS);
         entries.add((ItemConvertible)Items.LEATHER_BOOTS);
         entries.add((ItemConvertible)Items.CHAINMAIL_HELMET);
         entries.add((ItemConvertible)Items.CHAINMAIL_CHESTPLATE);
         entries.add((ItemConvertible)Items.CHAINMAIL_LEGGINGS);
         entries.add((ItemConvertible)Items.CHAINMAIL_BOOTS);
         entries.add((ItemConvertible)Items.IRON_HELMET);
         entries.add((ItemConvertible)Items.IRON_CHESTPLATE);
         entries.add((ItemConvertible)Items.IRON_LEGGINGS);
         entries.add((ItemConvertible)Items.IRON_BOOTS);
         entries.add((ItemConvertible)Items.GOLDEN_HELMET);
         entries.add((ItemConvertible)Items.GOLDEN_CHESTPLATE);
         entries.add((ItemConvertible)Items.GOLDEN_LEGGINGS);
         entries.add((ItemConvertible)Items.GOLDEN_BOOTS);
         entries.add((ItemConvertible)Items.DIAMOND_HELMET);
         entries.add((ItemConvertible)Items.DIAMOND_CHESTPLATE);
         entries.add((ItemConvertible)Items.DIAMOND_LEGGINGS);
         entries.add((ItemConvertible)Items.DIAMOND_BOOTS);
         entries.add((ItemConvertible)Items.NETHERITE_HELMET);
         entries.add((ItemConvertible)Items.NETHERITE_CHESTPLATE);
         entries.add((ItemConvertible)Items.NETHERITE_LEGGINGS);
         entries.add((ItemConvertible)Items.NETHERITE_BOOTS);
         entries.add((ItemConvertible)Items.TURTLE_HELMET);
         entries.add((ItemConvertible)Items.LEATHER_HORSE_ARMOR);
         entries.add((ItemConvertible)Items.IRON_HORSE_ARMOR);
         entries.add((ItemConvertible)Items.GOLDEN_HORSE_ARMOR);
         entries.add((ItemConvertible)Items.DIAMOND_HORSE_ARMOR);
         entries.add((ItemConvertible)Items.WOLF_ARMOR);
         entries.add((ItemConvertible)Items.TOTEM_OF_UNDYING);
         entries.add((ItemConvertible)Items.TNT);
         entries.add((ItemConvertible)Items.END_CRYSTAL);
         entries.add((ItemConvertible)Items.SNOWBALL);
         entries.add((ItemConvertible)Items.EGG);
         entries.add((ItemConvertible)Items.BROWN_EGG);
         entries.add((ItemConvertible)Items.BLUE_EGG);
         entries.add((ItemConvertible)Items.WIND_CHARGE);
         entries.add((ItemConvertible)Items.BOW);
         entries.add((ItemConvertible)Items.CROSSBOW);
         addFireworkRockets(entries, ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS);
         entries.add((ItemConvertible)Items.ARROW);
         entries.add((ItemConvertible)Items.SPECTRAL_ARROW);
         displayContext.lookup().getOptional(RegistryKeys.POTION).ifPresent((registryWrapper) -> addPotions(entries, registryWrapper, Items.TIPPED_ARROW, ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS, displayContext.enabledFeatures()));
      }).build());
      Registry.register(registry, (RegistryKey)FOOD_AND_DRINK, ItemGroup.create(ItemGroup.Row.BOTTOM, 2).displayName(Text.translatable("itemGroup.foodAndDrink")).icon(() -> new ItemStack(Items.GOLDEN_APPLE)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.APPLE);
         entries.add((ItemConvertible)Items.GOLDEN_APPLE);
         entries.add((ItemConvertible)Items.ENCHANTED_GOLDEN_APPLE);
         entries.add((ItemConvertible)Items.MELON_SLICE);
         entries.add((ItemConvertible)Items.SWEET_BERRIES);
         entries.add((ItemConvertible)Items.GLOW_BERRIES);
         entries.add((ItemConvertible)Items.CHORUS_FRUIT);
         entries.add((ItemConvertible)Items.CARROT);
         entries.add((ItemConvertible)Items.GOLDEN_CARROT);
         entries.add((ItemConvertible)Items.POTATO);
         entries.add((ItemConvertible)Items.BAKED_POTATO);
         entries.add((ItemConvertible)Items.POISONOUS_POTATO);
         entries.add((ItemConvertible)Items.BEETROOT);
         entries.add((ItemConvertible)Items.DRIED_KELP);
         entries.add((ItemConvertible)Items.BEEF);
         entries.add((ItemConvertible)Items.COOKED_BEEF);
         entries.add((ItemConvertible)Items.PORKCHOP);
         entries.add((ItemConvertible)Items.COOKED_PORKCHOP);
         entries.add((ItemConvertible)Items.MUTTON);
         entries.add((ItemConvertible)Items.COOKED_MUTTON);
         entries.add((ItemConvertible)Items.CHICKEN);
         entries.add((ItemConvertible)Items.COOKED_CHICKEN);
         entries.add((ItemConvertible)Items.RABBIT);
         entries.add((ItemConvertible)Items.COOKED_RABBIT);
         entries.add((ItemConvertible)Items.COD);
         entries.add((ItemConvertible)Items.COOKED_COD);
         entries.add((ItemConvertible)Items.SALMON);
         entries.add((ItemConvertible)Items.COOKED_SALMON);
         entries.add((ItemConvertible)Items.TROPICAL_FISH);
         entries.add((ItemConvertible)Items.PUFFERFISH);
         entries.add((ItemConvertible)Items.BREAD);
         entries.add((ItemConvertible)Items.COOKIE);
         entries.add((ItemConvertible)Items.CAKE);
         entries.add((ItemConvertible)Items.PUMPKIN_PIE);
         entries.add((ItemConvertible)Items.ROTTEN_FLESH);
         entries.add((ItemConvertible)Items.SPIDER_EYE);
         entries.add((ItemConvertible)Items.MUSHROOM_STEW);
         entries.add((ItemConvertible)Items.BEETROOT_SOUP);
         entries.add((ItemConvertible)Items.RABBIT_STEW);
         addSuspiciousStews(entries, ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS);
         entries.add((ItemConvertible)Items.MILK_BUCKET);
         entries.add((ItemConvertible)Items.HONEY_BOTTLE);
         addOminousBottles(entries, ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS);
         displayContext.lookup().getOptional(RegistryKeys.POTION).ifPresent((registryWrapper) -> {
            addPotions(entries, registryWrapper, Items.POTION, ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS, displayContext.enabledFeatures());
            addPotions(entries, registryWrapper, Items.SPLASH_POTION, ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS, displayContext.enabledFeatures());
            addPotions(entries, registryWrapper, Items.LINGERING_POTION, ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS, displayContext.enabledFeatures());
         });
      }).build());
      Registry.register(registry, (RegistryKey)INGREDIENTS, ItemGroup.create(ItemGroup.Row.BOTTOM, 3).displayName(Text.translatable("itemGroup.ingredients")).icon(() -> new ItemStack(Items.IRON_INGOT)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.COAL);
         entries.add((ItemConvertible)Items.CHARCOAL);
         entries.add((ItemConvertible)Items.RAW_IRON);
         entries.add((ItemConvertible)Items.RAW_COPPER);
         entries.add((ItemConvertible)Items.RAW_GOLD);
         entries.add((ItemConvertible)Items.EMERALD);
         entries.add((ItemConvertible)Items.LAPIS_LAZULI);
         entries.add((ItemConvertible)Items.DIAMOND);
         entries.add((ItemConvertible)Items.ANCIENT_DEBRIS);
         entries.add((ItemConvertible)Items.QUARTZ);
         entries.add((ItemConvertible)Items.AMETHYST_SHARD);
         entries.add((ItemConvertible)Items.IRON_NUGGET);
         entries.add((ItemConvertible)Items.GOLD_NUGGET);
         entries.add((ItemConvertible)Items.IRON_INGOT);
         entries.add((ItemConvertible)Items.COPPER_INGOT);
         entries.add((ItemConvertible)Items.GOLD_INGOT);
         entries.add((ItemConvertible)Items.NETHERITE_SCRAP);
         entries.add((ItemConvertible)Items.NETHERITE_INGOT);
         entries.add((ItemConvertible)Items.STICK);
         entries.add((ItemConvertible)Items.FLINT);
         entries.add((ItemConvertible)Items.WHEAT);
         entries.add((ItemConvertible)Items.BONE);
         entries.add((ItemConvertible)Items.BONE_MEAL);
         entries.add((ItemConvertible)Items.STRING);
         entries.add((ItemConvertible)Items.FEATHER);
         entries.add((ItemConvertible)Items.SNOWBALL);
         entries.add((ItemConvertible)Items.EGG);
         entries.add((ItemConvertible)Items.BROWN_EGG);
         entries.add((ItemConvertible)Items.BLUE_EGG);
         entries.add((ItemConvertible)Items.LEATHER);
         entries.add((ItemConvertible)Items.RABBIT_HIDE);
         entries.add((ItemConvertible)Items.HONEYCOMB);
         entries.add((ItemConvertible)Items.RESIN_CLUMP);
         entries.add((ItemConvertible)Items.INK_SAC);
         entries.add((ItemConvertible)Items.GLOW_INK_SAC);
         entries.add((ItemConvertible)Items.TURTLE_SCUTE);
         entries.add((ItemConvertible)Items.ARMADILLO_SCUTE);
         entries.add((ItemConvertible)Items.SLIME_BALL);
         entries.add((ItemConvertible)Items.CLAY_BALL);
         entries.add((ItemConvertible)Items.PRISMARINE_SHARD);
         entries.add((ItemConvertible)Items.PRISMARINE_CRYSTALS);
         entries.add((ItemConvertible)Items.NAUTILUS_SHELL);
         entries.add((ItemConvertible)Items.HEART_OF_THE_SEA);
         entries.add((ItemConvertible)Items.FIRE_CHARGE);
         entries.add((ItemConvertible)Items.BLAZE_ROD);
         entries.add((ItemConvertible)Items.BREEZE_ROD);
         entries.add((ItemConvertible)Items.HEAVY_CORE);
         entries.add((ItemConvertible)Items.NETHER_STAR);
         entries.add((ItemConvertible)Items.ENDER_PEARL);
         entries.add((ItemConvertible)Items.ENDER_EYE);
         entries.add((ItemConvertible)Items.SHULKER_SHELL);
         entries.add((ItemConvertible)Items.POPPED_CHORUS_FRUIT);
         entries.add((ItemConvertible)Items.ECHO_SHARD);
         entries.add((ItemConvertible)Items.DISC_FRAGMENT_5);
         entries.add((ItemConvertible)Items.WHITE_DYE);
         entries.add((ItemConvertible)Items.LIGHT_GRAY_DYE);
         entries.add((ItemConvertible)Items.GRAY_DYE);
         entries.add((ItemConvertible)Items.BLACK_DYE);
         entries.add((ItemConvertible)Items.BROWN_DYE);
         entries.add((ItemConvertible)Items.RED_DYE);
         entries.add((ItemConvertible)Items.ORANGE_DYE);
         entries.add((ItemConvertible)Items.YELLOW_DYE);
         entries.add((ItemConvertible)Items.LIME_DYE);
         entries.add((ItemConvertible)Items.GREEN_DYE);
         entries.add((ItemConvertible)Items.CYAN_DYE);
         entries.add((ItemConvertible)Items.LIGHT_BLUE_DYE);
         entries.add((ItemConvertible)Items.BLUE_DYE);
         entries.add((ItemConvertible)Items.PURPLE_DYE);
         entries.add((ItemConvertible)Items.MAGENTA_DYE);
         entries.add((ItemConvertible)Items.PINK_DYE);
         entries.add((ItemConvertible)Items.BOWL);
         entries.add((ItemConvertible)Items.BRICK);
         entries.add((ItemConvertible)Items.NETHER_BRICK);
         entries.add((ItemConvertible)Items.RESIN_BRICK);
         entries.add((ItemConvertible)Items.PAPER);
         entries.add((ItemConvertible)Items.BOOK);
         entries.add((ItemConvertible)Items.FIREWORK_STAR);
         entries.add((ItemConvertible)Items.GLASS_BOTTLE);
         entries.add((ItemConvertible)Items.NETHER_WART);
         entries.add((ItemConvertible)Items.REDSTONE);
         entries.add((ItemConvertible)Items.GLOWSTONE_DUST);
         entries.add((ItemConvertible)Items.GUNPOWDER);
         entries.add((ItemConvertible)Items.DRAGON_BREATH);
         entries.add((ItemConvertible)Items.FERMENTED_SPIDER_EYE);
         entries.add((ItemConvertible)Items.BLAZE_POWDER);
         entries.add((ItemConvertible)Items.SUGAR);
         entries.add((ItemConvertible)Items.RABBIT_FOOT);
         entries.add((ItemConvertible)Items.GLISTERING_MELON_SLICE);
         entries.add((ItemConvertible)Items.SPIDER_EYE);
         entries.add((ItemConvertible)Items.PUFFERFISH);
         entries.add((ItemConvertible)Items.MAGMA_CREAM);
         entries.add((ItemConvertible)Items.GOLDEN_CARROT);
         entries.add((ItemConvertible)Items.GHAST_TEAR);
         entries.add((ItemConvertible)Items.TURTLE_HELMET);
         entries.add((ItemConvertible)Items.PHANTOM_MEMBRANE);
         entries.add((ItemConvertible)Items.FIELD_MASONED_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.BORDURE_INDENTED_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.FLOWER_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.CREEPER_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.SKULL_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.MOJANG_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.GLOBE_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.PIGLIN_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.FLOW_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.GUSTER_BANNER_PATTERN);
         entries.add((ItemConvertible)Items.ANGLER_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.ARCHER_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.ARMS_UP_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.BLADE_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.BREWER_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.BURN_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.DANGER_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.EXPLORER_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.FLOW_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.FRIEND_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.GUSTER_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.HEART_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.HEARTBREAK_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.HOWL_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.MINER_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.MOURNER_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.PLENTY_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.PRIZE_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.SCRAPE_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.SHEAF_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.SHELTER_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.SKULL_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.SNORT_POTTERY_SHERD);
         entries.add((ItemConvertible)Items.NETHERITE_UPGRADE_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.SENTRY_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.VEX_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.WILD_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.COAST_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.DUNE_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.WAYFINDER_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.RAISER_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.SHAPER_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.HOST_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.WARD_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.SILENCE_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.TIDE_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.SNOUT_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.RIB_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.EYE_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.SPIRE_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.FLOW_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.BOLT_ARMOR_TRIM_SMITHING_TEMPLATE);
         entries.add((ItemConvertible)Items.EXPERIENCE_BOTTLE);
         entries.add((ItemConvertible)Items.TRIAL_KEY);
         entries.add((ItemConvertible)Items.OMINOUS_TRIAL_KEY);
         displayContext.lookup().getOptional(RegistryKeys.ENCHANTMENT).ifPresent((registryWrapper) -> {
            addMaxLevelEnchantedBooks(entries, registryWrapper, ItemGroup.StackVisibility.PARENT_TAB_ONLY);
            addAllLevelEnchantedBooks(entries, registryWrapper, ItemGroup.StackVisibility.SEARCH_TAB_ONLY);
         });
      }).build());
      Registry.register(registry, (RegistryKey)SPAWN_EGGS, ItemGroup.create(ItemGroup.Row.BOTTOM, 4).displayName(Text.translatable("itemGroup.spawnEggs")).icon(() -> new ItemStack(Items.CREEPER_SPAWN_EGG)).entries((displayContext, entries) -> {
         entries.add((ItemConvertible)Items.SPAWNER);
         entries.add((ItemConvertible)Items.TRIAL_SPAWNER);
         entries.add((ItemConvertible)Items.CREAKING_HEART);
         entries.add((ItemConvertible)Items.ALLAY_SPAWN_EGG);
         entries.add((ItemConvertible)Items.ARMADILLO_SPAWN_EGG);
         entries.add((ItemConvertible)Items.AXOLOTL_SPAWN_EGG);
         entries.add((ItemConvertible)Items.BAT_SPAWN_EGG);
         entries.add((ItemConvertible)Items.BEE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.BLAZE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.BOGGED_SPAWN_EGG);
         entries.add((ItemConvertible)Items.BREEZE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.CAMEL_SPAWN_EGG);
         entries.add((ItemConvertible)Items.CAT_SPAWN_EGG);
         entries.add((ItemConvertible)Items.CAVE_SPIDER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.CHICKEN_SPAWN_EGG);
         entries.add((ItemConvertible)Items.COD_SPAWN_EGG);
         entries.add((ItemConvertible)Items.COW_SPAWN_EGG);
         entries.add((ItemConvertible)Items.CREAKING_SPAWN_EGG);
         entries.add((ItemConvertible)Items.CREEPER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.DOLPHIN_SPAWN_EGG);
         entries.add((ItemConvertible)Items.DONKEY_SPAWN_EGG);
         entries.add((ItemConvertible)Items.DROWNED_SPAWN_EGG);
         entries.add((ItemConvertible)Items.ELDER_GUARDIAN_SPAWN_EGG);
         entries.add((ItemConvertible)Items.ENDERMAN_SPAWN_EGG);
         entries.add((ItemConvertible)Items.ENDERMITE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.EVOKER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.FOX_SPAWN_EGG);
         entries.add((ItemConvertible)Items.FROG_SPAWN_EGG);
         entries.add((ItemConvertible)Items.GHAST_SPAWN_EGG);
         entries.add((ItemConvertible)Items.GLOW_SQUID_SPAWN_EGG);
         entries.add((ItemConvertible)Items.GOAT_SPAWN_EGG);
         entries.add((ItemConvertible)Items.GUARDIAN_SPAWN_EGG);
         entries.add((ItemConvertible)Items.HOGLIN_SPAWN_EGG);
         entries.add((ItemConvertible)Items.HORSE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.HUSK_SPAWN_EGG);
         entries.add((ItemConvertible)Items.IRON_GOLEM_SPAWN_EGG);
         entries.add((ItemConvertible)Items.LLAMA_SPAWN_EGG);
         entries.add((ItemConvertible)Items.MAGMA_CUBE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.MOOSHROOM_SPAWN_EGG);
         entries.add((ItemConvertible)Items.MULE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.OCELOT_SPAWN_EGG);
         entries.add((ItemConvertible)Items.PANDA_SPAWN_EGG);
         entries.add((ItemConvertible)Items.PARROT_SPAWN_EGG);
         entries.add((ItemConvertible)Items.PHANTOM_SPAWN_EGG);
         entries.add((ItemConvertible)Items.PIG_SPAWN_EGG);
         entries.add((ItemConvertible)Items.PIGLIN_SPAWN_EGG);
         entries.add((ItemConvertible)Items.PIGLIN_BRUTE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.PILLAGER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.POLAR_BEAR_SPAWN_EGG);
         entries.add((ItemConvertible)Items.PUFFERFISH_SPAWN_EGG);
         entries.add((ItemConvertible)Items.RABBIT_SPAWN_EGG);
         entries.add((ItemConvertible)Items.RAVAGER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SALMON_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SHEEP_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SHULKER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SILVERFISH_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SKELETON_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SKELETON_HORSE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SLIME_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SNIFFER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SNOW_GOLEM_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SPIDER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.SQUID_SPAWN_EGG);
         entries.add((ItemConvertible)Items.STRAY_SPAWN_EGG);
         entries.add((ItemConvertible)Items.STRIDER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.TADPOLE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.TRADER_LLAMA_SPAWN_EGG);
         entries.add((ItemConvertible)Items.TROPICAL_FISH_SPAWN_EGG);
         entries.add((ItemConvertible)Items.TURTLE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.VEX_SPAWN_EGG);
         entries.add((ItemConvertible)Items.VILLAGER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.VINDICATOR_SPAWN_EGG);
         entries.add((ItemConvertible)Items.WANDERING_TRADER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.WARDEN_SPAWN_EGG);
         entries.add((ItemConvertible)Items.WITCH_SPAWN_EGG);
         entries.add((ItemConvertible)Items.WITHER_SKELETON_SPAWN_EGG);
         entries.add((ItemConvertible)Items.WOLF_SPAWN_EGG);
         entries.add((ItemConvertible)Items.ZOGLIN_SPAWN_EGG);
         entries.add((ItemConvertible)Items.ZOMBIE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.ZOMBIE_HORSE_SPAWN_EGG);
         entries.add((ItemConvertible)Items.ZOMBIE_VILLAGER_SPAWN_EGG);
         entries.add((ItemConvertible)Items.ZOMBIFIED_PIGLIN_SPAWN_EGG);
      }).build());
      Registry.register(registry, (RegistryKey)OPERATOR, ItemGroup.create(ItemGroup.Row.BOTTOM, 5).displayName(Text.translatable("itemGroup.op")).icon(() -> new ItemStack(Items.COMMAND_BLOCK)).special().entries((displayContext, entries) -> {
         if (displayContext.hasPermissions()) {
            entries.add((ItemConvertible)Items.COMMAND_BLOCK);
            entries.add((ItemConvertible)Items.CHAIN_COMMAND_BLOCK);
            entries.add((ItemConvertible)Items.REPEATING_COMMAND_BLOCK);
            entries.add((ItemConvertible)Items.COMMAND_BLOCK_MINECART);
            entries.add((ItemConvertible)Items.JIGSAW);
            entries.add((ItemConvertible)Items.STRUCTURE_BLOCK);
            entries.add((ItemConvertible)Items.STRUCTURE_VOID);
            entries.add((ItemConvertible)Items.BARRIER);
            entries.add((ItemConvertible)Items.DEBUG_STICK);
            entries.add((ItemConvertible)Items.TEST_INSTANCE_BLOCK);

            for(TestBlockMode testBlockMode : TestBlockMode.values()) {
               entries.add(TestBlock.applyBlockStateToStack(new ItemStack(Items.TEST_BLOCK), testBlockMode));
            }

            for(int i = 15; i >= 0; --i) {
               entries.add(LightBlock.addNbtForLevel(new ItemStack(Items.LIGHT), i));
            }

            displayContext.lookup().getOptional(RegistryKeys.PAINTING_VARIANT).ifPresent((registryWrapper) -> addPaintings(entries, displayContext.lookup(), registryWrapper, (registryEntry) -> !registryEntry.isIn(PaintingVariantTags.PLACEABLE), ItemGroup.StackVisibility.PARENT_AND_SEARCH_TABS));
         }

      }).build());
      return (ItemGroup)Registry.register(registry, (RegistryKey)INVENTORY, ItemGroup.create(ItemGroup.Row.BOTTOM, 6).displayName(Text.translatable("itemGroup.inventory")).icon(() -> new ItemStack(Blocks.CHEST)).texture(INVENTORY_TAB_TEXTURE_ID).noRenderedName().special().type(ItemGroup.Type.INVENTORY).noScrollbar().build());
   }

   public static void collect() {
      Map<Pair<ItemGroup.Row, Integer>, String> map = new HashMap();

      for(RegistryKey<ItemGroup> registryKey : Registries.ITEM_GROUP.getKeys()) {
         ItemGroup itemGroup = Registries.ITEM_GROUP.getValueOrThrow(registryKey);
         String string = itemGroup.getDisplayName().getString();
         String string2 = (String)map.put(Pair.of(itemGroup.getRow(), itemGroup.getColumn()), string);
         if (string2 != null) {
            throw new IllegalArgumentException("Duplicate position: " + string + " vs. " + string2);
         }
      }

   }

   public static ItemGroup getDefaultTab() {
      return Registries.ITEM_GROUP.getValueOrThrow(BUILDING_BLOCKS);
   }

   private static void addPotions(ItemGroup.Entries entries, RegistryWrapper<Potion> registryWrapper, Item item, ItemGroup.StackVisibility visibility, FeatureSet enabledFeatures) {
      registryWrapper.streamEntries().filter((potionEntry) -> ((Potion)potionEntry.value()).isEnabled(enabledFeatures)).map((entry) -> PotionContentsComponent.createStack(item, entry)).forEach((stack) -> entries.add(stack, visibility));
   }

   private static void addMaxLevelEnchantedBooks(ItemGroup.Entries entries, RegistryWrapper<Enchantment> registryWrapper, ItemGroup.StackVisibility stackVisibility) {
      registryWrapper.streamEntries().map((enchantmentEntry) -> EnchantmentHelper.getEnchantedBookWith(new EnchantmentLevelEntry(enchantmentEntry, ((Enchantment)enchantmentEntry.value()).getMaxLevel()))).forEach((stack) -> entries.add(stack, stackVisibility));
   }

   private static void addAllLevelEnchantedBooks(ItemGroup.Entries entries, RegistryWrapper<Enchantment> registryWrapper, ItemGroup.StackVisibility stackVisibility) {
      registryWrapper.streamEntries().flatMap((enchantmentEntry) -> IntStream.rangeClosed(((Enchantment)enchantmentEntry.value()).getMinLevel(), ((Enchantment)enchantmentEntry.value()).getMaxLevel()).mapToObj((level) -> EnchantmentHelper.getEnchantedBookWith(new EnchantmentLevelEntry(enchantmentEntry, level)))).forEach((stack) -> entries.add(stack, stackVisibility));
   }

   private static void addInstruments(ItemGroup.Entries entries, RegistryWrapper<Instrument> registryWrapper, Item item, TagKey<Instrument> instrumentTag, ItemGroup.StackVisibility visibility) {
      registryWrapper.getOptional(instrumentTag).ifPresent((entryList) -> entryList.stream().map((instrument) -> GoatHornItem.getStackForInstrument(item, instrument)).forEach((stack) -> entries.add(stack, visibility)));
   }

   private static void addSuspiciousStews(ItemGroup.Entries entries, ItemGroup.StackVisibility visibility) {
      List<SuspiciousStewIngredient> list = SuspiciousStewIngredient.getAll();
      Set<ItemStack> set = ItemStackSet.create();

      for(SuspiciousStewIngredient suspiciousStewIngredient : list) {
         ItemStack itemStack = new ItemStack(Items.SUSPICIOUS_STEW);
         itemStack.set(DataComponentTypes.SUSPICIOUS_STEW_EFFECTS, suspiciousStewIngredient.getStewEffects());
         set.add(itemStack);
      }

      entries.addAll(set, visibility);
   }

   private static void addOminousBottles(ItemGroup.Entries entries, ItemGroup.StackVisibility visibility) {
      for(int i = 0; i <= 4; ++i) {
         ItemStack itemStack = new ItemStack(Items.OMINOUS_BOTTLE);
         itemStack.set(DataComponentTypes.OMINOUS_BOTTLE_AMPLIFIER, new OminousBottleAmplifierComponent(i));
         entries.add(itemStack, visibility);
      }

   }

   private static void addFireworkRockets(ItemGroup.Entries entries, ItemGroup.StackVisibility visibility) {
      for(byte b : FireworkRocketItem.FLIGHT_VALUES) {
         ItemStack itemStack = new ItemStack(Items.FIREWORK_ROCKET);
         itemStack.set(DataComponentTypes.FIREWORKS, new FireworksComponent(b, List.of()));
         entries.add(itemStack, visibility);
      }

   }

   private static void addPaintings(ItemGroup.Entries entries, RegistryWrapper.WrapperLookup registries, RegistryWrapper.Impl<PaintingVariant> registryWrapper, Predicate<RegistryEntry<PaintingVariant>> filter, ItemGroup.StackVisibility stackVisibility) {
      RegistryOps<NbtElement> registryOps = registries.<NbtElement>getOps(NbtOps.INSTANCE);
      registryWrapper.streamEntries().filter(filter).sorted(PAINTING_VARIANT_COMPARATOR).forEach((reference) -> {
         ItemStack itemStack = new ItemStack(Items.PAINTING);
         itemStack.set(DataComponentTypes.PAINTING_VARIANT, reference);
         entries.add(itemStack, stackVisibility);
      });
   }

   public static List<ItemGroup> getGroupsToDisplay() {
      return stream().filter(ItemGroup::shouldDisplay).toList();
   }

   public static List<ItemGroup> getGroups() {
      return stream().toList();
   }

   private static Stream<ItemGroup> stream() {
      return Registries.ITEM_GROUP.stream();
   }

   public static ItemGroup getSearchGroup() {
      return Registries.ITEM_GROUP.getValueOrThrow(SEARCH);
   }

   private static void updateEntries(ItemGroup.DisplayContext displayContext) {
      stream().filter((group) -> group.getType() == ItemGroup.Type.CATEGORY).forEach((group) -> group.updateEntries(displayContext));
      stream().filter((group) -> group.getType() != ItemGroup.Type.CATEGORY).forEach((group) -> group.updateEntries(displayContext));
   }

   public static boolean updateDisplayContext(FeatureSet enabledFeatures, boolean operatorEnabled, RegistryWrapper.WrapperLookup registries) {
      if (displayContext != null && !displayContext.doesNotMatch(enabledFeatures, operatorEnabled, registries)) {
         return false;
      } else {
         displayContext = new ItemGroup.DisplayContext(enabledFeatures, operatorEnabled, registries);
         updateEntries(displayContext);
         return true;
      }
   }
}
