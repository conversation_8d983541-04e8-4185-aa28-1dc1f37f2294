package net.minecraft.item;

import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import net.minecraft.block.BeehiveBlock;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.block.LightBlock;
import net.minecraft.block.TestBlock;
import net.minecraft.block.entity.Sherds;
import net.minecraft.block.enums.TestBlockMode;
import net.minecraft.block.jukebox.JukeboxSongs;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.component.type.BannerPatternsComponent;
import net.minecraft.component.type.BeesComponent;
import net.minecraft.component.type.BlockStateComponent;
import net.minecraft.component.type.BlocksAttacksComponent;
import net.minecraft.component.type.BundleContentsComponent;
import net.minecraft.component.type.ChargedProjectilesComponent;
import net.minecraft.component.type.ConsumableComponents;
import net.minecraft.component.type.ContainerComponent;
import net.minecraft.component.type.DamageResistantComponent;
import net.minecraft.component.type.DeathProtectionComponent;
import net.minecraft.component.type.DebugStickStateComponent;
import net.minecraft.component.type.EquippableComponent;
import net.minecraft.component.type.FireworksComponent;
import net.minecraft.component.type.FoodComponents;
import net.minecraft.component.type.InstrumentComponent;
import net.minecraft.component.type.ItemEnchantmentsComponent;
import net.minecraft.component.type.MapColorComponent;
import net.minecraft.component.type.MapDecorationsComponent;
import net.minecraft.component.type.NbtComponent;
import net.minecraft.component.type.OminousBottleAmplifierComponent;
import net.minecraft.component.type.PotionContentsComponent;
import net.minecraft.component.type.SuspiciousStewEffectsComponent;
import net.minecraft.component.type.WeaponComponent;
import net.minecraft.component.type.WritableBookContentComponent;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.passive.ChickenVariants;
import net.minecraft.fluid.Fluids;
import net.minecraft.item.equipment.ArmorMaterials;
import net.minecraft.item.equipment.EquipmentAssetKeys;
import net.minecraft.item.equipment.EquipmentType;
import net.minecraft.item.equipment.trim.ArmorTrimMaterials;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.registry.entry.LazyRegistryEntryReference;
import net.minecraft.registry.tag.BannerPatternTags;
import net.minecraft.registry.tag.DamageTypeTags;
import net.minecraft.registry.tag.ItemTags;
import net.minecraft.sound.SoundEvents;
import net.minecraft.util.DyeColor;
import net.minecraft.util.Identifier;
import net.minecraft.util.Rarity;
import net.minecraft.util.Unit;
import net.minecraft.util.math.Direction;

public class Items {
   public static final Item AIR;
   public static final Item STONE;
   public static final Item GRANITE;
   public static final Item POLISHED_GRANITE;
   public static final Item DIORITE;
   public static final Item POLISHED_DIORITE;
   public static final Item ANDESITE;
   public static final Item POLISHED_ANDESITE;
   public static final Item DEEPSLATE;
   public static final Item COBBLED_DEEPSLATE;
   public static final Item POLISHED_DEEPSLATE;
   public static final Item CALCITE;
   public static final Item TUFF;
   public static final Item TUFF_SLAB;
   public static final Item TUFF_STAIRS;
   public static final Item TUFF_WALL;
   public static final Item CHISELED_TUFF;
   public static final Item POLISHED_TUFF;
   public static final Item POLISHED_TUFF_SLAB;
   public static final Item POLISHED_TUFF_STAIRS;
   public static final Item POLISHED_TUFF_WALL;
   public static final Item TUFF_BRICKS;
   public static final Item TUFF_BRICK_SLAB;
   public static final Item TUFF_BRICK_STAIRS;
   public static final Item TUFF_BRICK_WALL;
   public static final Item CHISELED_TUFF_BRICKS;
   public static final Item DRIPSTONE_BLOCK;
   public static final Item GRASS_BLOCK;
   public static final Item DIRT;
   public static final Item COARSE_DIRT;
   public static final Item PODZOL;
   public static final Item ROOTED_DIRT;
   public static final Item MUD;
   public static final Item CRIMSON_NYLIUM;
   public static final Item WARPED_NYLIUM;
   public static final Item COBBLESTONE;
   public static final Item OAK_PLANKS;
   public static final Item SPRUCE_PLANKS;
   public static final Item BIRCH_PLANKS;
   public static final Item JUNGLE_PLANKS;
   public static final Item ACACIA_PLANKS;
   public static final Item CHERRY_PLANKS;
   public static final Item DARK_OAK_PLANKS;
   public static final Item PALE_OAK_PLANKS;
   public static final Item MANGROVE_PLANKS;
   public static final Item BAMBOO_PLANKS;
   public static final Item CRIMSON_PLANKS;
   public static final Item WARPED_PLANKS;
   public static final Item BAMBOO_MOSAIC;
   public static final Item OAK_SAPLING;
   public static final Item SPRUCE_SAPLING;
   public static final Item BIRCH_SAPLING;
   public static final Item JUNGLE_SAPLING;
   public static final Item ACACIA_SAPLING;
   public static final Item CHERRY_SAPLING;
   public static final Item DARK_OAK_SAPLING;
   public static final Item PALE_OAK_SAPLING;
   public static final Item MANGROVE_PROPAGULE;
   public static final Item BEDROCK;
   public static final Item SAND;
   public static final Item SUSPICIOUS_SAND;
   public static final Item SUSPICIOUS_GRAVEL;
   public static final Item RED_SAND;
   public static final Item GRAVEL;
   public static final Item COAL_ORE;
   public static final Item DEEPSLATE_COAL_ORE;
   public static final Item IRON_ORE;
   public static final Item DEEPSLATE_IRON_ORE;
   public static final Item COPPER_ORE;
   public static final Item DEEPSLATE_COPPER_ORE;
   public static final Item GOLD_ORE;
   public static final Item DEEPSLATE_GOLD_ORE;
   public static final Item REDSTONE_ORE;
   public static final Item DEEPSLATE_REDSTONE_ORE;
   public static final Item EMERALD_ORE;
   public static final Item DEEPSLATE_EMERALD_ORE;
   public static final Item LAPIS_ORE;
   public static final Item DEEPSLATE_LAPIS_ORE;
   public static final Item DIAMOND_ORE;
   public static final Item DEEPSLATE_DIAMOND_ORE;
   public static final Item NETHER_GOLD_ORE;
   public static final Item NETHER_QUARTZ_ORE;
   public static final Item ANCIENT_DEBRIS;
   public static final Item COAL_BLOCK;
   public static final Item RAW_IRON_BLOCK;
   public static final Item RAW_COPPER_BLOCK;
   public static final Item RAW_GOLD_BLOCK;
   public static final Item HEAVY_CORE;
   public static final Item AMETHYST_BLOCK;
   public static final Item BUDDING_AMETHYST;
   public static final Item IRON_BLOCK;
   public static final Item COPPER_BLOCK;
   public static final Item GOLD_BLOCK;
   public static final Item DIAMOND_BLOCK;
   public static final Item NETHERITE_BLOCK;
   public static final Item EXPOSED_COPPER;
   public static final Item WEATHERED_COPPER;
   public static final Item OXIDIZED_COPPER;
   public static final Item CHISELED_COPPER;
   public static final Item EXPOSED_CHISELED_COPPER;
   public static final Item WEATHERED_CHISELED_COPPER;
   public static final Item OXIDIZED_CHISELED_COPPER;
   public static final Item CUT_COPPER;
   public static final Item EXPOSED_CUT_COPPER;
   public static final Item WEATHERED_CUT_COPPER;
   public static final Item OXIDIZED_CUT_COPPER;
   public static final Item CUT_COPPER_STAIRS;
   public static final Item EXPOSED_CUT_COPPER_STAIRS;
   public static final Item WEATHERED_CUT_COPPER_STAIRS;
   public static final Item OXIDIZED_CUT_COPPER_STAIRS;
   public static final Item CUT_COPPER_SLAB;
   public static final Item EXPOSED_CUT_COPPER_SLAB;
   public static final Item WEATHERED_CUT_COPPER_SLAB;
   public static final Item OXIDIZED_CUT_COPPER_SLAB;
   public static final Item WAXED_COPPER_BLOCK;
   public static final Item WAXED_EXPOSED_COPPER;
   public static final Item WAXED_WEATHERED_COPPER;
   public static final Item WAXED_OXIDIZED_COPPER;
   public static final Item WAXED_CHISELED_COPPER;
   public static final Item WAXED_EXPOSED_CHISELED_COPPER;
   public static final Item WAXED_WEATHERED_CHISELED_COPPER;
   public static final Item WAXED_OXIDIZED_CHISELED_COPPER;
   public static final Item WAXED_CUT_COPPER;
   public static final Item WAXED_EXPOSED_CUT_COPPER;
   public static final Item WAXED_WEATHERED_CUT_COPPER;
   public static final Item WAXED_OXIDIZED_CUT_COPPER;
   public static final Item WAXED_CUT_COPPER_STAIRS;
   public static final Item WAXED_EXPOSED_CUT_COPPER_STAIRS;
   public static final Item WAXED_WEATHERED_CUT_COPPER_STAIRS;
   public static final Item WAXED_OXIDIZED_CUT_COPPER_STAIRS;
   public static final Item WAXED_CUT_COPPER_SLAB;
   public static final Item WAXED_EXPOSED_CUT_COPPER_SLAB;
   public static final Item WAXED_WEATHERED_CUT_COPPER_SLAB;
   public static final Item WAXED_OXIDIZED_CUT_COPPER_SLAB;
   public static final Item OAK_LOG;
   public static final Item SPRUCE_LOG;
   public static final Item BIRCH_LOG;
   public static final Item JUNGLE_LOG;
   public static final Item ACACIA_LOG;
   public static final Item CHERRY_LOG;
   public static final Item PALE_OAK_LOG;
   public static final Item DARK_OAK_LOG;
   public static final Item MANGROVE_LOG;
   public static final Item MANGROVE_ROOTS;
   public static final Item MUDDY_MANGROVE_ROOTS;
   public static final Item CRIMSON_STEM;
   public static final Item WARPED_STEM;
   public static final Item BAMBOO_BLOCK;
   public static final Item STRIPPED_OAK_LOG;
   public static final Item STRIPPED_SPRUCE_LOG;
   public static final Item STRIPPED_BIRCH_LOG;
   public static final Item STRIPPED_JUNGLE_LOG;
   public static final Item STRIPPED_ACACIA_LOG;
   public static final Item STRIPPED_CHERRY_LOG;
   public static final Item STRIPPED_DARK_OAK_LOG;
   public static final Item STRIPPED_PALE_OAK_LOG;
   public static final Item STRIPPED_MANGROVE_LOG;
   public static final Item STRIPPED_CRIMSON_STEM;
   public static final Item STRIPPED_WARPED_STEM;
   public static final Item STRIPPED_OAK_WOOD;
   public static final Item STRIPPED_SPRUCE_WOOD;
   public static final Item STRIPPED_BIRCH_WOOD;
   public static final Item STRIPPED_JUNGLE_WOOD;
   public static final Item STRIPPED_ACACIA_WOOD;
   public static final Item STRIPPED_CHERRY_WOOD;
   public static final Item STRIPPED_DARK_OAK_WOOD;
   public static final Item STRIPPED_PALE_OAK_WOOD;
   public static final Item STRIPPED_MANGROVE_WOOD;
   public static final Item STRIPPED_CRIMSON_HYPHAE;
   public static final Item STRIPPED_WARPED_HYPHAE;
   public static final Item STRIPPED_BAMBOO_BLOCK;
   public static final Item OAK_WOOD;
   public static final Item SPRUCE_WOOD;
   public static final Item BIRCH_WOOD;
   public static final Item JUNGLE_WOOD;
   public static final Item ACACIA_WOOD;
   public static final Item CHERRY_WOOD;
   public static final Item PALE_OAK_WOOD;
   public static final Item DARK_OAK_WOOD;
   public static final Item MANGROVE_WOOD;
   public static final Item CRIMSON_HYPHAE;
   public static final Item WARPED_HYPHAE;
   public static final Item OAK_LEAVES;
   public static final Item SPRUCE_LEAVES;
   public static final Item BIRCH_LEAVES;
   public static final Item JUNGLE_LEAVES;
   public static final Item ACACIA_LEAVES;
   public static final Item CHERRY_LEAVES;
   public static final Item DARK_OAK_LEAVES;
   public static final Item PALE_OAK_LEAVES;
   public static final Item MANGROVE_LEAVES;
   public static final Item AZALEA_LEAVES;
   public static final Item FLOWERING_AZALEA_LEAVES;
   public static final Item SPONGE;
   public static final Item WET_SPONGE;
   public static final Item GLASS;
   public static final Item TINTED_GLASS;
   public static final Item LAPIS_BLOCK;
   public static final Item SANDSTONE;
   public static final Item CHISELED_SANDSTONE;
   public static final Item CUT_SANDSTONE;
   public static final Item COBWEB;
   public static final Item SHORT_GRASS;
   public static final Item FERN;
   public static final Item BUSH;
   public static final Item AZALEA;
   public static final Item FLOWERING_AZALEA;
   public static final Item DEAD_BUSH;
   public static final Item FIREFLY_BUSH;
   public static final Item SHORT_DRY_GRASS;
   public static final Item TALL_DRY_GRASS;
   public static final Item SEAGRASS;
   public static final Item SEA_PICKLE;
   public static final Item WHITE_WOOL;
   public static final Item ORANGE_WOOL;
   public static final Item MAGENTA_WOOL;
   public static final Item LIGHT_BLUE_WOOL;
   public static final Item YELLOW_WOOL;
   public static final Item LIME_WOOL;
   public static final Item PINK_WOOL;
   public static final Item GRAY_WOOL;
   public static final Item LIGHT_GRAY_WOOL;
   public static final Item CYAN_WOOL;
   public static final Item PURPLE_WOOL;
   public static final Item BLUE_WOOL;
   public static final Item BROWN_WOOL;
   public static final Item GREEN_WOOL;
   public static final Item RED_WOOL;
   public static final Item BLACK_WOOL;
   public static final Item DANDELION;
   public static final Item OPEN_EYEBLOSSOM;
   public static final Item CLOSED_EYEBLOSSOM;
   public static final Item POPPY;
   public static final Item BLUE_ORCHID;
   public static final Item ALLIUM;
   public static final Item AZURE_BLUET;
   public static final Item RED_TULIP;
   public static final Item ORANGE_TULIP;
   public static final Item WHITE_TULIP;
   public static final Item PINK_TULIP;
   public static final Item OXEYE_DAISY;
   public static final Item CORNFLOWER;
   public static final Item LILY_OF_THE_VALLEY;
   public static final Item WITHER_ROSE;
   public static final Item TORCHFLOWER;
   public static final Item PITCHER_PLANT;
   public static final Item SPORE_BLOSSOM;
   public static final Item BROWN_MUSHROOM;
   public static final Item RED_MUSHROOM;
   public static final Item CRIMSON_FUNGUS;
   public static final Item WARPED_FUNGUS;
   public static final Item CRIMSON_ROOTS;
   public static final Item WARPED_ROOTS;
   public static final Item NETHER_SPROUTS;
   public static final Item WEEPING_VINES;
   public static final Item TWISTING_VINES;
   public static final Item SUGAR_CANE;
   public static final Item KELP;
   public static final Item PINK_PETALS;
   public static final Item WILDFLOWERS;
   public static final Item LEAF_LITTER;
   public static final Item MOSS_CARPET;
   public static final Item MOSS_BLOCK;
   public static final Item PALE_MOSS_CARPET;
   public static final Item PALE_HANGING_MOSS;
   public static final Item PALE_MOSS_BLOCK;
   public static final Item HANGING_ROOTS;
   public static final Item BIG_DRIPLEAF;
   public static final Item SMALL_DRIPLEAF;
   public static final Item BAMBOO;
   public static final Item OAK_SLAB;
   public static final Item SPRUCE_SLAB;
   public static final Item BIRCH_SLAB;
   public static final Item JUNGLE_SLAB;
   public static final Item ACACIA_SLAB;
   public static final Item CHERRY_SLAB;
   public static final Item DARK_OAK_SLAB;
   public static final Item PALE_OAK_SLAB;
   public static final Item MANGROVE_SLAB;
   public static final Item BAMBOO_SLAB;
   public static final Item BAMBOO_MOSAIC_SLAB;
   public static final Item CRIMSON_SLAB;
   public static final Item WARPED_SLAB;
   public static final Item STONE_SLAB;
   public static final Item SMOOTH_STONE_SLAB;
   public static final Item SANDSTONE_SLAB;
   public static final Item CUT_SANDSTONE_SLAB;
   public static final Item PETRIFIED_OAK_SLAB;
   public static final Item COBBLESTONE_SLAB;
   public static final Item BRICK_SLAB;
   public static final Item STONE_BRICK_SLAB;
   public static final Item MUD_BRICK_SLAB;
   public static final Item NETHER_BRICK_SLAB;
   public static final Item QUARTZ_SLAB;
   public static final Item RED_SANDSTONE_SLAB;
   public static final Item CUT_RED_SANDSTONE_SLAB;
   public static final Item PURPUR_SLAB;
   public static final Item PRISMARINE_SLAB;
   public static final Item PRISMARINE_BRICK_SLAB;
   public static final Item DARK_PRISMARINE_SLAB;
   public static final Item SMOOTH_QUARTZ;
   public static final Item SMOOTH_RED_SANDSTONE;
   public static final Item SMOOTH_SANDSTONE;
   public static final Item SMOOTH_STONE;
   public static final Item BRICKS;
   public static final Item BOOKSHELF;
   public static final Item CHISELED_BOOKSHELF;
   public static final Item DECORATED_POT;
   public static final Item MOSSY_COBBLESTONE;
   public static final Item OBSIDIAN;
   public static final Item TORCH;
   public static final Item END_ROD;
   public static final Item CHORUS_PLANT;
   public static final Item CHORUS_FLOWER;
   public static final Item PURPUR_BLOCK;
   public static final Item PURPUR_PILLAR;
   public static final Item PURPUR_STAIRS;
   public static final Item SPAWNER;
   public static final Item CREAKING_HEART;
   public static final Item CHEST;
   public static final Item CRAFTING_TABLE;
   public static final Item FARMLAND;
   public static final Item FURNACE;
   public static final Item LADDER;
   public static final Item COBBLESTONE_STAIRS;
   public static final Item SNOW;
   public static final Item ICE;
   public static final Item SNOW_BLOCK;
   public static final Item CACTUS;
   public static final Item CACTUS_FLOWER;
   public static final Item CLAY;
   public static final Item JUKEBOX;
   public static final Item OAK_FENCE;
   public static final Item SPRUCE_FENCE;
   public static final Item BIRCH_FENCE;
   public static final Item JUNGLE_FENCE;
   public static final Item ACACIA_FENCE;
   public static final Item CHERRY_FENCE;
   public static final Item DARK_OAK_FENCE;
   public static final Item PALE_OAK_FENCE;
   public static final Item MANGROVE_FENCE;
   public static final Item BAMBOO_FENCE;
   public static final Item CRIMSON_FENCE;
   public static final Item WARPED_FENCE;
   public static final Item PUMPKIN;
   public static final Item CARVED_PUMPKIN;
   public static final Item JACK_O_LANTERN;
   public static final Item NETHERRACK;
   public static final Item SOUL_SAND;
   public static final Item SOUL_SOIL;
   public static final Item BASALT;
   public static final Item POLISHED_BASALT;
   public static final Item SMOOTH_BASALT;
   public static final Item SOUL_TORCH;
   public static final Item GLOWSTONE;
   public static final Item INFESTED_STONE;
   public static final Item INFESTED_COBBLESTONE;
   public static final Item INFESTED_STONE_BRICKS;
   public static final Item INFESTED_MOSSY_STONE_BRICKS;
   public static final Item INFESTED_CRACKED_STONE_BRICKS;
   public static final Item INFESTED_CHISELED_STONE_BRICKS;
   public static final Item INFESTED_DEEPSLATE;
   public static final Item STONE_BRICKS;
   public static final Item MOSSY_STONE_BRICKS;
   public static final Item CRACKED_STONE_BRICKS;
   public static final Item CHISELED_STONE_BRICKS;
   public static final Item PACKED_MUD;
   public static final Item MUD_BRICKS;
   public static final Item DEEPSLATE_BRICKS;
   public static final Item CRACKED_DEEPSLATE_BRICKS;
   public static final Item DEEPSLATE_TILES;
   public static final Item CRACKED_DEEPSLATE_TILES;
   public static final Item CHISELED_DEEPSLATE;
   public static final Item REINFORCED_DEEPSLATE;
   public static final Item BROWN_MUSHROOM_BLOCK;
   public static final Item RED_MUSHROOM_BLOCK;
   public static final Item MUSHROOM_STEM;
   public static final Item IRON_BARS;
   public static final Item CHAIN;
   public static final Item GLASS_PANE;
   public static final Item MELON;
   public static final Item VINE;
   public static final Item GLOW_LICHEN;
   public static final Item RESIN_CLUMP;
   public static final Item RESIN_BLOCK;
   public static final Item RESIN_BRICKS;
   public static final Item RESIN_BRICK_STAIRS;
   public static final Item RESIN_BRICK_SLAB;
   public static final Item RESIN_BRICK_WALL;
   public static final Item CHISELED_RESIN_BRICKS;
   public static final Item BRICK_STAIRS;
   public static final Item STONE_BRICK_STAIRS;
   public static final Item MUD_BRICK_STAIRS;
   public static final Item MYCELIUM;
   public static final Item LILY_PAD;
   public static final Item NETHER_BRICKS;
   public static final Item CRACKED_NETHER_BRICKS;
   public static final Item CHISELED_NETHER_BRICKS;
   public static final Item NETHER_BRICK_FENCE;
   public static final Item NETHER_BRICK_STAIRS;
   public static final Item SCULK;
   public static final Item SCULK_VEIN;
   public static final Item SCULK_CATALYST;
   public static final Item SCULK_SHRIEKER;
   public static final Item ENCHANTING_TABLE;
   public static final Item END_PORTAL_FRAME;
   public static final Item END_STONE;
   public static final Item END_STONE_BRICKS;
   public static final Item DRAGON_EGG;
   public static final Item SANDSTONE_STAIRS;
   public static final Item ENDER_CHEST;
   public static final Item EMERALD_BLOCK;
   public static final Item OAK_STAIRS;
   public static final Item SPRUCE_STAIRS;
   public static final Item BIRCH_STAIRS;
   public static final Item JUNGLE_STAIRS;
   public static final Item ACACIA_STAIRS;
   public static final Item CHERRY_STAIRS;
   public static final Item DARK_OAK_STAIRS;
   public static final Item PALE_OAK_STAIRS;
   public static final Item MANGROVE_STAIRS;
   public static final Item BAMBOO_STAIRS;
   public static final Item BAMBOO_MOSAIC_STAIRS;
   public static final Item CRIMSON_STAIRS;
   public static final Item WARPED_STAIRS;
   public static final Item COMMAND_BLOCK;
   public static final Item BEACON;
   public static final Item COBBLESTONE_WALL;
   public static final Item MOSSY_COBBLESTONE_WALL;
   public static final Item BRICK_WALL;
   public static final Item PRISMARINE_WALL;
   public static final Item RED_SANDSTONE_WALL;
   public static final Item MOSSY_STONE_BRICK_WALL;
   public static final Item GRANITE_WALL;
   public static final Item STONE_BRICK_WALL;
   public static final Item MUD_BRICK_WALL;
   public static final Item NETHER_BRICK_WALL;
   public static final Item ANDESITE_WALL;
   public static final Item RED_NETHER_BRICK_WALL;
   public static final Item SANDSTONE_WALL;
   public static final Item END_STONE_BRICK_WALL;
   public static final Item DIORITE_WALL;
   public static final Item BLACKSTONE_WALL;
   public static final Item POLISHED_BLACKSTONE_WALL;
   public static final Item POLISHED_BLACKSTONE_BRICK_WALL;
   public static final Item COBBLED_DEEPSLATE_WALL;
   public static final Item POLISHED_DEEPSLATE_WALL;
   public static final Item DEEPSLATE_BRICK_WALL;
   public static final Item DEEPSLATE_TILE_WALL;
   public static final Item ANVIL;
   public static final Item CHIPPED_ANVIL;
   public static final Item DAMAGED_ANVIL;
   public static final Item CHISELED_QUARTZ_BLOCK;
   public static final Item QUARTZ_BLOCK;
   public static final Item QUARTZ_BRICKS;
   public static final Item QUARTZ_PILLAR;
   public static final Item QUARTZ_STAIRS;
   public static final Item WHITE_TERRACOTTA;
   public static final Item ORANGE_TERRACOTTA;
   public static final Item MAGENTA_TERRACOTTA;
   public static final Item LIGHT_BLUE_TERRACOTTA;
   public static final Item YELLOW_TERRACOTTA;
   public static final Item LIME_TERRACOTTA;
   public static final Item PINK_TERRACOTTA;
   public static final Item GRAY_TERRACOTTA;
   public static final Item LIGHT_GRAY_TERRACOTTA;
   public static final Item CYAN_TERRACOTTA;
   public static final Item PURPLE_TERRACOTTA;
   public static final Item BLUE_TERRACOTTA;
   public static final Item BROWN_TERRACOTTA;
   public static final Item GREEN_TERRACOTTA;
   public static final Item RED_TERRACOTTA;
   public static final Item BLACK_TERRACOTTA;
   public static final Item BARRIER;
   public static final Item LIGHT;
   public static final Item HAY_BLOCK;
   public static final Item WHITE_CARPET;
   public static final Item ORANGE_CARPET;
   public static final Item MAGENTA_CARPET;
   public static final Item LIGHT_BLUE_CARPET;
   public static final Item YELLOW_CARPET;
   public static final Item LIME_CARPET;
   public static final Item PINK_CARPET;
   public static final Item GRAY_CARPET;
   public static final Item LIGHT_GRAY_CARPET;
   public static final Item CYAN_CARPET;
   public static final Item PURPLE_CARPET;
   public static final Item BLUE_CARPET;
   public static final Item BROWN_CARPET;
   public static final Item GREEN_CARPET;
   public static final Item RED_CARPET;
   public static final Item BLACK_CARPET;
   public static final Item TERRACOTTA;
   public static final Item PACKED_ICE;
   public static final Item DIRT_PATH;
   public static final Item SUNFLOWER;
   public static final Item LILAC;
   public static final Item ROSE_BUSH;
   public static final Item PEONY;
   public static final Item TALL_GRASS;
   public static final Item LARGE_FERN;
   public static final Item WHITE_STAINED_GLASS;
   public static final Item ORANGE_STAINED_GLASS;
   public static final Item MAGENTA_STAINED_GLASS;
   public static final Item LIGHT_BLUE_STAINED_GLASS;
   public static final Item YELLOW_STAINED_GLASS;
   public static final Item LIME_STAINED_GLASS;
   public static final Item PINK_STAINED_GLASS;
   public static final Item GRAY_STAINED_GLASS;
   public static final Item LIGHT_GRAY_STAINED_GLASS;
   public static final Item CYAN_STAINED_GLASS;
   public static final Item PURPLE_STAINED_GLASS;
   public static final Item BLUE_STAINED_GLASS;
   public static final Item BROWN_STAINED_GLASS;
   public static final Item GREEN_STAINED_GLASS;
   public static final Item RED_STAINED_GLASS;
   public static final Item BLACK_STAINED_GLASS;
   public static final Item WHITE_STAINED_GLASS_PANE;
   public static final Item ORANGE_STAINED_GLASS_PANE;
   public static final Item MAGENTA_STAINED_GLASS_PANE;
   public static final Item LIGHT_BLUE_STAINED_GLASS_PANE;
   public static final Item YELLOW_STAINED_GLASS_PANE;
   public static final Item LIME_STAINED_GLASS_PANE;
   public static final Item PINK_STAINED_GLASS_PANE;
   public static final Item GRAY_STAINED_GLASS_PANE;
   public static final Item LIGHT_GRAY_STAINED_GLASS_PANE;
   public static final Item CYAN_STAINED_GLASS_PANE;
   public static final Item PURPLE_STAINED_GLASS_PANE;
   public static final Item BLUE_STAINED_GLASS_PANE;
   public static final Item BROWN_STAINED_GLASS_PANE;
   public static final Item GREEN_STAINED_GLASS_PANE;
   public static final Item RED_STAINED_GLASS_PANE;
   public static final Item BLACK_STAINED_GLASS_PANE;
   public static final Item PRISMARINE;
   public static final Item PRISMARINE_BRICKS;
   public static final Item DARK_PRISMARINE;
   public static final Item PRISMARINE_STAIRS;
   public static final Item PRISMARINE_BRICK_STAIRS;
   public static final Item DARK_PRISMARINE_STAIRS;
   public static final Item SEA_LANTERN;
   public static final Item RED_SANDSTONE;
   public static final Item CHISELED_RED_SANDSTONE;
   public static final Item CUT_RED_SANDSTONE;
   public static final Item RED_SANDSTONE_STAIRS;
   public static final Item REPEATING_COMMAND_BLOCK;
   public static final Item CHAIN_COMMAND_BLOCK;
   public static final Item MAGMA_BLOCK;
   public static final Item NETHER_WART_BLOCK;
   public static final Item WARPED_WART_BLOCK;
   public static final Item RED_NETHER_BRICKS;
   public static final Item BONE_BLOCK;
   public static final Item STRUCTURE_VOID;
   public static final Item SHULKER_BOX;
   public static final Item WHITE_SHULKER_BOX;
   public static final Item ORANGE_SHULKER_BOX;
   public static final Item MAGENTA_SHULKER_BOX;
   public static final Item LIGHT_BLUE_SHULKER_BOX;
   public static final Item YELLOW_SHULKER_BOX;
   public static final Item LIME_SHULKER_BOX;
   public static final Item PINK_SHULKER_BOX;
   public static final Item GRAY_SHULKER_BOX;
   public static final Item LIGHT_GRAY_SHULKER_BOX;
   public static final Item CYAN_SHULKER_BOX;
   public static final Item PURPLE_SHULKER_BOX;
   public static final Item BLUE_SHULKER_BOX;
   public static final Item BROWN_SHULKER_BOX;
   public static final Item GREEN_SHULKER_BOX;
   public static final Item RED_SHULKER_BOX;
   public static final Item BLACK_SHULKER_BOX;
   public static final Item WHITE_GLAZED_TERRACOTTA;
   public static final Item ORANGE_GLAZED_TERRACOTTA;
   public static final Item MAGENTA_GLAZED_TERRACOTTA;
   public static final Item LIGHT_BLUE_GLAZED_TERRACOTTA;
   public static final Item YELLOW_GLAZED_TERRACOTTA;
   public static final Item LIME_GLAZED_TERRACOTTA;
   public static final Item PINK_GLAZED_TERRACOTTA;
   public static final Item GRAY_GLAZED_TERRACOTTA;
   public static final Item LIGHT_GRAY_GLAZED_TERRACOTTA;
   public static final Item CYAN_GLAZED_TERRACOTTA;
   public static final Item PURPLE_GLAZED_TERRACOTTA;
   public static final Item BLUE_GLAZED_TERRACOTTA;
   public static final Item BROWN_GLAZED_TERRACOTTA;
   public static final Item GREEN_GLAZED_TERRACOTTA;
   public static final Item RED_GLAZED_TERRACOTTA;
   public static final Item BLACK_GLAZED_TERRACOTTA;
   public static final Item WHITE_CONCRETE;
   public static final Item ORANGE_CONCRETE;
   public static final Item MAGENTA_CONCRETE;
   public static final Item LIGHT_BLUE_CONCRETE;
   public static final Item YELLOW_CONCRETE;
   public static final Item LIME_CONCRETE;
   public static final Item PINK_CONCRETE;
   public static final Item GRAY_CONCRETE;
   public static final Item LIGHT_GRAY_CONCRETE;
   public static final Item CYAN_CONCRETE;
   public static final Item PURPLE_CONCRETE;
   public static final Item BLUE_CONCRETE;
   public static final Item BROWN_CONCRETE;
   public static final Item GREEN_CONCRETE;
   public static final Item RED_CONCRETE;
   public static final Item BLACK_CONCRETE;
   public static final Item WHITE_CONCRETE_POWDER;
   public static final Item ORANGE_CONCRETE_POWDER;
   public static final Item MAGENTA_CONCRETE_POWDER;
   public static final Item LIGHT_BLUE_CONCRETE_POWDER;
   public static final Item YELLOW_CONCRETE_POWDER;
   public static final Item LIME_CONCRETE_POWDER;
   public static final Item PINK_CONCRETE_POWDER;
   public static final Item GRAY_CONCRETE_POWDER;
   public static final Item LIGHT_GRAY_CONCRETE_POWDER;
   public static final Item CYAN_CONCRETE_POWDER;
   public static final Item PURPLE_CONCRETE_POWDER;
   public static final Item BLUE_CONCRETE_POWDER;
   public static final Item BROWN_CONCRETE_POWDER;
   public static final Item GREEN_CONCRETE_POWDER;
   public static final Item RED_CONCRETE_POWDER;
   public static final Item BLACK_CONCRETE_POWDER;
   public static final Item TURTLE_EGG;
   public static final Item SNIFFER_EGG;
   public static final Item DEAD_TUBE_CORAL_BLOCK;
   public static final Item DEAD_BRAIN_CORAL_BLOCK;
   public static final Item DEAD_BUBBLE_CORAL_BLOCK;
   public static final Item DEAD_FIRE_CORAL_BLOCK;
   public static final Item DEAD_HORN_CORAL_BLOCK;
   public static final Item TUBE_CORAL_BLOCK;
   public static final Item BRAIN_CORAL_BLOCK;
   public static final Item BUBBLE_CORAL_BLOCK;
   public static final Item FIRE_CORAL_BLOCK;
   public static final Item HORN_CORAL_BLOCK;
   public static final Item TUBE_CORAL;
   public static final Item BRAIN_CORAL;
   public static final Item BUBBLE_CORAL;
   public static final Item FIRE_CORAL;
   public static final Item HORN_CORAL;
   public static final Item DEAD_BRAIN_CORAL;
   public static final Item DEAD_BUBBLE_CORAL;
   public static final Item DEAD_FIRE_CORAL;
   public static final Item DEAD_HORN_CORAL;
   public static final Item DEAD_TUBE_CORAL;
   public static final Item TUBE_CORAL_FAN;
   public static final Item BRAIN_CORAL_FAN;
   public static final Item BUBBLE_CORAL_FAN;
   public static final Item FIRE_CORAL_FAN;
   public static final Item HORN_CORAL_FAN;
   public static final Item DEAD_TUBE_CORAL_FAN;
   public static final Item DEAD_BRAIN_CORAL_FAN;
   public static final Item DEAD_BUBBLE_CORAL_FAN;
   public static final Item DEAD_FIRE_CORAL_FAN;
   public static final Item DEAD_HORN_CORAL_FAN;
   public static final Item BLUE_ICE;
   public static final Item CONDUIT;
   public static final Item POLISHED_GRANITE_STAIRS;
   public static final Item SMOOTH_RED_SANDSTONE_STAIRS;
   public static final Item MOSSY_STONE_BRICK_STAIRS;
   public static final Item POLISHED_DIORITE_STAIRS;
   public static final Item MOSSY_COBBLESTONE_STAIRS;
   public static final Item END_STONE_BRICK_STAIRS;
   public static final Item STONE_STAIRS;
   public static final Item SMOOTH_SANDSTONE_STAIRS;
   public static final Item SMOOTH_QUARTZ_STAIRS;
   public static final Item GRANITE_STAIRS;
   public static final Item ANDESITE_STAIRS;
   public static final Item RED_NETHER_BRICK_STAIRS;
   public static final Item POLISHED_ANDESITE_STAIRS;
   public static final Item DIORITE_STAIRS;
   public static final Item COBBLED_DEEPSLATE_STAIRS;
   public static final Item POLISHED_DEEPSLATE_STAIRS;
   public static final Item DEEPSLATE_BRICK_STAIRS;
   public static final Item DEEPSLATE_TILE_STAIRS;
   public static final Item POLISHED_GRANITE_SLAB;
   public static final Item SMOOTH_RED_SANDSTONE_SLAB;
   public static final Item MOSSY_STONE_BRICK_SLAB;
   public static final Item POLISHED_DIORITE_SLAB;
   public static final Item MOSSY_COBBLESTONE_SLAB;
   public static final Item END_STONE_BRICK_SLAB;
   public static final Item SMOOTH_SANDSTONE_SLAB;
   public static final Item SMOOTH_QUARTZ_SLAB;
   public static final Item GRANITE_SLAB;
   public static final Item ANDESITE_SLAB;
   public static final Item RED_NETHER_BRICK_SLAB;
   public static final Item POLISHED_ANDESITE_SLAB;
   public static final Item DIORITE_SLAB;
   public static final Item COBBLED_DEEPSLATE_SLAB;
   public static final Item POLISHED_DEEPSLATE_SLAB;
   public static final Item DEEPSLATE_BRICK_SLAB;
   public static final Item DEEPSLATE_TILE_SLAB;
   public static final Item SCAFFOLDING;
   public static final Item REDSTONE;
   public static final Item REDSTONE_TORCH;
   public static final Item REDSTONE_BLOCK;
   public static final Item REPEATER;
   public static final Item COMPARATOR;
   public static final Item PISTON;
   public static final Item STICKY_PISTON;
   public static final Item SLIME_BLOCK;
   public static final Item HONEY_BLOCK;
   public static final Item OBSERVER;
   public static final Item HOPPER;
   public static final Item DISPENSER;
   public static final Item DROPPER;
   public static final Item LECTERN;
   public static final Item TARGET;
   public static final Item LEVER;
   public static final Item LIGHTNING_ROD;
   public static final Item DAYLIGHT_DETECTOR;
   public static final Item SCULK_SENSOR;
   public static final Item CALIBRATED_SCULK_SENSOR;
   public static final Item TRIPWIRE_HOOK;
   public static final Item TRAPPED_CHEST;
   public static final Item TNT;
   public static final Item REDSTONE_LAMP;
   public static final Item NOTE_BLOCK;
   public static final Item STONE_BUTTON;
   public static final Item POLISHED_BLACKSTONE_BUTTON;
   public static final Item OAK_BUTTON;
   public static final Item SPRUCE_BUTTON;
   public static final Item BIRCH_BUTTON;
   public static final Item JUNGLE_BUTTON;
   public static final Item ACACIA_BUTTON;
   public static final Item CHERRY_BUTTON;
   public static final Item DARK_OAK_BUTTON;
   public static final Item PALE_OAK_BUTTON;
   public static final Item MANGROVE_BUTTON;
   public static final Item BAMBOO_BUTTON;
   public static final Item CRIMSON_BUTTON;
   public static final Item WARPED_BUTTON;
   public static final Item STONE_PRESSURE_PLATE;
   public static final Item POLISHED_BLACKSTONE_PRESSURE_PLATE;
   public static final Item LIGHT_WEIGHTED_PRESSURE_PLATE;
   public static final Item HEAVY_WEIGHTED_PRESSURE_PLATE;
   public static final Item OAK_PRESSURE_PLATE;
   public static final Item SPRUCE_PRESSURE_PLATE;
   public static final Item BIRCH_PRESSURE_PLATE;
   public static final Item JUNGLE_PRESSURE_PLATE;
   public static final Item ACACIA_PRESSURE_PLATE;
   public static final Item CHERRY_PRESSURE_PLATE;
   public static final Item DARK_OAK_PRESSURE_PLATE;
   public static final Item PALE_OAK_PRESSURE_PLATE;
   public static final Item MANGROVE_PRESSURE_PLATE;
   public static final Item BAMBOO_PRESSURE_PLATE;
   public static final Item CRIMSON_PRESSURE_PLATE;
   public static final Item WARPED_PRESSURE_PLATE;
   public static final Item IRON_DOOR;
   public static final Item OAK_DOOR;
   public static final Item SPRUCE_DOOR;
   public static final Item BIRCH_DOOR;
   public static final Item JUNGLE_DOOR;
   public static final Item ACACIA_DOOR;
   public static final Item CHERRY_DOOR;
   public static final Item DARK_OAK_DOOR;
   public static final Item PALE_OAK_DOOR;
   public static final Item MANGROVE_DOOR;
   public static final Item BAMBOO_DOOR;
   public static final Item CRIMSON_DOOR;
   public static final Item WARPED_DOOR;
   public static final Item COPPER_DOOR;
   public static final Item EXPOSED_COPPER_DOOR;
   public static final Item WEATHERED_COPPER_DOOR;
   public static final Item OXIDIZED_COPPER_DOOR;
   public static final Item WAXED_COPPER_DOOR;
   public static final Item WAXED_EXPOSED_COPPER_DOOR;
   public static final Item WAXED_WEATHERED_COPPER_DOOR;
   public static final Item WAXED_OXIDIZED_COPPER_DOOR;
   public static final Item IRON_TRAPDOOR;
   public static final Item OAK_TRAPDOOR;
   public static final Item SPRUCE_TRAPDOOR;
   public static final Item BIRCH_TRAPDOOR;
   public static final Item JUNGLE_TRAPDOOR;
   public static final Item ACACIA_TRAPDOOR;
   public static final Item CHERRY_TRAPDOOR;
   public static final Item DARK_OAK_TRAPDOOR;
   public static final Item PALE_OAK_TRAPDOOR;
   public static final Item MANGROVE_TRAPDOOR;
   public static final Item BAMBOO_TRAPDOOR;
   public static final Item CRIMSON_TRAPDOOR;
   public static final Item WARPED_TRAPDOOR;
   public static final Item COPPER_TRAPDOOR;
   public static final Item EXPOSED_COPPER_TRAPDOOR;
   public static final Item WEATHERED_COPPER_TRAPDOOR;
   public static final Item OXIDIZED_COPPER_TRAPDOOR;
   public static final Item WAXED_COPPER_TRAPDOOR;
   public static final Item WAXED_EXPOSED_COPPER_TRAPDOOR;
   public static final Item WAXED_WEATHERED_COPPER_TRAPDOOR;
   public static final Item WAXED_OXIDIZED_COPPER_TRAPDOOR;
   public static final Item OAK_FENCE_GATE;
   public static final Item SPRUCE_FENCE_GATE;
   public static final Item BIRCH_FENCE_GATE;
   public static final Item JUNGLE_FENCE_GATE;
   public static final Item ACACIA_FENCE_GATE;
   public static final Item CHERRY_FENCE_GATE;
   public static final Item DARK_OAK_FENCE_GATE;
   public static final Item PALE_OAK_FENCE_GATE;
   public static final Item MANGROVE_FENCE_GATE;
   public static final Item BAMBOO_FENCE_GATE;
   public static final Item CRIMSON_FENCE_GATE;
   public static final Item WARPED_FENCE_GATE;
   public static final Item POWERED_RAIL;
   public static final Item DETECTOR_RAIL;
   public static final Item RAIL;
   public static final Item ACTIVATOR_RAIL;
   public static final Item SADDLE;
   public static final Item MINECART;
   public static final Item CHEST_MINECART;
   public static final Item FURNACE_MINECART;
   public static final Item TNT_MINECART;
   public static final Item HOPPER_MINECART;
   public static final Item CARROT_ON_A_STICK;
   public static final Item WARPED_FUNGUS_ON_A_STICK;
   public static final Item PHANTOM_MEMBRANE;
   public static final Item ELYTRA;
   public static final Item OAK_BOAT;
   public static final Item OAK_CHEST_BOAT;
   public static final Item SPRUCE_BOAT;
   public static final Item SPRUCE_CHEST_BOAT;
   public static final Item BIRCH_BOAT;
   public static final Item BIRCH_CHEST_BOAT;
   public static final Item JUNGLE_BOAT;
   public static final Item JUNGLE_CHEST_BOAT;
   public static final Item ACACIA_BOAT;
   public static final Item ACACIA_CHEST_BOAT;
   public static final Item CHERRY_BOAT;
   public static final Item CHERRY_CHEST_BOAT;
   public static final Item DARK_OAK_BOAT;
   public static final Item DARK_OAK_CHEST_BOAT;
   public static final Item PALE_OAK_BOAT;
   public static final Item PALE_OAK_CHEST_BOAT;
   public static final Item MANGROVE_BOAT;
   public static final Item MANGROVE_CHEST_BOAT;
   public static final Item BAMBOO_RAFT;
   public static final Item BAMBOO_CHEST_RAFT;
   public static final Item STRUCTURE_BLOCK;
   public static final Item JIGSAW;
   public static final Item TEST_BLOCK;
   public static final Item TEST_INSTANCE_BLOCK;
   public static final Item TURTLE_HELMET;
   public static final Item TURTLE_SCUTE;
   public static final Item ARMADILLO_SCUTE;
   public static final Item WOLF_ARMOR;
   public static final Item FLINT_AND_STEEL;
   public static final Item BOWL;
   public static final Item APPLE;
   public static final Item BOW;
   public static final Item ARROW;
   public static final Item COAL;
   public static final Item CHARCOAL;
   public static final Item DIAMOND;
   public static final Item EMERALD;
   public static final Item LAPIS_LAZULI;
   public static final Item QUARTZ;
   public static final Item AMETHYST_SHARD;
   public static final Item RAW_IRON;
   public static final Item IRON_INGOT;
   public static final Item RAW_COPPER;
   public static final Item COPPER_INGOT;
   public static final Item RAW_GOLD;
   public static final Item GOLD_INGOT;
   public static final Item NETHERITE_INGOT;
   public static final Item NETHERITE_SCRAP;
   public static final Item WOODEN_SWORD;
   public static final Item WOODEN_SHOVEL;
   public static final Item WOODEN_PICKAXE;
   public static final Item WOODEN_AXE;
   public static final Item WOODEN_HOE;
   public static final Item STONE_SWORD;
   public static final Item STONE_SHOVEL;
   public static final Item STONE_PICKAXE;
   public static final Item STONE_AXE;
   public static final Item STONE_HOE;
   public static final Item GOLDEN_SWORD;
   public static final Item GOLDEN_SHOVEL;
   public static final Item GOLDEN_PICKAXE;
   public static final Item GOLDEN_AXE;
   public static final Item GOLDEN_HOE;
   public static final Item IRON_SWORD;
   public static final Item IRON_SHOVEL;
   public static final Item IRON_PICKAXE;
   public static final Item IRON_AXE;
   public static final Item IRON_HOE;
   public static final Item DIAMOND_SWORD;
   public static final Item DIAMOND_SHOVEL;
   public static final Item DIAMOND_PICKAXE;
   public static final Item DIAMOND_AXE;
   public static final Item DIAMOND_HOE;
   public static final Item NETHERITE_SWORD;
   public static final Item NETHERITE_SHOVEL;
   public static final Item NETHERITE_PICKAXE;
   public static final Item NETHERITE_AXE;
   public static final Item NETHERITE_HOE;
   public static final Item STICK;
   public static final Item MUSHROOM_STEW;
   public static final Item STRING;
   public static final Item FEATHER;
   public static final Item GUNPOWDER;
   public static final Item WHEAT_SEEDS;
   public static final Item WHEAT;
   public static final Item BREAD;
   public static final Item LEATHER_HELMET;
   public static final Item LEATHER_CHESTPLATE;
   public static final Item LEATHER_LEGGINGS;
   public static final Item LEATHER_BOOTS;
   public static final Item CHAINMAIL_HELMET;
   public static final Item CHAINMAIL_CHESTPLATE;
   public static final Item CHAINMAIL_LEGGINGS;
   public static final Item CHAINMAIL_BOOTS;
   public static final Item IRON_HELMET;
   public static final Item IRON_CHESTPLATE;
   public static final Item IRON_LEGGINGS;
   public static final Item IRON_BOOTS;
   public static final Item DIAMOND_HELMET;
   public static final Item DIAMOND_CHESTPLATE;
   public static final Item DIAMOND_LEGGINGS;
   public static final Item DIAMOND_BOOTS;
   public static final Item GOLDEN_HELMET;
   public static final Item GOLDEN_CHESTPLATE;
   public static final Item GOLDEN_LEGGINGS;
   public static final Item GOLDEN_BOOTS;
   public static final Item NETHERITE_HELMET;
   public static final Item NETHERITE_CHESTPLATE;
   public static final Item NETHERITE_LEGGINGS;
   public static final Item NETHERITE_BOOTS;
   public static final Item FLINT;
   public static final Item PORKCHOP;
   public static final Item COOKED_PORKCHOP;
   public static final Item PAINTING;
   public static final Item GOLDEN_APPLE;
   public static final Item ENCHANTED_GOLDEN_APPLE;
   public static final Item OAK_SIGN;
   public static final Item SPRUCE_SIGN;
   public static final Item BIRCH_SIGN;
   public static final Item JUNGLE_SIGN;
   public static final Item ACACIA_SIGN;
   public static final Item CHERRY_SIGN;
   public static final Item DARK_OAK_SIGN;
   public static final Item PALE_OAK_SIGN;
   public static final Item MANGROVE_SIGN;
   public static final Item BAMBOO_SIGN;
   public static final Item CRIMSON_SIGN;
   public static final Item WARPED_SIGN;
   public static final Item OAK_HANGING_SIGN;
   public static final Item SPRUCE_HANGING_SIGN;
   public static final Item BIRCH_HANGING_SIGN;
   public static final Item JUNGLE_HANGING_SIGN;
   public static final Item ACACIA_HANGING_SIGN;
   public static final Item CHERRY_HANGING_SIGN;
   public static final Item DARK_OAK_HANGING_SIGN;
   public static final Item PALE_OAK_HANGING_SIGN;
   public static final Item MANGROVE_HANGING_SIGN;
   public static final Item BAMBOO_HANGING_SIGN;
   public static final Item CRIMSON_HANGING_SIGN;
   public static final Item WARPED_HANGING_SIGN;
   public static final Item BUCKET;
   public static final Item WATER_BUCKET;
   public static final Item LAVA_BUCKET;
   public static final Item POWDER_SNOW_BUCKET;
   public static final Item SNOWBALL;
   public static final Item LEATHER;
   public static final Item MILK_BUCKET;
   public static final Item PUFFERFISH_BUCKET;
   public static final Item SALMON_BUCKET;
   public static final Item COD_BUCKET;
   public static final Item TROPICAL_FISH_BUCKET;
   public static final Item AXOLOTL_BUCKET;
   public static final Item TADPOLE_BUCKET;
   public static final Item BRICK;
   public static final Item CLAY_BALL;
   public static final Item DRIED_KELP_BLOCK;
   public static final Item PAPER;
   public static final Item BOOK;
   public static final Item SLIME_BALL;
   public static final Item EGG;
   public static final Item BLUE_EGG;
   public static final Item BROWN_EGG;
   public static final Item COMPASS;
   public static final Item RECOVERY_COMPASS;
   public static final Item BUNDLE;
   public static final Item WHITE_BUNDLE;
   public static final Item ORANGE_BUNDLE;
   public static final Item MAGENTA_BUNDLE;
   public static final Item LIGHT_BLUE_BUNDLE;
   public static final Item YELLOW_BUNDLE;
   public static final Item LIME_BUNDLE;
   public static final Item PINK_BUNDLE;
   public static final Item GRAY_BUNDLE;
   public static final Item LIGHT_GRAY_BUNDLE;
   public static final Item CYAN_BUNDLE;
   public static final Item PURPLE_BUNDLE;
   public static final Item BLUE_BUNDLE;
   public static final Item BROWN_BUNDLE;
   public static final Item GREEN_BUNDLE;
   public static final Item RED_BUNDLE;
   public static final Item BLACK_BUNDLE;
   public static final Item FISHING_ROD;
   public static final Item CLOCK;
   public static final Item SPYGLASS;
   public static final Item GLOWSTONE_DUST;
   public static final Item COD;
   public static final Item SALMON;
   public static final Item TROPICAL_FISH;
   public static final Item PUFFERFISH;
   public static final Item COOKED_COD;
   public static final Item COOKED_SALMON;
   public static final Item INK_SAC;
   public static final Item GLOW_INK_SAC;
   public static final Item COCOA_BEANS;
   public static final Item WHITE_DYE;
   public static final Item ORANGE_DYE;
   public static final Item MAGENTA_DYE;
   public static final Item LIGHT_BLUE_DYE;
   public static final Item YELLOW_DYE;
   public static final Item LIME_DYE;
   public static final Item PINK_DYE;
   public static final Item GRAY_DYE;
   public static final Item LIGHT_GRAY_DYE;
   public static final Item CYAN_DYE;
   public static final Item PURPLE_DYE;
   public static final Item BLUE_DYE;
   public static final Item BROWN_DYE;
   public static final Item GREEN_DYE;
   public static final Item RED_DYE;
   public static final Item BLACK_DYE;
   public static final Item BONE_MEAL;
   public static final Item BONE;
   public static final Item SUGAR;
   public static final Item CAKE;
   public static final Item WHITE_BED;
   public static final Item ORANGE_BED;
   public static final Item MAGENTA_BED;
   public static final Item LIGHT_BLUE_BED;
   public static final Item YELLOW_BED;
   public static final Item LIME_BED;
   public static final Item PINK_BED;
   public static final Item GRAY_BED;
   public static final Item LIGHT_GRAY_BED;
   public static final Item CYAN_BED;
   public static final Item PURPLE_BED;
   public static final Item BLUE_BED;
   public static final Item BROWN_BED;
   public static final Item GREEN_BED;
   public static final Item RED_BED;
   public static final Item BLACK_BED;
   public static final Item COOKIE;
   public static final Item CRAFTER;
   public static final Item FILLED_MAP;
   public static final Item SHEARS;
   public static final Item MELON_SLICE;
   public static final Item DRIED_KELP;
   public static final Item PUMPKIN_SEEDS;
   public static final Item MELON_SEEDS;
   public static final Item BEEF;
   public static final Item COOKED_BEEF;
   public static final Item CHICKEN;
   public static final Item COOKED_CHICKEN;
   public static final Item ROTTEN_FLESH;
   public static final Item ENDER_PEARL;
   public static final Item BLAZE_ROD;
   public static final Item GHAST_TEAR;
   public static final Item GOLD_NUGGET;
   public static final Item NETHER_WART;
   public static final Item GLASS_BOTTLE;
   public static final Item POTION;
   public static final Item SPIDER_EYE;
   public static final Item FERMENTED_SPIDER_EYE;
   public static final Item BLAZE_POWDER;
   public static final Item MAGMA_CREAM;
   public static final Item BREWING_STAND;
   public static final Item CAULDRON;
   public static final Item ENDER_EYE;
   public static final Item GLISTERING_MELON_SLICE;
   public static final Item ARMADILLO_SPAWN_EGG;
   public static final Item ALLAY_SPAWN_EGG;
   public static final Item AXOLOTL_SPAWN_EGG;
   public static final Item BAT_SPAWN_EGG;
   public static final Item BEE_SPAWN_EGG;
   public static final Item BLAZE_SPAWN_EGG;
   public static final Item BOGGED_SPAWN_EGG;
   public static final Item BREEZE_SPAWN_EGG;
   public static final Item CAT_SPAWN_EGG;
   public static final Item CAMEL_SPAWN_EGG;
   public static final Item CAVE_SPIDER_SPAWN_EGG;
   public static final Item CHICKEN_SPAWN_EGG;
   public static final Item COD_SPAWN_EGG;
   public static final Item COW_SPAWN_EGG;
   public static final Item CREEPER_SPAWN_EGG;
   public static final Item DOLPHIN_SPAWN_EGG;
   public static final Item DONKEY_SPAWN_EGG;
   public static final Item DROWNED_SPAWN_EGG;
   public static final Item ELDER_GUARDIAN_SPAWN_EGG;
   public static final Item ENDER_DRAGON_SPAWN_EGG;
   public static final Item ENDERMAN_SPAWN_EGG;
   public static final Item ENDERMITE_SPAWN_EGG;
   public static final Item EVOKER_SPAWN_EGG;
   public static final Item FOX_SPAWN_EGG;
   public static final Item FROG_SPAWN_EGG;
   public static final Item GHAST_SPAWN_EGG;
   public static final Item GLOW_SQUID_SPAWN_EGG;
   public static final Item GOAT_SPAWN_EGG;
   public static final Item GUARDIAN_SPAWN_EGG;
   public static final Item HOGLIN_SPAWN_EGG;
   public static final Item HORSE_SPAWN_EGG;
   public static final Item HUSK_SPAWN_EGG;
   public static final Item IRON_GOLEM_SPAWN_EGG;
   public static final Item LLAMA_SPAWN_EGG;
   public static final Item MAGMA_CUBE_SPAWN_EGG;
   public static final Item MOOSHROOM_SPAWN_EGG;
   public static final Item MULE_SPAWN_EGG;
   public static final Item OCELOT_SPAWN_EGG;
   public static final Item PANDA_SPAWN_EGG;
   public static final Item PARROT_SPAWN_EGG;
   public static final Item PHANTOM_SPAWN_EGG;
   public static final Item PIG_SPAWN_EGG;
   public static final Item PIGLIN_SPAWN_EGG;
   public static final Item PIGLIN_BRUTE_SPAWN_EGG;
   public static final Item PILLAGER_SPAWN_EGG;
   public static final Item POLAR_BEAR_SPAWN_EGG;
   public static final Item PUFFERFISH_SPAWN_EGG;
   public static final Item RABBIT_SPAWN_EGG;
   public static final Item RAVAGER_SPAWN_EGG;
   public static final Item SALMON_SPAWN_EGG;
   public static final Item SHEEP_SPAWN_EGG;
   public static final Item SHULKER_SPAWN_EGG;
   public static final Item SILVERFISH_SPAWN_EGG;
   public static final Item SKELETON_SPAWN_EGG;
   public static final Item SKELETON_HORSE_SPAWN_EGG;
   public static final Item SLIME_SPAWN_EGG;
   public static final Item SNIFFER_SPAWN_EGG;
   public static final Item SNOW_GOLEM_SPAWN_EGG;
   public static final Item SPIDER_SPAWN_EGG;
   public static final Item SQUID_SPAWN_EGG;
   public static final Item STRAY_SPAWN_EGG;
   public static final Item STRIDER_SPAWN_EGG;
   public static final Item TADPOLE_SPAWN_EGG;
   public static final Item TRADER_LLAMA_SPAWN_EGG;
   public static final Item TROPICAL_FISH_SPAWN_EGG;
   public static final Item TURTLE_SPAWN_EGG;
   public static final Item VEX_SPAWN_EGG;
   public static final Item VILLAGER_SPAWN_EGG;
   public static final Item VINDICATOR_SPAWN_EGG;
   public static final Item WANDERING_TRADER_SPAWN_EGG;
   public static final Item WARDEN_SPAWN_EGG;
   public static final Item WITCH_SPAWN_EGG;
   public static final Item WITHER_SPAWN_EGG;
   public static final Item WITHER_SKELETON_SPAWN_EGG;
   public static final Item WOLF_SPAWN_EGG;
   public static final Item ZOGLIN_SPAWN_EGG;
   public static final Item CREAKING_SPAWN_EGG;
   public static final Item ZOMBIE_SPAWN_EGG;
   public static final Item ZOMBIE_HORSE_SPAWN_EGG;
   public static final Item ZOMBIE_VILLAGER_SPAWN_EGG;
   public static final Item ZOMBIFIED_PIGLIN_SPAWN_EGG;
   public static final Item EXPERIENCE_BOTTLE;
   public static final Item FIRE_CHARGE;
   public static final Item WIND_CHARGE;
   public static final Item WRITABLE_BOOK;
   public static final Item WRITTEN_BOOK;
   public static final Item BREEZE_ROD;
   public static final Item MACE;
   public static final Item ITEM_FRAME;
   public static final Item GLOW_ITEM_FRAME;
   public static final Item FLOWER_POT;
   public static final Item CARROT;
   public static final Item POTATO;
   public static final Item BAKED_POTATO;
   public static final Item POISONOUS_POTATO;
   public static final Item MAP;
   public static final Item GOLDEN_CARROT;
   public static final Item SKELETON_SKULL;
   public static final Item WITHER_SKELETON_SKULL;
   public static final Item PLAYER_HEAD;
   public static final Item ZOMBIE_HEAD;
   public static final Item CREEPER_HEAD;
   public static final Item DRAGON_HEAD;
   public static final Item PIGLIN_HEAD;
   public static final Item NETHER_STAR;
   public static final Item PUMPKIN_PIE;
   public static final Item FIREWORK_ROCKET;
   public static final Item FIREWORK_STAR;
   public static final Item ENCHANTED_BOOK;
   public static final Item NETHER_BRICK;
   public static final Item RESIN_BRICK;
   public static final Item PRISMARINE_SHARD;
   public static final Item PRISMARINE_CRYSTALS;
   public static final Item RABBIT;
   public static final Item COOKED_RABBIT;
   public static final Item RABBIT_STEW;
   public static final Item RABBIT_FOOT;
   public static final Item RABBIT_HIDE;
   public static final Item ARMOR_STAND;
   public static final Item IRON_HORSE_ARMOR;
   public static final Item GOLDEN_HORSE_ARMOR;
   public static final Item DIAMOND_HORSE_ARMOR;
   public static final Item LEATHER_HORSE_ARMOR;
   public static final Item LEAD;
   public static final Item NAME_TAG;
   public static final Item COMMAND_BLOCK_MINECART;
   public static final Item MUTTON;
   public static final Item COOKED_MUTTON;
   public static final Item WHITE_BANNER;
   public static final Item ORANGE_BANNER;
   public static final Item MAGENTA_BANNER;
   public static final Item LIGHT_BLUE_BANNER;
   public static final Item YELLOW_BANNER;
   public static final Item LIME_BANNER;
   public static final Item PINK_BANNER;
   public static final Item GRAY_BANNER;
   public static final Item LIGHT_GRAY_BANNER;
   public static final Item CYAN_BANNER;
   public static final Item PURPLE_BANNER;
   public static final Item BLUE_BANNER;
   public static final Item BROWN_BANNER;
   public static final Item GREEN_BANNER;
   public static final Item RED_BANNER;
   public static final Item BLACK_BANNER;
   public static final Item END_CRYSTAL;
   public static final Item CHORUS_FRUIT;
   public static final Item POPPED_CHORUS_FRUIT;
   public static final Item TORCHFLOWER_SEEDS;
   public static final Item PITCHER_POD;
   public static final Item BEETROOT;
   public static final Item BEETROOT_SEEDS;
   public static final Item BEETROOT_SOUP;
   public static final Item DRAGON_BREATH;
   public static final Item SPLASH_POTION;
   public static final Item SPECTRAL_ARROW;
   public static final Item TIPPED_ARROW;
   public static final Item LINGERING_POTION;
   public static final Item SHIELD;
   public static final Item TOTEM_OF_UNDYING;
   public static final Item SHULKER_SHELL;
   public static final Item IRON_NUGGET;
   public static final Item KNOWLEDGE_BOOK;
   public static final Item DEBUG_STICK;
   public static final Item MUSIC_DISC_13;
   public static final Item MUSIC_DISC_CAT;
   public static final Item MUSIC_DISC_BLOCKS;
   public static final Item MUSIC_DISC_CHIRP;
   public static final Item MUSIC_DISC_CREATOR;
   public static final Item MUSIC_DISC_CREATOR_MUSIC_BOX;
   public static final Item MUSIC_DISC_FAR;
   public static final Item MUSIC_DISC_MALL;
   public static final Item MUSIC_DISC_MELLOHI;
   public static final Item MUSIC_DISC_STAL;
   public static final Item MUSIC_DISC_STRAD;
   public static final Item MUSIC_DISC_WARD;
   public static final Item MUSIC_DISC_11;
   public static final Item MUSIC_DISC_WAIT;
   public static final Item MUSIC_DISC_OTHERSIDE;
   public static final Item MUSIC_DISC_RELIC;
   public static final Item MUSIC_DISC_5;
   public static final Item MUSIC_DISC_PIGSTEP;
   public static final Item MUSIC_DISC_PRECIPICE;
   public static final Item DISC_FRAGMENT_5;
   public static final Item TRIDENT;
   public static final Item NAUTILUS_SHELL;
   public static final Item HEART_OF_THE_SEA;
   public static final Item CROSSBOW;
   public static final Item SUSPICIOUS_STEW;
   public static final Item LOOM;
   public static final Item FLOWER_BANNER_PATTERN;
   public static final Item CREEPER_BANNER_PATTERN;
   public static final Item SKULL_BANNER_PATTERN;
   public static final Item MOJANG_BANNER_PATTERN;
   public static final Item GLOBE_BANNER_PATTERN;
   public static final Item PIGLIN_BANNER_PATTERN;
   public static final Item FLOW_BANNER_PATTERN;
   public static final Item GUSTER_BANNER_PATTERN;
   public static final Item FIELD_MASONED_BANNER_PATTERN;
   public static final Item BORDURE_INDENTED_BANNER_PATTERN;
   public static final Item GOAT_HORN;
   public static final Item COMPOSTER;
   public static final Item BARREL;
   public static final Item SMOKER;
   public static final Item BLAST_FURNACE;
   public static final Item CARTOGRAPHY_TABLE;
   public static final Item FLETCHING_TABLE;
   public static final Item GRINDSTONE;
   public static final Item SMITHING_TABLE;
   public static final Item STONECUTTER;
   public static final Item BELL;
   public static final Item LANTERN;
   public static final Item SOUL_LANTERN;
   public static final Item SWEET_BERRIES;
   public static final Item GLOW_BERRIES;
   public static final Item CAMPFIRE;
   public static final Item SOUL_CAMPFIRE;
   public static final Item SHROOMLIGHT;
   public static final Item HONEYCOMB;
   public static final Item BEE_NEST;
   public static final Item BEEHIVE;
   public static final Item HONEY_BOTTLE;
   public static final Item HONEYCOMB_BLOCK;
   public static final Item LODESTONE;
   public static final Item CRYING_OBSIDIAN;
   public static final Item BLACKSTONE;
   public static final Item BLACKSTONE_SLAB;
   public static final Item BLACKSTONE_STAIRS;
   public static final Item GILDED_BLACKSTONE;
   public static final Item POLISHED_BLACKSTONE;
   public static final Item POLISHED_BLACKSTONE_SLAB;
   public static final Item POLISHED_BLACKSTONE_STAIRS;
   public static final Item CHISELED_POLISHED_BLACKSTONE;
   public static final Item POLISHED_BLACKSTONE_BRICKS;
   public static final Item POLISHED_BLACKSTONE_BRICK_SLAB;
   public static final Item POLISHED_BLACKSTONE_BRICK_STAIRS;
   public static final Item CRACKED_POLISHED_BLACKSTONE_BRICKS;
   public static final Item RESPAWN_ANCHOR;
   public static final Item CANDLE;
   public static final Item WHITE_CANDLE;
   public static final Item ORANGE_CANDLE;
   public static final Item MAGENTA_CANDLE;
   public static final Item LIGHT_BLUE_CANDLE;
   public static final Item YELLOW_CANDLE;
   public static final Item LIME_CANDLE;
   public static final Item PINK_CANDLE;
   public static final Item GRAY_CANDLE;
   public static final Item LIGHT_GRAY_CANDLE;
   public static final Item CYAN_CANDLE;
   public static final Item PURPLE_CANDLE;
   public static final Item BLUE_CANDLE;
   public static final Item BROWN_CANDLE;
   public static final Item GREEN_CANDLE;
   public static final Item RED_CANDLE;
   public static final Item BLACK_CANDLE;
   public static final Item SMALL_AMETHYST_BUD;
   public static final Item MEDIUM_AMETHYST_BUD;
   public static final Item LARGE_AMETHYST_BUD;
   public static final Item AMETHYST_CLUSTER;
   public static final Item POINTED_DRIPSTONE;
   public static final Item OCHRE_FROGLIGHT;
   public static final Item VERDANT_FROGLIGHT;
   public static final Item PEARLESCENT_FROGLIGHT;
   public static final Item FROGSPAWN;
   public static final Item ECHO_SHARD;
   public static final Item BRUSH;
   public static final Item NETHERITE_UPGRADE_SMITHING_TEMPLATE;
   public static final Item SENTRY_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item DUNE_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item COAST_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item WILD_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item WARD_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item EYE_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item VEX_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item TIDE_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item SNOUT_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item RIB_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item SPIRE_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item WAYFINDER_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item SHAPER_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item SILENCE_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item RAISER_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item HOST_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item FLOW_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item BOLT_ARMOR_TRIM_SMITHING_TEMPLATE;
   public static final Item ANGLER_POTTERY_SHERD;
   public static final Item ARCHER_POTTERY_SHERD;
   public static final Item ARMS_UP_POTTERY_SHERD;
   public static final Item BLADE_POTTERY_SHERD;
   public static final Item BREWER_POTTERY_SHERD;
   public static final Item BURN_POTTERY_SHERD;
   public static final Item DANGER_POTTERY_SHERD;
   public static final Item EXPLORER_POTTERY_SHERD;
   public static final Item FLOW_POTTERY_SHERD;
   public static final Item FRIEND_POTTERY_SHERD;
   public static final Item GUSTER_POTTERY_SHERD;
   public static final Item HEART_POTTERY_SHERD;
   public static final Item HEARTBREAK_POTTERY_SHERD;
   public static final Item HOWL_POTTERY_SHERD;
   public static final Item MINER_POTTERY_SHERD;
   public static final Item MOURNER_POTTERY_SHERD;
   public static final Item PLENTY_POTTERY_SHERD;
   public static final Item PRIZE_POTTERY_SHERD;
   public static final Item SCRAPE_POTTERY_SHERD;
   public static final Item SHEAF_POTTERY_SHERD;
   public static final Item SHELTER_POTTERY_SHERD;
   public static final Item SKULL_POTTERY_SHERD;
   public static final Item SNORT_POTTERY_SHERD;
   public static final Item COPPER_GRATE;
   public static final Item EXPOSED_COPPER_GRATE;
   public static final Item WEATHERED_COPPER_GRATE;
   public static final Item OXIDIZED_COPPER_GRATE;
   public static final Item WAXED_COPPER_GRATE;
   public static final Item WAXED_EXPOSED_COPPER_GRATE;
   public static final Item WAXED_WEATHERED_COPPER_GRATE;
   public static final Item WAXED_OXIDIZED_COPPER_GRATE;
   public static final Item COPPER_BULB;
   public static final Item EXPOSED_COPPER_BULB;
   public static final Item WEATHERED_COPPER_BULB;
   public static final Item OXIDIZED_COPPER_BULB;
   public static final Item WAXED_COPPER_BULB;
   public static final Item WAXED_EXPOSED_COPPER_BULB;
   public static final Item WAXED_WEATHERED_COPPER_BULB;
   public static final Item WAXED_OXIDIZED_COPPER_BULB;
   public static final Item TRIAL_SPAWNER;
   public static final Item TRIAL_KEY;
   public static final Item OMINOUS_TRIAL_KEY;
   public static final Item VAULT;
   public static final Item OMINOUS_BOTTLE;

   private static Function<Item.Settings, Item> createBlockItemWithUniqueName(Block block) {
      return (settings) -> new BlockItem(block, settings.useItemPrefixedTranslationKey());
   }

   private static RegistryKey<Item> keyOf(String id) {
      return RegistryKey.of(RegistryKeys.ITEM, Identifier.ofVanilla(id));
   }

   private static RegistryKey<Item> keyOf(RegistryKey<Block> blockKey) {
      return RegistryKey.of(RegistryKeys.ITEM, blockKey.getValue());
   }

   public static Item register(Block block) {
      return register(block, BlockItem::new);
   }

   public static Item register(Block block, Item.Settings settings) {
      return register(block, BlockItem::new, settings);
   }

   public static Item register(Block block, UnaryOperator<Item.Settings> settingsOperator) {
      return register((Block)block, (BiFunction)((blockx, settings) -> new BlockItem(blockx, (Item.Settings)settingsOperator.apply(settings))));
   }

   public static Item register(Block block, Block... blocks) {
      Item item = register(block);

      for(Block block2 : blocks) {
         Item.BLOCK_ITEMS.put(block2, item);
      }

      return item;
   }

   public static Item register(Block block, BiFunction<Block, Item.Settings, Item> factory) {
      return register(block, factory, new Item.Settings());
   }

   public static Item register(Block block, BiFunction<Block, Item.Settings, Item> factory, Item.Settings settings) {
      return register((RegistryKey)keyOf(block.getRegistryEntry().registryKey()), (Function)((itemSettings) -> (Item)factory.apply(block, itemSettings)), settings.useBlockPrefixedTranslationKey());
   }

   public static Item register(String id, Function<Item.Settings, Item> factory) {
      return register(keyOf(id), factory, new Item.Settings());
   }

   public static Item register(String id, Function<Item.Settings, Item> factory, Item.Settings settings) {
      return register(keyOf(id), factory, settings);
   }

   public static Item register(String id, Item.Settings settings) {
      return register(keyOf(id), Item::new, settings);
   }

   public static Item register(String id) {
      return register(keyOf(id), Item::new, new Item.Settings());
   }

   public static Item register(RegistryKey<Item> key, Function<Item.Settings, Item> factory) {
      return register(key, factory, new Item.Settings());
   }

   public static Item register(RegistryKey<Item> key, Function<Item.Settings, Item> factory, Item.Settings settings) {
      Item item = (Item)factory.apply(settings.registryKey(key));
      if (item instanceof BlockItem blockItem) {
         blockItem.appendBlocks(Item.BLOCK_ITEMS, item);
      }

      return (Item)Registry.register(Registries.ITEM, (RegistryKey)key, item);
   }

   static {
      AIR = register(Blocks.AIR, AirBlockItem::new);
      STONE = register(Blocks.STONE);
      GRANITE = register(Blocks.GRANITE);
      POLISHED_GRANITE = register(Blocks.POLISHED_GRANITE);
      DIORITE = register(Blocks.DIORITE);
      POLISHED_DIORITE = register(Blocks.POLISHED_DIORITE);
      ANDESITE = register(Blocks.ANDESITE);
      POLISHED_ANDESITE = register(Blocks.POLISHED_ANDESITE);
      DEEPSLATE = register(Blocks.DEEPSLATE);
      COBBLED_DEEPSLATE = register(Blocks.COBBLED_DEEPSLATE);
      POLISHED_DEEPSLATE = register(Blocks.POLISHED_DEEPSLATE);
      CALCITE = register(Blocks.CALCITE);
      TUFF = register(Blocks.TUFF);
      TUFF_SLAB = register(Blocks.TUFF_SLAB);
      TUFF_STAIRS = register(Blocks.TUFF_STAIRS);
      TUFF_WALL = register(Blocks.TUFF_WALL);
      CHISELED_TUFF = register(Blocks.CHISELED_TUFF);
      POLISHED_TUFF = register(Blocks.POLISHED_TUFF);
      POLISHED_TUFF_SLAB = register(Blocks.POLISHED_TUFF_SLAB);
      POLISHED_TUFF_STAIRS = register(Blocks.POLISHED_TUFF_STAIRS);
      POLISHED_TUFF_WALL = register(Blocks.POLISHED_TUFF_WALL);
      TUFF_BRICKS = register(Blocks.TUFF_BRICKS);
      TUFF_BRICK_SLAB = register(Blocks.TUFF_BRICK_SLAB);
      TUFF_BRICK_STAIRS = register(Blocks.TUFF_BRICK_STAIRS);
      TUFF_BRICK_WALL = register(Blocks.TUFF_BRICK_WALL);
      CHISELED_TUFF_BRICKS = register(Blocks.CHISELED_TUFF_BRICKS);
      DRIPSTONE_BLOCK = register(Blocks.DRIPSTONE_BLOCK);
      GRASS_BLOCK = register(Blocks.GRASS_BLOCK);
      DIRT = register(Blocks.DIRT);
      COARSE_DIRT = register(Blocks.COARSE_DIRT);
      PODZOL = register(Blocks.PODZOL);
      ROOTED_DIRT = register(Blocks.ROOTED_DIRT);
      MUD = register(Blocks.MUD);
      CRIMSON_NYLIUM = register(Blocks.CRIMSON_NYLIUM);
      WARPED_NYLIUM = register(Blocks.WARPED_NYLIUM);
      COBBLESTONE = register(Blocks.COBBLESTONE);
      OAK_PLANKS = register(Blocks.OAK_PLANKS);
      SPRUCE_PLANKS = register(Blocks.SPRUCE_PLANKS);
      BIRCH_PLANKS = register(Blocks.BIRCH_PLANKS);
      JUNGLE_PLANKS = register(Blocks.JUNGLE_PLANKS);
      ACACIA_PLANKS = register(Blocks.ACACIA_PLANKS);
      CHERRY_PLANKS = register(Blocks.CHERRY_PLANKS);
      DARK_OAK_PLANKS = register(Blocks.DARK_OAK_PLANKS);
      PALE_OAK_PLANKS = register(Blocks.PALE_OAK_PLANKS);
      MANGROVE_PLANKS = register(Blocks.MANGROVE_PLANKS);
      BAMBOO_PLANKS = register(Blocks.BAMBOO_PLANKS);
      CRIMSON_PLANKS = register(Blocks.CRIMSON_PLANKS);
      WARPED_PLANKS = register(Blocks.WARPED_PLANKS);
      BAMBOO_MOSAIC = register(Blocks.BAMBOO_MOSAIC);
      OAK_SAPLING = register(Blocks.OAK_SAPLING);
      SPRUCE_SAPLING = register(Blocks.SPRUCE_SAPLING);
      BIRCH_SAPLING = register(Blocks.BIRCH_SAPLING);
      JUNGLE_SAPLING = register(Blocks.JUNGLE_SAPLING);
      ACACIA_SAPLING = register(Blocks.ACACIA_SAPLING);
      CHERRY_SAPLING = register(Blocks.CHERRY_SAPLING);
      DARK_OAK_SAPLING = register(Blocks.DARK_OAK_SAPLING);
      PALE_OAK_SAPLING = register(Blocks.PALE_OAK_SAPLING);
      MANGROVE_PROPAGULE = register(Blocks.MANGROVE_PROPAGULE);
      BEDROCK = register(Blocks.BEDROCK);
      SAND = register(Blocks.SAND);
      SUSPICIOUS_SAND = register(Blocks.SUSPICIOUS_SAND);
      SUSPICIOUS_GRAVEL = register(Blocks.SUSPICIOUS_GRAVEL);
      RED_SAND = register(Blocks.RED_SAND);
      GRAVEL = register(Blocks.GRAVEL);
      COAL_ORE = register(Blocks.COAL_ORE);
      DEEPSLATE_COAL_ORE = register(Blocks.DEEPSLATE_COAL_ORE);
      IRON_ORE = register(Blocks.IRON_ORE);
      DEEPSLATE_IRON_ORE = register(Blocks.DEEPSLATE_IRON_ORE);
      COPPER_ORE = register(Blocks.COPPER_ORE);
      DEEPSLATE_COPPER_ORE = register(Blocks.DEEPSLATE_COPPER_ORE);
      GOLD_ORE = register(Blocks.GOLD_ORE);
      DEEPSLATE_GOLD_ORE = register(Blocks.DEEPSLATE_GOLD_ORE);
      REDSTONE_ORE = register(Blocks.REDSTONE_ORE);
      DEEPSLATE_REDSTONE_ORE = register(Blocks.DEEPSLATE_REDSTONE_ORE);
      EMERALD_ORE = register(Blocks.EMERALD_ORE);
      DEEPSLATE_EMERALD_ORE = register(Blocks.DEEPSLATE_EMERALD_ORE);
      LAPIS_ORE = register(Blocks.LAPIS_ORE);
      DEEPSLATE_LAPIS_ORE = register(Blocks.DEEPSLATE_LAPIS_ORE);
      DIAMOND_ORE = register(Blocks.DIAMOND_ORE);
      DEEPSLATE_DIAMOND_ORE = register(Blocks.DEEPSLATE_DIAMOND_ORE);
      NETHER_GOLD_ORE = register(Blocks.NETHER_GOLD_ORE);
      NETHER_QUARTZ_ORE = register(Blocks.NETHER_QUARTZ_ORE);
      ANCIENT_DEBRIS = register(Blocks.ANCIENT_DEBRIS, (new Item.Settings()).fireproof());
      COAL_BLOCK = register(Blocks.COAL_BLOCK);
      RAW_IRON_BLOCK = register(Blocks.RAW_IRON_BLOCK);
      RAW_COPPER_BLOCK = register(Blocks.RAW_COPPER_BLOCK);
      RAW_GOLD_BLOCK = register(Blocks.RAW_GOLD_BLOCK);
      HEAVY_CORE = register(Blocks.HEAVY_CORE, (new Item.Settings()).rarity(Rarity.EPIC));
      AMETHYST_BLOCK = register(Blocks.AMETHYST_BLOCK);
      BUDDING_AMETHYST = register(Blocks.BUDDING_AMETHYST);
      IRON_BLOCK = register(Blocks.IRON_BLOCK);
      COPPER_BLOCK = register(Blocks.COPPER_BLOCK);
      GOLD_BLOCK = register(Blocks.GOLD_BLOCK);
      DIAMOND_BLOCK = register(Blocks.DIAMOND_BLOCK);
      NETHERITE_BLOCK = register(Blocks.NETHERITE_BLOCK, (new Item.Settings()).fireproof());
      EXPOSED_COPPER = register(Blocks.EXPOSED_COPPER);
      WEATHERED_COPPER = register(Blocks.WEATHERED_COPPER);
      OXIDIZED_COPPER = register(Blocks.OXIDIZED_COPPER);
      CHISELED_COPPER = register(Blocks.CHISELED_COPPER);
      EXPOSED_CHISELED_COPPER = register(Blocks.EXPOSED_CHISELED_COPPER);
      WEATHERED_CHISELED_COPPER = register(Blocks.WEATHERED_CHISELED_COPPER);
      OXIDIZED_CHISELED_COPPER = register(Blocks.OXIDIZED_CHISELED_COPPER);
      CUT_COPPER = register(Blocks.CUT_COPPER);
      EXPOSED_CUT_COPPER = register(Blocks.EXPOSED_CUT_COPPER);
      WEATHERED_CUT_COPPER = register(Blocks.WEATHERED_CUT_COPPER);
      OXIDIZED_CUT_COPPER = register(Blocks.OXIDIZED_CUT_COPPER);
      CUT_COPPER_STAIRS = register(Blocks.CUT_COPPER_STAIRS);
      EXPOSED_CUT_COPPER_STAIRS = register(Blocks.EXPOSED_CUT_COPPER_STAIRS);
      WEATHERED_CUT_COPPER_STAIRS = register(Blocks.WEATHERED_CUT_COPPER_STAIRS);
      OXIDIZED_CUT_COPPER_STAIRS = register(Blocks.OXIDIZED_CUT_COPPER_STAIRS);
      CUT_COPPER_SLAB = register(Blocks.CUT_COPPER_SLAB);
      EXPOSED_CUT_COPPER_SLAB = register(Blocks.EXPOSED_CUT_COPPER_SLAB);
      WEATHERED_CUT_COPPER_SLAB = register(Blocks.WEATHERED_CUT_COPPER_SLAB);
      OXIDIZED_CUT_COPPER_SLAB = register(Blocks.OXIDIZED_CUT_COPPER_SLAB);
      WAXED_COPPER_BLOCK = register(Blocks.WAXED_COPPER_BLOCK);
      WAXED_EXPOSED_COPPER = register(Blocks.WAXED_EXPOSED_COPPER);
      WAXED_WEATHERED_COPPER = register(Blocks.WAXED_WEATHERED_COPPER);
      WAXED_OXIDIZED_COPPER = register(Blocks.WAXED_OXIDIZED_COPPER);
      WAXED_CHISELED_COPPER = register(Blocks.WAXED_CHISELED_COPPER);
      WAXED_EXPOSED_CHISELED_COPPER = register(Blocks.WAXED_EXPOSED_CHISELED_COPPER);
      WAXED_WEATHERED_CHISELED_COPPER = register(Blocks.WAXED_WEATHERED_CHISELED_COPPER);
      WAXED_OXIDIZED_CHISELED_COPPER = register(Blocks.WAXED_OXIDIZED_CHISELED_COPPER);
      WAXED_CUT_COPPER = register(Blocks.WAXED_CUT_COPPER);
      WAXED_EXPOSED_CUT_COPPER = register(Blocks.WAXED_EXPOSED_CUT_COPPER);
      WAXED_WEATHERED_CUT_COPPER = register(Blocks.WAXED_WEATHERED_CUT_COPPER);
      WAXED_OXIDIZED_CUT_COPPER = register(Blocks.WAXED_OXIDIZED_CUT_COPPER);
      WAXED_CUT_COPPER_STAIRS = register(Blocks.WAXED_CUT_COPPER_STAIRS);
      WAXED_EXPOSED_CUT_COPPER_STAIRS = register(Blocks.WAXED_EXPOSED_CUT_COPPER_STAIRS);
      WAXED_WEATHERED_CUT_COPPER_STAIRS = register(Blocks.WAXED_WEATHERED_CUT_COPPER_STAIRS);
      WAXED_OXIDIZED_CUT_COPPER_STAIRS = register(Blocks.WAXED_OXIDIZED_CUT_COPPER_STAIRS);
      WAXED_CUT_COPPER_SLAB = register(Blocks.WAXED_CUT_COPPER_SLAB);
      WAXED_EXPOSED_CUT_COPPER_SLAB = register(Blocks.WAXED_EXPOSED_CUT_COPPER_SLAB);
      WAXED_WEATHERED_CUT_COPPER_SLAB = register(Blocks.WAXED_WEATHERED_CUT_COPPER_SLAB);
      WAXED_OXIDIZED_CUT_COPPER_SLAB = register(Blocks.WAXED_OXIDIZED_CUT_COPPER_SLAB);
      OAK_LOG = register(Blocks.OAK_LOG);
      SPRUCE_LOG = register(Blocks.SPRUCE_LOG);
      BIRCH_LOG = register(Blocks.BIRCH_LOG);
      JUNGLE_LOG = register(Blocks.JUNGLE_LOG);
      ACACIA_LOG = register(Blocks.ACACIA_LOG);
      CHERRY_LOG = register(Blocks.CHERRY_LOG);
      PALE_OAK_LOG = register(Blocks.PALE_OAK_LOG);
      DARK_OAK_LOG = register(Blocks.DARK_OAK_LOG);
      MANGROVE_LOG = register(Blocks.MANGROVE_LOG);
      MANGROVE_ROOTS = register(Blocks.MANGROVE_ROOTS);
      MUDDY_MANGROVE_ROOTS = register(Blocks.MUDDY_MANGROVE_ROOTS);
      CRIMSON_STEM = register(Blocks.CRIMSON_STEM);
      WARPED_STEM = register(Blocks.WARPED_STEM);
      BAMBOO_BLOCK = register(Blocks.BAMBOO_BLOCK);
      STRIPPED_OAK_LOG = register(Blocks.STRIPPED_OAK_LOG);
      STRIPPED_SPRUCE_LOG = register(Blocks.STRIPPED_SPRUCE_LOG);
      STRIPPED_BIRCH_LOG = register(Blocks.STRIPPED_BIRCH_LOG);
      STRIPPED_JUNGLE_LOG = register(Blocks.STRIPPED_JUNGLE_LOG);
      STRIPPED_ACACIA_LOG = register(Blocks.STRIPPED_ACACIA_LOG);
      STRIPPED_CHERRY_LOG = register(Blocks.STRIPPED_CHERRY_LOG);
      STRIPPED_DARK_OAK_LOG = register(Blocks.STRIPPED_DARK_OAK_LOG);
      STRIPPED_PALE_OAK_LOG = register(Blocks.STRIPPED_PALE_OAK_LOG);
      STRIPPED_MANGROVE_LOG = register(Blocks.STRIPPED_MANGROVE_LOG);
      STRIPPED_CRIMSON_STEM = register(Blocks.STRIPPED_CRIMSON_STEM);
      STRIPPED_WARPED_STEM = register(Blocks.STRIPPED_WARPED_STEM);
      STRIPPED_OAK_WOOD = register(Blocks.STRIPPED_OAK_WOOD);
      STRIPPED_SPRUCE_WOOD = register(Blocks.STRIPPED_SPRUCE_WOOD);
      STRIPPED_BIRCH_WOOD = register(Blocks.STRIPPED_BIRCH_WOOD);
      STRIPPED_JUNGLE_WOOD = register(Blocks.STRIPPED_JUNGLE_WOOD);
      STRIPPED_ACACIA_WOOD = register(Blocks.STRIPPED_ACACIA_WOOD);
      STRIPPED_CHERRY_WOOD = register(Blocks.STRIPPED_CHERRY_WOOD);
      STRIPPED_DARK_OAK_WOOD = register(Blocks.STRIPPED_DARK_OAK_WOOD);
      STRIPPED_PALE_OAK_WOOD = register(Blocks.STRIPPED_PALE_OAK_WOOD);
      STRIPPED_MANGROVE_WOOD = register(Blocks.STRIPPED_MANGROVE_WOOD);
      STRIPPED_CRIMSON_HYPHAE = register(Blocks.STRIPPED_CRIMSON_HYPHAE);
      STRIPPED_WARPED_HYPHAE = register(Blocks.STRIPPED_WARPED_HYPHAE);
      STRIPPED_BAMBOO_BLOCK = register(Blocks.STRIPPED_BAMBOO_BLOCK);
      OAK_WOOD = register(Blocks.OAK_WOOD);
      SPRUCE_WOOD = register(Blocks.SPRUCE_WOOD);
      BIRCH_WOOD = register(Blocks.BIRCH_WOOD);
      JUNGLE_WOOD = register(Blocks.JUNGLE_WOOD);
      ACACIA_WOOD = register(Blocks.ACACIA_WOOD);
      CHERRY_WOOD = register(Blocks.CHERRY_WOOD);
      PALE_OAK_WOOD = register(Blocks.PALE_OAK_WOOD);
      DARK_OAK_WOOD = register(Blocks.DARK_OAK_WOOD);
      MANGROVE_WOOD = register(Blocks.MANGROVE_WOOD);
      CRIMSON_HYPHAE = register(Blocks.CRIMSON_HYPHAE);
      WARPED_HYPHAE = register(Blocks.WARPED_HYPHAE);
      OAK_LEAVES = register(Blocks.OAK_LEAVES);
      SPRUCE_LEAVES = register(Blocks.SPRUCE_LEAVES);
      BIRCH_LEAVES = register(Blocks.BIRCH_LEAVES);
      JUNGLE_LEAVES = register(Blocks.JUNGLE_LEAVES);
      ACACIA_LEAVES = register(Blocks.ACACIA_LEAVES);
      CHERRY_LEAVES = register(Blocks.CHERRY_LEAVES);
      DARK_OAK_LEAVES = register(Blocks.DARK_OAK_LEAVES);
      PALE_OAK_LEAVES = register(Blocks.PALE_OAK_LEAVES);
      MANGROVE_LEAVES = register(Blocks.MANGROVE_LEAVES);
      AZALEA_LEAVES = register(Blocks.AZALEA_LEAVES);
      FLOWERING_AZALEA_LEAVES = register(Blocks.FLOWERING_AZALEA_LEAVES);
      SPONGE = register(Blocks.SPONGE);
      WET_SPONGE = register(Blocks.WET_SPONGE);
      GLASS = register(Blocks.GLASS);
      TINTED_GLASS = register(Blocks.TINTED_GLASS);
      LAPIS_BLOCK = register(Blocks.LAPIS_BLOCK);
      SANDSTONE = register(Blocks.SANDSTONE);
      CHISELED_SANDSTONE = register(Blocks.CHISELED_SANDSTONE);
      CUT_SANDSTONE = register(Blocks.CUT_SANDSTONE);
      COBWEB = register(Blocks.COBWEB);
      SHORT_GRASS = register(Blocks.SHORT_GRASS);
      FERN = register(Blocks.FERN);
      BUSH = register(Blocks.BUSH);
      AZALEA = register(Blocks.AZALEA);
      FLOWERING_AZALEA = register(Blocks.FLOWERING_AZALEA);
      DEAD_BUSH = register(Blocks.DEAD_BUSH);
      FIREFLY_BUSH = register(Blocks.FIREFLY_BUSH);
      SHORT_DRY_GRASS = register(Blocks.SHORT_DRY_GRASS);
      TALL_DRY_GRASS = register(Blocks.TALL_DRY_GRASS);
      SEAGRASS = register(Blocks.SEAGRASS);
      SEA_PICKLE = register(Blocks.SEA_PICKLE);
      WHITE_WOOL = register(Blocks.WHITE_WOOL);
      ORANGE_WOOL = register(Blocks.ORANGE_WOOL);
      MAGENTA_WOOL = register(Blocks.MAGENTA_WOOL);
      LIGHT_BLUE_WOOL = register(Blocks.LIGHT_BLUE_WOOL);
      YELLOW_WOOL = register(Blocks.YELLOW_WOOL);
      LIME_WOOL = register(Blocks.LIME_WOOL);
      PINK_WOOL = register(Blocks.PINK_WOOL);
      GRAY_WOOL = register(Blocks.GRAY_WOOL);
      LIGHT_GRAY_WOOL = register(Blocks.LIGHT_GRAY_WOOL);
      CYAN_WOOL = register(Blocks.CYAN_WOOL);
      PURPLE_WOOL = register(Blocks.PURPLE_WOOL);
      BLUE_WOOL = register(Blocks.BLUE_WOOL);
      BROWN_WOOL = register(Blocks.BROWN_WOOL);
      GREEN_WOOL = register(Blocks.GREEN_WOOL);
      RED_WOOL = register(Blocks.RED_WOOL);
      BLACK_WOOL = register(Blocks.BLACK_WOOL);
      DANDELION = register(Blocks.DANDELION);
      OPEN_EYEBLOSSOM = register(Blocks.OPEN_EYEBLOSSOM);
      CLOSED_EYEBLOSSOM = register(Blocks.CLOSED_EYEBLOSSOM);
      POPPY = register(Blocks.POPPY);
      BLUE_ORCHID = register(Blocks.BLUE_ORCHID);
      ALLIUM = register(Blocks.ALLIUM);
      AZURE_BLUET = register(Blocks.AZURE_BLUET);
      RED_TULIP = register(Blocks.RED_TULIP);
      ORANGE_TULIP = register(Blocks.ORANGE_TULIP);
      WHITE_TULIP = register(Blocks.WHITE_TULIP);
      PINK_TULIP = register(Blocks.PINK_TULIP);
      OXEYE_DAISY = register(Blocks.OXEYE_DAISY);
      CORNFLOWER = register(Blocks.CORNFLOWER);
      LILY_OF_THE_VALLEY = register(Blocks.LILY_OF_THE_VALLEY);
      WITHER_ROSE = register(Blocks.WITHER_ROSE);
      TORCHFLOWER = register(Blocks.TORCHFLOWER);
      PITCHER_PLANT = register(Blocks.PITCHER_PLANT);
      SPORE_BLOSSOM = register(Blocks.SPORE_BLOSSOM);
      BROWN_MUSHROOM = register(Blocks.BROWN_MUSHROOM);
      RED_MUSHROOM = register(Blocks.RED_MUSHROOM);
      CRIMSON_FUNGUS = register(Blocks.CRIMSON_FUNGUS);
      WARPED_FUNGUS = register(Blocks.WARPED_FUNGUS);
      CRIMSON_ROOTS = register(Blocks.CRIMSON_ROOTS);
      WARPED_ROOTS = register(Blocks.WARPED_ROOTS);
      NETHER_SPROUTS = register(Blocks.NETHER_SPROUTS);
      WEEPING_VINES = register(Blocks.WEEPING_VINES);
      TWISTING_VINES = register(Blocks.TWISTING_VINES);
      SUGAR_CANE = register(Blocks.SUGAR_CANE);
      KELP = register(Blocks.KELP);
      PINK_PETALS = register(Blocks.PINK_PETALS);
      WILDFLOWERS = register(Blocks.WILDFLOWERS);
      LEAF_LITTER = register(Blocks.LEAF_LITTER);
      MOSS_CARPET = register(Blocks.MOSS_CARPET);
      MOSS_BLOCK = register(Blocks.MOSS_BLOCK);
      PALE_MOSS_CARPET = register(Blocks.PALE_MOSS_CARPET);
      PALE_HANGING_MOSS = register(Blocks.PALE_HANGING_MOSS);
      PALE_MOSS_BLOCK = register(Blocks.PALE_MOSS_BLOCK);
      HANGING_ROOTS = register(Blocks.HANGING_ROOTS);
      BIG_DRIPLEAF = register(Blocks.BIG_DRIPLEAF, Blocks.BIG_DRIPLEAF_STEM);
      SMALL_DRIPLEAF = register(Blocks.SMALL_DRIPLEAF, TallBlockItem::new);
      BAMBOO = register(Blocks.BAMBOO);
      OAK_SLAB = register(Blocks.OAK_SLAB);
      SPRUCE_SLAB = register(Blocks.SPRUCE_SLAB);
      BIRCH_SLAB = register(Blocks.BIRCH_SLAB);
      JUNGLE_SLAB = register(Blocks.JUNGLE_SLAB);
      ACACIA_SLAB = register(Blocks.ACACIA_SLAB);
      CHERRY_SLAB = register(Blocks.CHERRY_SLAB);
      DARK_OAK_SLAB = register(Blocks.DARK_OAK_SLAB);
      PALE_OAK_SLAB = register(Blocks.PALE_OAK_SLAB);
      MANGROVE_SLAB = register(Blocks.MANGROVE_SLAB);
      BAMBOO_SLAB = register(Blocks.BAMBOO_SLAB);
      BAMBOO_MOSAIC_SLAB = register(Blocks.BAMBOO_MOSAIC_SLAB);
      CRIMSON_SLAB = register(Blocks.CRIMSON_SLAB);
      WARPED_SLAB = register(Blocks.WARPED_SLAB);
      STONE_SLAB = register(Blocks.STONE_SLAB);
      SMOOTH_STONE_SLAB = register(Blocks.SMOOTH_STONE_SLAB);
      SANDSTONE_SLAB = register(Blocks.SANDSTONE_SLAB);
      CUT_SANDSTONE_SLAB = register(Blocks.CUT_SANDSTONE_SLAB);
      PETRIFIED_OAK_SLAB = register(Blocks.PETRIFIED_OAK_SLAB);
      COBBLESTONE_SLAB = register(Blocks.COBBLESTONE_SLAB);
      BRICK_SLAB = register(Blocks.BRICK_SLAB);
      STONE_BRICK_SLAB = register(Blocks.STONE_BRICK_SLAB);
      MUD_BRICK_SLAB = register(Blocks.MUD_BRICK_SLAB);
      NETHER_BRICK_SLAB = register(Blocks.NETHER_BRICK_SLAB);
      QUARTZ_SLAB = register(Blocks.QUARTZ_SLAB);
      RED_SANDSTONE_SLAB = register(Blocks.RED_SANDSTONE_SLAB);
      CUT_RED_SANDSTONE_SLAB = register(Blocks.CUT_RED_SANDSTONE_SLAB);
      PURPUR_SLAB = register(Blocks.PURPUR_SLAB);
      PRISMARINE_SLAB = register(Blocks.PRISMARINE_SLAB);
      PRISMARINE_BRICK_SLAB = register(Blocks.PRISMARINE_BRICK_SLAB);
      DARK_PRISMARINE_SLAB = register(Blocks.DARK_PRISMARINE_SLAB);
      SMOOTH_QUARTZ = register(Blocks.SMOOTH_QUARTZ);
      SMOOTH_RED_SANDSTONE = register(Blocks.SMOOTH_RED_SANDSTONE);
      SMOOTH_SANDSTONE = register(Blocks.SMOOTH_SANDSTONE);
      SMOOTH_STONE = register(Blocks.SMOOTH_STONE);
      BRICKS = register(Blocks.BRICKS);
      BOOKSHELF = register(Blocks.BOOKSHELF);
      CHISELED_BOOKSHELF = register((Block)Blocks.CHISELED_BOOKSHELF, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      DECORATED_POT = register(Blocks.DECORATED_POT, (new Item.Settings()).component(DataComponentTypes.POT_DECORATIONS, Sherds.DEFAULT).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      MOSSY_COBBLESTONE = register(Blocks.MOSSY_COBBLESTONE);
      OBSIDIAN = register(Blocks.OBSIDIAN);
      TORCH = register((Block)Blocks.TORCH, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.WALL_TORCH, Direction.DOWN, settings)));
      END_ROD = register(Blocks.END_ROD);
      CHORUS_PLANT = register(Blocks.CHORUS_PLANT);
      CHORUS_FLOWER = register(Blocks.CHORUS_FLOWER);
      PURPUR_BLOCK = register(Blocks.PURPUR_BLOCK);
      PURPUR_PILLAR = register(Blocks.PURPUR_PILLAR);
      PURPUR_STAIRS = register(Blocks.PURPUR_STAIRS);
      SPAWNER = register(Blocks.SPAWNER);
      CREAKING_HEART = register(Blocks.CREAKING_HEART);
      CHEST = register((Block)Blocks.CHEST, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      CRAFTING_TABLE = register(Blocks.CRAFTING_TABLE);
      FARMLAND = register(Blocks.FARMLAND);
      FURNACE = register((Block)Blocks.FURNACE, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      LADDER = register(Blocks.LADDER);
      COBBLESTONE_STAIRS = register(Blocks.COBBLESTONE_STAIRS);
      SNOW = register(Blocks.SNOW);
      ICE = register(Blocks.ICE);
      SNOW_BLOCK = register(Blocks.SNOW_BLOCK);
      CACTUS = register(Blocks.CACTUS);
      CACTUS_FLOWER = register(Blocks.CACTUS_FLOWER);
      CLAY = register(Blocks.CLAY);
      JUKEBOX = register(Blocks.JUKEBOX);
      OAK_FENCE = register(Blocks.OAK_FENCE);
      SPRUCE_FENCE = register(Blocks.SPRUCE_FENCE);
      BIRCH_FENCE = register(Blocks.BIRCH_FENCE);
      JUNGLE_FENCE = register(Blocks.JUNGLE_FENCE);
      ACACIA_FENCE = register(Blocks.ACACIA_FENCE);
      CHERRY_FENCE = register(Blocks.CHERRY_FENCE);
      DARK_OAK_FENCE = register(Blocks.DARK_OAK_FENCE);
      PALE_OAK_FENCE = register(Blocks.PALE_OAK_FENCE);
      MANGROVE_FENCE = register(Blocks.MANGROVE_FENCE);
      BAMBOO_FENCE = register(Blocks.BAMBOO_FENCE);
      CRIMSON_FENCE = register(Blocks.CRIMSON_FENCE);
      WARPED_FENCE = register(Blocks.WARPED_FENCE);
      PUMPKIN = register(Blocks.PUMPKIN);
      CARVED_PUMPKIN = register((Block)Blocks.CARVED_PUMPKIN, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.builder(EquipmentSlot.HEAD).swappable(false).cameraOverlay(Identifier.ofVanilla("misc/pumpkinblur")).build())));
      JACK_O_LANTERN = register(Blocks.JACK_O_LANTERN);
      NETHERRACK = register(Blocks.NETHERRACK);
      SOUL_SAND = register(Blocks.SOUL_SAND);
      SOUL_SOIL = register(Blocks.SOUL_SOIL);
      BASALT = register(Blocks.BASALT);
      POLISHED_BASALT = register(Blocks.POLISHED_BASALT);
      SMOOTH_BASALT = register(Blocks.SMOOTH_BASALT);
      SOUL_TORCH = register((Block)Blocks.SOUL_TORCH, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.SOUL_WALL_TORCH, Direction.DOWN, settings)));
      GLOWSTONE = register(Blocks.GLOWSTONE);
      INFESTED_STONE = register(Blocks.INFESTED_STONE);
      INFESTED_COBBLESTONE = register(Blocks.INFESTED_COBBLESTONE);
      INFESTED_STONE_BRICKS = register(Blocks.INFESTED_STONE_BRICKS);
      INFESTED_MOSSY_STONE_BRICKS = register(Blocks.INFESTED_MOSSY_STONE_BRICKS);
      INFESTED_CRACKED_STONE_BRICKS = register(Blocks.INFESTED_CRACKED_STONE_BRICKS);
      INFESTED_CHISELED_STONE_BRICKS = register(Blocks.INFESTED_CHISELED_STONE_BRICKS);
      INFESTED_DEEPSLATE = register(Blocks.INFESTED_DEEPSLATE);
      STONE_BRICKS = register(Blocks.STONE_BRICKS);
      MOSSY_STONE_BRICKS = register(Blocks.MOSSY_STONE_BRICKS);
      CRACKED_STONE_BRICKS = register(Blocks.CRACKED_STONE_BRICKS);
      CHISELED_STONE_BRICKS = register(Blocks.CHISELED_STONE_BRICKS);
      PACKED_MUD = register(Blocks.PACKED_MUD);
      MUD_BRICKS = register(Blocks.MUD_BRICKS);
      DEEPSLATE_BRICKS = register(Blocks.DEEPSLATE_BRICKS);
      CRACKED_DEEPSLATE_BRICKS = register(Blocks.CRACKED_DEEPSLATE_BRICKS);
      DEEPSLATE_TILES = register(Blocks.DEEPSLATE_TILES);
      CRACKED_DEEPSLATE_TILES = register(Blocks.CRACKED_DEEPSLATE_TILES);
      CHISELED_DEEPSLATE = register(Blocks.CHISELED_DEEPSLATE);
      REINFORCED_DEEPSLATE = register(Blocks.REINFORCED_DEEPSLATE);
      BROWN_MUSHROOM_BLOCK = register(Blocks.BROWN_MUSHROOM_BLOCK);
      RED_MUSHROOM_BLOCK = register(Blocks.RED_MUSHROOM_BLOCK);
      MUSHROOM_STEM = register(Blocks.MUSHROOM_STEM);
      IRON_BARS = register(Blocks.IRON_BARS);
      CHAIN = register(Blocks.CHAIN);
      GLASS_PANE = register(Blocks.GLASS_PANE);
      MELON = register(Blocks.MELON);
      VINE = register(Blocks.VINE);
      GLOW_LICHEN = register(Blocks.GLOW_LICHEN);
      RESIN_CLUMP = register("resin_clump", createBlockItemWithUniqueName(Blocks.RESIN_CLUMP));
      RESIN_BLOCK = register(Blocks.RESIN_BLOCK);
      RESIN_BRICKS = register(Blocks.RESIN_BRICKS);
      RESIN_BRICK_STAIRS = register(Blocks.RESIN_BRICK_STAIRS);
      RESIN_BRICK_SLAB = register(Blocks.RESIN_BRICK_SLAB);
      RESIN_BRICK_WALL = register(Blocks.RESIN_BRICK_WALL);
      CHISELED_RESIN_BRICKS = register(Blocks.CHISELED_RESIN_BRICKS);
      BRICK_STAIRS = register(Blocks.BRICK_STAIRS);
      STONE_BRICK_STAIRS = register(Blocks.STONE_BRICK_STAIRS);
      MUD_BRICK_STAIRS = register(Blocks.MUD_BRICK_STAIRS);
      MYCELIUM = register(Blocks.MYCELIUM);
      LILY_PAD = register(Blocks.LILY_PAD, PlaceableOnWaterItem::new);
      NETHER_BRICKS = register(Blocks.NETHER_BRICKS);
      CRACKED_NETHER_BRICKS = register(Blocks.CRACKED_NETHER_BRICKS);
      CHISELED_NETHER_BRICKS = register(Blocks.CHISELED_NETHER_BRICKS);
      NETHER_BRICK_FENCE = register(Blocks.NETHER_BRICK_FENCE);
      NETHER_BRICK_STAIRS = register(Blocks.NETHER_BRICK_STAIRS);
      SCULK = register(Blocks.SCULK);
      SCULK_VEIN = register(Blocks.SCULK_VEIN);
      SCULK_CATALYST = register(Blocks.SCULK_CATALYST);
      SCULK_SHRIEKER = register(Blocks.SCULK_SHRIEKER);
      ENCHANTING_TABLE = register(Blocks.ENCHANTING_TABLE);
      END_PORTAL_FRAME = register(Blocks.END_PORTAL_FRAME);
      END_STONE = register(Blocks.END_STONE);
      END_STONE_BRICKS = register(Blocks.END_STONE_BRICKS);
      DRAGON_EGG = register(Blocks.DRAGON_EGG, (new Item.Settings()).rarity(Rarity.EPIC));
      SANDSTONE_STAIRS = register(Blocks.SANDSTONE_STAIRS);
      ENDER_CHEST = register(Blocks.ENDER_CHEST);
      EMERALD_BLOCK = register(Blocks.EMERALD_BLOCK);
      OAK_STAIRS = register(Blocks.OAK_STAIRS);
      SPRUCE_STAIRS = register(Blocks.SPRUCE_STAIRS);
      BIRCH_STAIRS = register(Blocks.BIRCH_STAIRS);
      JUNGLE_STAIRS = register(Blocks.JUNGLE_STAIRS);
      ACACIA_STAIRS = register(Blocks.ACACIA_STAIRS);
      CHERRY_STAIRS = register(Blocks.CHERRY_STAIRS);
      DARK_OAK_STAIRS = register(Blocks.DARK_OAK_STAIRS);
      PALE_OAK_STAIRS = register(Blocks.PALE_OAK_STAIRS);
      MANGROVE_STAIRS = register(Blocks.MANGROVE_STAIRS);
      BAMBOO_STAIRS = register(Blocks.BAMBOO_STAIRS);
      BAMBOO_MOSAIC_STAIRS = register(Blocks.BAMBOO_MOSAIC_STAIRS);
      CRIMSON_STAIRS = register(Blocks.CRIMSON_STAIRS);
      WARPED_STAIRS = register(Blocks.WARPED_STAIRS);
      COMMAND_BLOCK = register(Blocks.COMMAND_BLOCK, OperatorOnlyBlockItem::new, (new Item.Settings()).rarity(Rarity.EPIC));
      BEACON = register(Blocks.BEACON, (new Item.Settings()).rarity(Rarity.RARE));
      COBBLESTONE_WALL = register(Blocks.COBBLESTONE_WALL);
      MOSSY_COBBLESTONE_WALL = register(Blocks.MOSSY_COBBLESTONE_WALL);
      BRICK_WALL = register(Blocks.BRICK_WALL);
      PRISMARINE_WALL = register(Blocks.PRISMARINE_WALL);
      RED_SANDSTONE_WALL = register(Blocks.RED_SANDSTONE_WALL);
      MOSSY_STONE_BRICK_WALL = register(Blocks.MOSSY_STONE_BRICK_WALL);
      GRANITE_WALL = register(Blocks.GRANITE_WALL);
      STONE_BRICK_WALL = register(Blocks.STONE_BRICK_WALL);
      MUD_BRICK_WALL = register(Blocks.MUD_BRICK_WALL);
      NETHER_BRICK_WALL = register(Blocks.NETHER_BRICK_WALL);
      ANDESITE_WALL = register(Blocks.ANDESITE_WALL);
      RED_NETHER_BRICK_WALL = register(Blocks.RED_NETHER_BRICK_WALL);
      SANDSTONE_WALL = register(Blocks.SANDSTONE_WALL);
      END_STONE_BRICK_WALL = register(Blocks.END_STONE_BRICK_WALL);
      DIORITE_WALL = register(Blocks.DIORITE_WALL);
      BLACKSTONE_WALL = register(Blocks.BLACKSTONE_WALL);
      POLISHED_BLACKSTONE_WALL = register(Blocks.POLISHED_BLACKSTONE_WALL);
      POLISHED_BLACKSTONE_BRICK_WALL = register(Blocks.POLISHED_BLACKSTONE_BRICK_WALL);
      COBBLED_DEEPSLATE_WALL = register(Blocks.COBBLED_DEEPSLATE_WALL);
      POLISHED_DEEPSLATE_WALL = register(Blocks.POLISHED_DEEPSLATE_WALL);
      DEEPSLATE_BRICK_WALL = register(Blocks.DEEPSLATE_BRICK_WALL);
      DEEPSLATE_TILE_WALL = register(Blocks.DEEPSLATE_TILE_WALL);
      ANVIL = register(Blocks.ANVIL);
      CHIPPED_ANVIL = register(Blocks.CHIPPED_ANVIL);
      DAMAGED_ANVIL = register(Blocks.DAMAGED_ANVIL);
      CHISELED_QUARTZ_BLOCK = register(Blocks.CHISELED_QUARTZ_BLOCK);
      QUARTZ_BLOCK = register(Blocks.QUARTZ_BLOCK);
      QUARTZ_BRICKS = register(Blocks.QUARTZ_BRICKS);
      QUARTZ_PILLAR = register(Blocks.QUARTZ_PILLAR);
      QUARTZ_STAIRS = register(Blocks.QUARTZ_STAIRS);
      WHITE_TERRACOTTA = register(Blocks.WHITE_TERRACOTTA);
      ORANGE_TERRACOTTA = register(Blocks.ORANGE_TERRACOTTA);
      MAGENTA_TERRACOTTA = register(Blocks.MAGENTA_TERRACOTTA);
      LIGHT_BLUE_TERRACOTTA = register(Blocks.LIGHT_BLUE_TERRACOTTA);
      YELLOW_TERRACOTTA = register(Blocks.YELLOW_TERRACOTTA);
      LIME_TERRACOTTA = register(Blocks.LIME_TERRACOTTA);
      PINK_TERRACOTTA = register(Blocks.PINK_TERRACOTTA);
      GRAY_TERRACOTTA = register(Blocks.GRAY_TERRACOTTA);
      LIGHT_GRAY_TERRACOTTA = register(Blocks.LIGHT_GRAY_TERRACOTTA);
      CYAN_TERRACOTTA = register(Blocks.CYAN_TERRACOTTA);
      PURPLE_TERRACOTTA = register(Blocks.PURPLE_TERRACOTTA);
      BLUE_TERRACOTTA = register(Blocks.BLUE_TERRACOTTA);
      BROWN_TERRACOTTA = register(Blocks.BROWN_TERRACOTTA);
      GREEN_TERRACOTTA = register(Blocks.GREEN_TERRACOTTA);
      RED_TERRACOTTA = register(Blocks.RED_TERRACOTTA);
      BLACK_TERRACOTTA = register(Blocks.BLACK_TERRACOTTA);
      BARRIER = register(Blocks.BARRIER, (new Item.Settings()).rarity(Rarity.EPIC));
      LIGHT = register((Block)Blocks.LIGHT, (UnaryOperator)((settings) -> settings.rarity(Rarity.EPIC).component(DataComponentTypes.BLOCK_STATE, BlockStateComponent.DEFAULT.with(LightBlock.LEVEL_15, 15))));
      HAY_BLOCK = register(Blocks.HAY_BLOCK);
      WHITE_CARPET = register((Block)Blocks.WHITE_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.WHITE))));
      ORANGE_CARPET = register((Block)Blocks.ORANGE_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.ORANGE))));
      MAGENTA_CARPET = register((Block)Blocks.MAGENTA_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.MAGENTA))));
      LIGHT_BLUE_CARPET = register((Block)Blocks.LIGHT_BLUE_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.LIGHT_BLUE))));
      YELLOW_CARPET = register((Block)Blocks.YELLOW_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.YELLOW))));
      LIME_CARPET = register((Block)Blocks.LIME_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.LIME))));
      PINK_CARPET = register((Block)Blocks.PINK_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.PINK))));
      GRAY_CARPET = register((Block)Blocks.GRAY_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.GRAY))));
      LIGHT_GRAY_CARPET = register((Block)Blocks.LIGHT_GRAY_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.LIGHT_GRAY))));
      CYAN_CARPET = register((Block)Blocks.CYAN_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.CYAN))));
      PURPLE_CARPET = register((Block)Blocks.PURPLE_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.PURPLE))));
      BLUE_CARPET = register((Block)Blocks.BLUE_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.BLUE))));
      BROWN_CARPET = register((Block)Blocks.BROWN_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.BROWN))));
      GREEN_CARPET = register((Block)Blocks.GREEN_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.GREEN))));
      RED_CARPET = register((Block)Blocks.RED_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.RED))));
      BLACK_CARPET = register((Block)Blocks.BLACK_CARPET, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofCarpet(DyeColor.BLACK))));
      TERRACOTTA = register(Blocks.TERRACOTTA);
      PACKED_ICE = register(Blocks.PACKED_ICE);
      DIRT_PATH = register(Blocks.DIRT_PATH);
      SUNFLOWER = register(Blocks.SUNFLOWER, TallBlockItem::new);
      LILAC = register(Blocks.LILAC, TallBlockItem::new);
      ROSE_BUSH = register(Blocks.ROSE_BUSH, TallBlockItem::new);
      PEONY = register(Blocks.PEONY, TallBlockItem::new);
      TALL_GRASS = register(Blocks.TALL_GRASS, TallBlockItem::new);
      LARGE_FERN = register(Blocks.LARGE_FERN, TallBlockItem::new);
      WHITE_STAINED_GLASS = register(Blocks.WHITE_STAINED_GLASS);
      ORANGE_STAINED_GLASS = register(Blocks.ORANGE_STAINED_GLASS);
      MAGENTA_STAINED_GLASS = register(Blocks.MAGENTA_STAINED_GLASS);
      LIGHT_BLUE_STAINED_GLASS = register(Blocks.LIGHT_BLUE_STAINED_GLASS);
      YELLOW_STAINED_GLASS = register(Blocks.YELLOW_STAINED_GLASS);
      LIME_STAINED_GLASS = register(Blocks.LIME_STAINED_GLASS);
      PINK_STAINED_GLASS = register(Blocks.PINK_STAINED_GLASS);
      GRAY_STAINED_GLASS = register(Blocks.GRAY_STAINED_GLASS);
      LIGHT_GRAY_STAINED_GLASS = register(Blocks.LIGHT_GRAY_STAINED_GLASS);
      CYAN_STAINED_GLASS = register(Blocks.CYAN_STAINED_GLASS);
      PURPLE_STAINED_GLASS = register(Blocks.PURPLE_STAINED_GLASS);
      BLUE_STAINED_GLASS = register(Blocks.BLUE_STAINED_GLASS);
      BROWN_STAINED_GLASS = register(Blocks.BROWN_STAINED_GLASS);
      GREEN_STAINED_GLASS = register(Blocks.GREEN_STAINED_GLASS);
      RED_STAINED_GLASS = register(Blocks.RED_STAINED_GLASS);
      BLACK_STAINED_GLASS = register(Blocks.BLACK_STAINED_GLASS);
      WHITE_STAINED_GLASS_PANE = register(Blocks.WHITE_STAINED_GLASS_PANE);
      ORANGE_STAINED_GLASS_PANE = register(Blocks.ORANGE_STAINED_GLASS_PANE);
      MAGENTA_STAINED_GLASS_PANE = register(Blocks.MAGENTA_STAINED_GLASS_PANE);
      LIGHT_BLUE_STAINED_GLASS_PANE = register(Blocks.LIGHT_BLUE_STAINED_GLASS_PANE);
      YELLOW_STAINED_GLASS_PANE = register(Blocks.YELLOW_STAINED_GLASS_PANE);
      LIME_STAINED_GLASS_PANE = register(Blocks.LIME_STAINED_GLASS_PANE);
      PINK_STAINED_GLASS_PANE = register(Blocks.PINK_STAINED_GLASS_PANE);
      GRAY_STAINED_GLASS_PANE = register(Blocks.GRAY_STAINED_GLASS_PANE);
      LIGHT_GRAY_STAINED_GLASS_PANE = register(Blocks.LIGHT_GRAY_STAINED_GLASS_PANE);
      CYAN_STAINED_GLASS_PANE = register(Blocks.CYAN_STAINED_GLASS_PANE);
      PURPLE_STAINED_GLASS_PANE = register(Blocks.PURPLE_STAINED_GLASS_PANE);
      BLUE_STAINED_GLASS_PANE = register(Blocks.BLUE_STAINED_GLASS_PANE);
      BROWN_STAINED_GLASS_PANE = register(Blocks.BROWN_STAINED_GLASS_PANE);
      GREEN_STAINED_GLASS_PANE = register(Blocks.GREEN_STAINED_GLASS_PANE);
      RED_STAINED_GLASS_PANE = register(Blocks.RED_STAINED_GLASS_PANE);
      BLACK_STAINED_GLASS_PANE = register(Blocks.BLACK_STAINED_GLASS_PANE);
      PRISMARINE = register(Blocks.PRISMARINE);
      PRISMARINE_BRICKS = register(Blocks.PRISMARINE_BRICKS);
      DARK_PRISMARINE = register(Blocks.DARK_PRISMARINE);
      PRISMARINE_STAIRS = register(Blocks.PRISMARINE_STAIRS);
      PRISMARINE_BRICK_STAIRS = register(Blocks.PRISMARINE_BRICK_STAIRS);
      DARK_PRISMARINE_STAIRS = register(Blocks.DARK_PRISMARINE_STAIRS);
      SEA_LANTERN = register(Blocks.SEA_LANTERN);
      RED_SANDSTONE = register(Blocks.RED_SANDSTONE);
      CHISELED_RED_SANDSTONE = register(Blocks.CHISELED_RED_SANDSTONE);
      CUT_RED_SANDSTONE = register(Blocks.CUT_RED_SANDSTONE);
      RED_SANDSTONE_STAIRS = register(Blocks.RED_SANDSTONE_STAIRS);
      REPEATING_COMMAND_BLOCK = register(Blocks.REPEATING_COMMAND_BLOCK, OperatorOnlyBlockItem::new, (new Item.Settings()).rarity(Rarity.EPIC));
      CHAIN_COMMAND_BLOCK = register(Blocks.CHAIN_COMMAND_BLOCK, OperatorOnlyBlockItem::new, (new Item.Settings()).rarity(Rarity.EPIC));
      MAGMA_BLOCK = register(Blocks.MAGMA_BLOCK);
      NETHER_WART_BLOCK = register(Blocks.NETHER_WART_BLOCK);
      WARPED_WART_BLOCK = register(Blocks.WARPED_WART_BLOCK);
      RED_NETHER_BRICKS = register(Blocks.RED_NETHER_BRICKS);
      BONE_BLOCK = register(Blocks.BONE_BLOCK);
      STRUCTURE_VOID = register(Blocks.STRUCTURE_VOID, (new Item.Settings()).rarity(Rarity.EPIC));
      SHULKER_BOX = register(Blocks.SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      WHITE_SHULKER_BOX = register(Blocks.WHITE_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      ORANGE_SHULKER_BOX = register(Blocks.ORANGE_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      MAGENTA_SHULKER_BOX = register(Blocks.MAGENTA_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      LIGHT_BLUE_SHULKER_BOX = register(Blocks.LIGHT_BLUE_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      YELLOW_SHULKER_BOX = register(Blocks.YELLOW_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      LIME_SHULKER_BOX = register(Blocks.LIME_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      PINK_SHULKER_BOX = register(Blocks.PINK_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      GRAY_SHULKER_BOX = register(Blocks.GRAY_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      LIGHT_GRAY_SHULKER_BOX = register(Blocks.LIGHT_GRAY_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      CYAN_SHULKER_BOX = register(Blocks.CYAN_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      PURPLE_SHULKER_BOX = register(Blocks.PURPLE_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      BLUE_SHULKER_BOX = register(Blocks.BLUE_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      BROWN_SHULKER_BOX = register(Blocks.BROWN_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      GREEN_SHULKER_BOX = register(Blocks.GREEN_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      RED_SHULKER_BOX = register(Blocks.RED_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      BLACK_SHULKER_BOX = register(Blocks.BLACK_SHULKER_BOX, (new Item.Settings()).maxCount(1).component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT));
      WHITE_GLAZED_TERRACOTTA = register(Blocks.WHITE_GLAZED_TERRACOTTA);
      ORANGE_GLAZED_TERRACOTTA = register(Blocks.ORANGE_GLAZED_TERRACOTTA);
      MAGENTA_GLAZED_TERRACOTTA = register(Blocks.MAGENTA_GLAZED_TERRACOTTA);
      LIGHT_BLUE_GLAZED_TERRACOTTA = register(Blocks.LIGHT_BLUE_GLAZED_TERRACOTTA);
      YELLOW_GLAZED_TERRACOTTA = register(Blocks.YELLOW_GLAZED_TERRACOTTA);
      LIME_GLAZED_TERRACOTTA = register(Blocks.LIME_GLAZED_TERRACOTTA);
      PINK_GLAZED_TERRACOTTA = register(Blocks.PINK_GLAZED_TERRACOTTA);
      GRAY_GLAZED_TERRACOTTA = register(Blocks.GRAY_GLAZED_TERRACOTTA);
      LIGHT_GRAY_GLAZED_TERRACOTTA = register(Blocks.LIGHT_GRAY_GLAZED_TERRACOTTA);
      CYAN_GLAZED_TERRACOTTA = register(Blocks.CYAN_GLAZED_TERRACOTTA);
      PURPLE_GLAZED_TERRACOTTA = register(Blocks.PURPLE_GLAZED_TERRACOTTA);
      BLUE_GLAZED_TERRACOTTA = register(Blocks.BLUE_GLAZED_TERRACOTTA);
      BROWN_GLAZED_TERRACOTTA = register(Blocks.BROWN_GLAZED_TERRACOTTA);
      GREEN_GLAZED_TERRACOTTA = register(Blocks.GREEN_GLAZED_TERRACOTTA);
      RED_GLAZED_TERRACOTTA = register(Blocks.RED_GLAZED_TERRACOTTA);
      BLACK_GLAZED_TERRACOTTA = register(Blocks.BLACK_GLAZED_TERRACOTTA);
      WHITE_CONCRETE = register(Blocks.WHITE_CONCRETE);
      ORANGE_CONCRETE = register(Blocks.ORANGE_CONCRETE);
      MAGENTA_CONCRETE = register(Blocks.MAGENTA_CONCRETE);
      LIGHT_BLUE_CONCRETE = register(Blocks.LIGHT_BLUE_CONCRETE);
      YELLOW_CONCRETE = register(Blocks.YELLOW_CONCRETE);
      LIME_CONCRETE = register(Blocks.LIME_CONCRETE);
      PINK_CONCRETE = register(Blocks.PINK_CONCRETE);
      GRAY_CONCRETE = register(Blocks.GRAY_CONCRETE);
      LIGHT_GRAY_CONCRETE = register(Blocks.LIGHT_GRAY_CONCRETE);
      CYAN_CONCRETE = register(Blocks.CYAN_CONCRETE);
      PURPLE_CONCRETE = register(Blocks.PURPLE_CONCRETE);
      BLUE_CONCRETE = register(Blocks.BLUE_CONCRETE);
      BROWN_CONCRETE = register(Blocks.BROWN_CONCRETE);
      GREEN_CONCRETE = register(Blocks.GREEN_CONCRETE);
      RED_CONCRETE = register(Blocks.RED_CONCRETE);
      BLACK_CONCRETE = register(Blocks.BLACK_CONCRETE);
      WHITE_CONCRETE_POWDER = register(Blocks.WHITE_CONCRETE_POWDER);
      ORANGE_CONCRETE_POWDER = register(Blocks.ORANGE_CONCRETE_POWDER);
      MAGENTA_CONCRETE_POWDER = register(Blocks.MAGENTA_CONCRETE_POWDER);
      LIGHT_BLUE_CONCRETE_POWDER = register(Blocks.LIGHT_BLUE_CONCRETE_POWDER);
      YELLOW_CONCRETE_POWDER = register(Blocks.YELLOW_CONCRETE_POWDER);
      LIME_CONCRETE_POWDER = register(Blocks.LIME_CONCRETE_POWDER);
      PINK_CONCRETE_POWDER = register(Blocks.PINK_CONCRETE_POWDER);
      GRAY_CONCRETE_POWDER = register(Blocks.GRAY_CONCRETE_POWDER);
      LIGHT_GRAY_CONCRETE_POWDER = register(Blocks.LIGHT_GRAY_CONCRETE_POWDER);
      CYAN_CONCRETE_POWDER = register(Blocks.CYAN_CONCRETE_POWDER);
      PURPLE_CONCRETE_POWDER = register(Blocks.PURPLE_CONCRETE_POWDER);
      BLUE_CONCRETE_POWDER = register(Blocks.BLUE_CONCRETE_POWDER);
      BROWN_CONCRETE_POWDER = register(Blocks.BROWN_CONCRETE_POWDER);
      GREEN_CONCRETE_POWDER = register(Blocks.GREEN_CONCRETE_POWDER);
      RED_CONCRETE_POWDER = register(Blocks.RED_CONCRETE_POWDER);
      BLACK_CONCRETE_POWDER = register(Blocks.BLACK_CONCRETE_POWDER);
      TURTLE_EGG = register(Blocks.TURTLE_EGG);
      SNIFFER_EGG = register((Block)Blocks.SNIFFER_EGG, (UnaryOperator)((settings) -> settings.rarity(Rarity.UNCOMMON)));
      DEAD_TUBE_CORAL_BLOCK = register(Blocks.DEAD_TUBE_CORAL_BLOCK);
      DEAD_BRAIN_CORAL_BLOCK = register(Blocks.DEAD_BRAIN_CORAL_BLOCK);
      DEAD_BUBBLE_CORAL_BLOCK = register(Blocks.DEAD_BUBBLE_CORAL_BLOCK);
      DEAD_FIRE_CORAL_BLOCK = register(Blocks.DEAD_FIRE_CORAL_BLOCK);
      DEAD_HORN_CORAL_BLOCK = register(Blocks.DEAD_HORN_CORAL_BLOCK);
      TUBE_CORAL_BLOCK = register(Blocks.TUBE_CORAL_BLOCK);
      BRAIN_CORAL_BLOCK = register(Blocks.BRAIN_CORAL_BLOCK);
      BUBBLE_CORAL_BLOCK = register(Blocks.BUBBLE_CORAL_BLOCK);
      FIRE_CORAL_BLOCK = register(Blocks.FIRE_CORAL_BLOCK);
      HORN_CORAL_BLOCK = register(Blocks.HORN_CORAL_BLOCK);
      TUBE_CORAL = register(Blocks.TUBE_CORAL);
      BRAIN_CORAL = register(Blocks.BRAIN_CORAL);
      BUBBLE_CORAL = register(Blocks.BUBBLE_CORAL);
      FIRE_CORAL = register(Blocks.FIRE_CORAL);
      HORN_CORAL = register(Blocks.HORN_CORAL);
      DEAD_BRAIN_CORAL = register(Blocks.DEAD_BRAIN_CORAL);
      DEAD_BUBBLE_CORAL = register(Blocks.DEAD_BUBBLE_CORAL);
      DEAD_FIRE_CORAL = register(Blocks.DEAD_FIRE_CORAL);
      DEAD_HORN_CORAL = register(Blocks.DEAD_HORN_CORAL);
      DEAD_TUBE_CORAL = register(Blocks.DEAD_TUBE_CORAL);
      TUBE_CORAL_FAN = register((Block)Blocks.TUBE_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.TUBE_CORAL_WALL_FAN, Direction.DOWN, settings)));
      BRAIN_CORAL_FAN = register((Block)Blocks.BRAIN_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.BRAIN_CORAL_WALL_FAN, Direction.DOWN, settings)));
      BUBBLE_CORAL_FAN = register((Block)Blocks.BUBBLE_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.BUBBLE_CORAL_WALL_FAN, Direction.DOWN, settings)));
      FIRE_CORAL_FAN = register((Block)Blocks.FIRE_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.FIRE_CORAL_WALL_FAN, Direction.DOWN, settings)));
      HORN_CORAL_FAN = register((Block)Blocks.HORN_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.HORN_CORAL_WALL_FAN, Direction.DOWN, settings)));
      DEAD_TUBE_CORAL_FAN = register((Block)Blocks.DEAD_TUBE_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.DEAD_TUBE_CORAL_WALL_FAN, Direction.DOWN, settings)));
      DEAD_BRAIN_CORAL_FAN = register((Block)Blocks.DEAD_BRAIN_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.DEAD_BRAIN_CORAL_WALL_FAN, Direction.DOWN, settings)));
      DEAD_BUBBLE_CORAL_FAN = register((Block)Blocks.DEAD_BUBBLE_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.DEAD_BUBBLE_CORAL_WALL_FAN, Direction.DOWN, settings)));
      DEAD_FIRE_CORAL_FAN = register((Block)Blocks.DEAD_FIRE_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.DEAD_FIRE_CORAL_WALL_FAN, Direction.DOWN, settings)));
      DEAD_HORN_CORAL_FAN = register((Block)Blocks.DEAD_HORN_CORAL_FAN, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.DEAD_HORN_CORAL_WALL_FAN, Direction.DOWN, settings)));
      BLUE_ICE = register(Blocks.BLUE_ICE);
      CONDUIT = register(Blocks.CONDUIT, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      POLISHED_GRANITE_STAIRS = register(Blocks.POLISHED_GRANITE_STAIRS);
      SMOOTH_RED_SANDSTONE_STAIRS = register(Blocks.SMOOTH_RED_SANDSTONE_STAIRS);
      MOSSY_STONE_BRICK_STAIRS = register(Blocks.MOSSY_STONE_BRICK_STAIRS);
      POLISHED_DIORITE_STAIRS = register(Blocks.POLISHED_DIORITE_STAIRS);
      MOSSY_COBBLESTONE_STAIRS = register(Blocks.MOSSY_COBBLESTONE_STAIRS);
      END_STONE_BRICK_STAIRS = register(Blocks.END_STONE_BRICK_STAIRS);
      STONE_STAIRS = register(Blocks.STONE_STAIRS);
      SMOOTH_SANDSTONE_STAIRS = register(Blocks.SMOOTH_SANDSTONE_STAIRS);
      SMOOTH_QUARTZ_STAIRS = register(Blocks.SMOOTH_QUARTZ_STAIRS);
      GRANITE_STAIRS = register(Blocks.GRANITE_STAIRS);
      ANDESITE_STAIRS = register(Blocks.ANDESITE_STAIRS);
      RED_NETHER_BRICK_STAIRS = register(Blocks.RED_NETHER_BRICK_STAIRS);
      POLISHED_ANDESITE_STAIRS = register(Blocks.POLISHED_ANDESITE_STAIRS);
      DIORITE_STAIRS = register(Blocks.DIORITE_STAIRS);
      COBBLED_DEEPSLATE_STAIRS = register(Blocks.COBBLED_DEEPSLATE_STAIRS);
      POLISHED_DEEPSLATE_STAIRS = register(Blocks.POLISHED_DEEPSLATE_STAIRS);
      DEEPSLATE_BRICK_STAIRS = register(Blocks.DEEPSLATE_BRICK_STAIRS);
      DEEPSLATE_TILE_STAIRS = register(Blocks.DEEPSLATE_TILE_STAIRS);
      POLISHED_GRANITE_SLAB = register(Blocks.POLISHED_GRANITE_SLAB);
      SMOOTH_RED_SANDSTONE_SLAB = register(Blocks.SMOOTH_RED_SANDSTONE_SLAB);
      MOSSY_STONE_BRICK_SLAB = register(Blocks.MOSSY_STONE_BRICK_SLAB);
      POLISHED_DIORITE_SLAB = register(Blocks.POLISHED_DIORITE_SLAB);
      MOSSY_COBBLESTONE_SLAB = register(Blocks.MOSSY_COBBLESTONE_SLAB);
      END_STONE_BRICK_SLAB = register(Blocks.END_STONE_BRICK_SLAB);
      SMOOTH_SANDSTONE_SLAB = register(Blocks.SMOOTH_SANDSTONE_SLAB);
      SMOOTH_QUARTZ_SLAB = register(Blocks.SMOOTH_QUARTZ_SLAB);
      GRANITE_SLAB = register(Blocks.GRANITE_SLAB);
      ANDESITE_SLAB = register(Blocks.ANDESITE_SLAB);
      RED_NETHER_BRICK_SLAB = register(Blocks.RED_NETHER_BRICK_SLAB);
      POLISHED_ANDESITE_SLAB = register(Blocks.POLISHED_ANDESITE_SLAB);
      DIORITE_SLAB = register(Blocks.DIORITE_SLAB);
      COBBLED_DEEPSLATE_SLAB = register(Blocks.COBBLED_DEEPSLATE_SLAB);
      POLISHED_DEEPSLATE_SLAB = register(Blocks.POLISHED_DEEPSLATE_SLAB);
      DEEPSLATE_BRICK_SLAB = register(Blocks.DEEPSLATE_BRICK_SLAB);
      DEEPSLATE_TILE_SLAB = register(Blocks.DEEPSLATE_TILE_SLAB);
      SCAFFOLDING = register(Blocks.SCAFFOLDING, ScaffoldingItem::new);
      REDSTONE = register("redstone", createBlockItemWithUniqueName(Blocks.REDSTONE_WIRE), (new Item.Settings()).trimMaterial(ArmorTrimMaterials.REDSTONE));
      REDSTONE_TORCH = register((Block)Blocks.REDSTONE_TORCH, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.REDSTONE_WALL_TORCH, Direction.DOWN, settings)));
      REDSTONE_BLOCK = register(Blocks.REDSTONE_BLOCK);
      REPEATER = register(Blocks.REPEATER);
      COMPARATOR = register(Blocks.COMPARATOR);
      PISTON = register(Blocks.PISTON);
      STICKY_PISTON = register(Blocks.STICKY_PISTON);
      SLIME_BLOCK = register(Blocks.SLIME_BLOCK);
      HONEY_BLOCK = register(Blocks.HONEY_BLOCK);
      OBSERVER = register(Blocks.OBSERVER);
      HOPPER = register((Block)Blocks.HOPPER, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      DISPENSER = register((Block)Blocks.DISPENSER, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      DROPPER = register((Block)Blocks.DROPPER, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      LECTERN = register(Blocks.LECTERN);
      TARGET = register(Blocks.TARGET);
      LEVER = register(Blocks.LEVER);
      LIGHTNING_ROD = register(Blocks.LIGHTNING_ROD);
      DAYLIGHT_DETECTOR = register(Blocks.DAYLIGHT_DETECTOR);
      SCULK_SENSOR = register(Blocks.SCULK_SENSOR);
      CALIBRATED_SCULK_SENSOR = register(Blocks.CALIBRATED_SCULK_SENSOR);
      TRIPWIRE_HOOK = register(Blocks.TRIPWIRE_HOOK);
      TRAPPED_CHEST = register((Block)Blocks.TRAPPED_CHEST, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      TNT = register(Blocks.TNT);
      REDSTONE_LAMP = register(Blocks.REDSTONE_LAMP);
      NOTE_BLOCK = register(Blocks.NOTE_BLOCK);
      STONE_BUTTON = register(Blocks.STONE_BUTTON);
      POLISHED_BLACKSTONE_BUTTON = register(Blocks.POLISHED_BLACKSTONE_BUTTON);
      OAK_BUTTON = register(Blocks.OAK_BUTTON);
      SPRUCE_BUTTON = register(Blocks.SPRUCE_BUTTON);
      BIRCH_BUTTON = register(Blocks.BIRCH_BUTTON);
      JUNGLE_BUTTON = register(Blocks.JUNGLE_BUTTON);
      ACACIA_BUTTON = register(Blocks.ACACIA_BUTTON);
      CHERRY_BUTTON = register(Blocks.CHERRY_BUTTON);
      DARK_OAK_BUTTON = register(Blocks.DARK_OAK_BUTTON);
      PALE_OAK_BUTTON = register(Blocks.PALE_OAK_BUTTON);
      MANGROVE_BUTTON = register(Blocks.MANGROVE_BUTTON);
      BAMBOO_BUTTON = register(Blocks.BAMBOO_BUTTON);
      CRIMSON_BUTTON = register(Blocks.CRIMSON_BUTTON);
      WARPED_BUTTON = register(Blocks.WARPED_BUTTON);
      STONE_PRESSURE_PLATE = register(Blocks.STONE_PRESSURE_PLATE);
      POLISHED_BLACKSTONE_PRESSURE_PLATE = register(Blocks.POLISHED_BLACKSTONE_PRESSURE_PLATE);
      LIGHT_WEIGHTED_PRESSURE_PLATE = register(Blocks.LIGHT_WEIGHTED_PRESSURE_PLATE);
      HEAVY_WEIGHTED_PRESSURE_PLATE = register(Blocks.HEAVY_WEIGHTED_PRESSURE_PLATE);
      OAK_PRESSURE_PLATE = register(Blocks.OAK_PRESSURE_PLATE);
      SPRUCE_PRESSURE_PLATE = register(Blocks.SPRUCE_PRESSURE_PLATE);
      BIRCH_PRESSURE_PLATE = register(Blocks.BIRCH_PRESSURE_PLATE);
      JUNGLE_PRESSURE_PLATE = register(Blocks.JUNGLE_PRESSURE_PLATE);
      ACACIA_PRESSURE_PLATE = register(Blocks.ACACIA_PRESSURE_PLATE);
      CHERRY_PRESSURE_PLATE = register(Blocks.CHERRY_PRESSURE_PLATE);
      DARK_OAK_PRESSURE_PLATE = register(Blocks.DARK_OAK_PRESSURE_PLATE);
      PALE_OAK_PRESSURE_PLATE = register(Blocks.PALE_OAK_PRESSURE_PLATE);
      MANGROVE_PRESSURE_PLATE = register(Blocks.MANGROVE_PRESSURE_PLATE);
      BAMBOO_PRESSURE_PLATE = register(Blocks.BAMBOO_PRESSURE_PLATE);
      CRIMSON_PRESSURE_PLATE = register(Blocks.CRIMSON_PRESSURE_PLATE);
      WARPED_PRESSURE_PLATE = register(Blocks.WARPED_PRESSURE_PLATE);
      IRON_DOOR = register(Blocks.IRON_DOOR, TallBlockItem::new);
      OAK_DOOR = register(Blocks.OAK_DOOR, TallBlockItem::new);
      SPRUCE_DOOR = register(Blocks.SPRUCE_DOOR, TallBlockItem::new);
      BIRCH_DOOR = register(Blocks.BIRCH_DOOR, TallBlockItem::new);
      JUNGLE_DOOR = register(Blocks.JUNGLE_DOOR, TallBlockItem::new);
      ACACIA_DOOR = register(Blocks.ACACIA_DOOR, TallBlockItem::new);
      CHERRY_DOOR = register(Blocks.CHERRY_DOOR, TallBlockItem::new);
      DARK_OAK_DOOR = register(Blocks.DARK_OAK_DOOR, TallBlockItem::new);
      PALE_OAK_DOOR = register(Blocks.PALE_OAK_DOOR, TallBlockItem::new);
      MANGROVE_DOOR = register(Blocks.MANGROVE_DOOR, TallBlockItem::new);
      BAMBOO_DOOR = register(Blocks.BAMBOO_DOOR, TallBlockItem::new);
      CRIMSON_DOOR = register(Blocks.CRIMSON_DOOR, TallBlockItem::new);
      WARPED_DOOR = register(Blocks.WARPED_DOOR, TallBlockItem::new);
      COPPER_DOOR = register(Blocks.COPPER_DOOR, TallBlockItem::new);
      EXPOSED_COPPER_DOOR = register(Blocks.EXPOSED_COPPER_DOOR, TallBlockItem::new);
      WEATHERED_COPPER_DOOR = register(Blocks.WEATHERED_COPPER_DOOR, TallBlockItem::new);
      OXIDIZED_COPPER_DOOR = register(Blocks.OXIDIZED_COPPER_DOOR, TallBlockItem::new);
      WAXED_COPPER_DOOR = register(Blocks.WAXED_COPPER_DOOR, TallBlockItem::new);
      WAXED_EXPOSED_COPPER_DOOR = register(Blocks.WAXED_EXPOSED_COPPER_DOOR, TallBlockItem::new);
      WAXED_WEATHERED_COPPER_DOOR = register(Blocks.WAXED_WEATHERED_COPPER_DOOR, TallBlockItem::new);
      WAXED_OXIDIZED_COPPER_DOOR = register(Blocks.WAXED_OXIDIZED_COPPER_DOOR, TallBlockItem::new);
      IRON_TRAPDOOR = register(Blocks.IRON_TRAPDOOR);
      OAK_TRAPDOOR = register(Blocks.OAK_TRAPDOOR);
      SPRUCE_TRAPDOOR = register(Blocks.SPRUCE_TRAPDOOR);
      BIRCH_TRAPDOOR = register(Blocks.BIRCH_TRAPDOOR);
      JUNGLE_TRAPDOOR = register(Blocks.JUNGLE_TRAPDOOR);
      ACACIA_TRAPDOOR = register(Blocks.ACACIA_TRAPDOOR);
      CHERRY_TRAPDOOR = register(Blocks.CHERRY_TRAPDOOR);
      DARK_OAK_TRAPDOOR = register(Blocks.DARK_OAK_TRAPDOOR);
      PALE_OAK_TRAPDOOR = register(Blocks.PALE_OAK_TRAPDOOR);
      MANGROVE_TRAPDOOR = register(Blocks.MANGROVE_TRAPDOOR);
      BAMBOO_TRAPDOOR = register(Blocks.BAMBOO_TRAPDOOR);
      CRIMSON_TRAPDOOR = register(Blocks.CRIMSON_TRAPDOOR);
      WARPED_TRAPDOOR = register(Blocks.WARPED_TRAPDOOR);
      COPPER_TRAPDOOR = register(Blocks.COPPER_TRAPDOOR);
      EXPOSED_COPPER_TRAPDOOR = register(Blocks.EXPOSED_COPPER_TRAPDOOR);
      WEATHERED_COPPER_TRAPDOOR = register(Blocks.WEATHERED_COPPER_TRAPDOOR);
      OXIDIZED_COPPER_TRAPDOOR = register(Blocks.OXIDIZED_COPPER_TRAPDOOR);
      WAXED_COPPER_TRAPDOOR = register(Blocks.WAXED_COPPER_TRAPDOOR);
      WAXED_EXPOSED_COPPER_TRAPDOOR = register(Blocks.WAXED_EXPOSED_COPPER_TRAPDOOR);
      WAXED_WEATHERED_COPPER_TRAPDOOR = register(Blocks.WAXED_WEATHERED_COPPER_TRAPDOOR);
      WAXED_OXIDIZED_COPPER_TRAPDOOR = register(Blocks.WAXED_OXIDIZED_COPPER_TRAPDOOR);
      OAK_FENCE_GATE = register(Blocks.OAK_FENCE_GATE);
      SPRUCE_FENCE_GATE = register(Blocks.SPRUCE_FENCE_GATE);
      BIRCH_FENCE_GATE = register(Blocks.BIRCH_FENCE_GATE);
      JUNGLE_FENCE_GATE = register(Blocks.JUNGLE_FENCE_GATE);
      ACACIA_FENCE_GATE = register(Blocks.ACACIA_FENCE_GATE);
      CHERRY_FENCE_GATE = register(Blocks.CHERRY_FENCE_GATE);
      DARK_OAK_FENCE_GATE = register(Blocks.DARK_OAK_FENCE_GATE);
      PALE_OAK_FENCE_GATE = register(Blocks.PALE_OAK_FENCE_GATE);
      MANGROVE_FENCE_GATE = register(Blocks.MANGROVE_FENCE_GATE);
      BAMBOO_FENCE_GATE = register(Blocks.BAMBOO_FENCE_GATE);
      CRIMSON_FENCE_GATE = register(Blocks.CRIMSON_FENCE_GATE);
      WARPED_FENCE_GATE = register(Blocks.WARPED_FENCE_GATE);
      POWERED_RAIL = register(Blocks.POWERED_RAIL);
      DETECTOR_RAIL = register(Blocks.DETECTOR_RAIL);
      RAIL = register(Blocks.RAIL);
      ACTIVATOR_RAIL = register(Blocks.ACTIVATOR_RAIL);
      SADDLE = register("saddle", (new Item.Settings()).maxCount(1).component(DataComponentTypes.EQUIPPABLE, EquippableComponent.ofSaddle()));
      MINECART = register((String)"minecart", (Function)((settings) -> new MinecartItem(EntityType.MINECART, settings)), (new Item.Settings()).maxCount(1));
      CHEST_MINECART = register((String)"chest_minecart", (Function)((settings) -> new MinecartItem(EntityType.CHEST_MINECART, settings)), (new Item.Settings()).maxCount(1));
      FURNACE_MINECART = register((String)"furnace_minecart", (Function)((settings) -> new MinecartItem(EntityType.FURNACE_MINECART, settings)), (new Item.Settings()).maxCount(1));
      TNT_MINECART = register((String)"tnt_minecart", (Function)((settings) -> new MinecartItem(EntityType.TNT_MINECART, settings)), (new Item.Settings()).maxCount(1));
      HOPPER_MINECART = register((String)"hopper_minecart", (Function)((settings) -> new MinecartItem(EntityType.HOPPER_MINECART, settings)), (new Item.Settings()).maxCount(1));
      CARROT_ON_A_STICK = register((String)"carrot_on_a_stick", (Function)((settings) -> new OnAStickItem(EntityType.PIG, 7, settings)), (new Item.Settings()).maxDamage(25));
      WARPED_FUNGUS_ON_A_STICK = register((String)"warped_fungus_on_a_stick", (Function)((settings) -> new OnAStickItem(EntityType.STRIDER, 1, settings)), (new Item.Settings()).maxDamage(100));
      PHANTOM_MEMBRANE = register("phantom_membrane");
      ELYTRA = register("elytra", (new Item.Settings()).maxDamage(432).rarity(Rarity.EPIC).component(DataComponentTypes.GLIDER, Unit.INSTANCE).component(DataComponentTypes.EQUIPPABLE, EquippableComponent.builder(EquipmentSlot.CHEST).equipSound(SoundEvents.ITEM_ARMOR_EQUIP_ELYTRA).model(EquipmentAssetKeys.ELYTRA).damageOnHurt(false).build()).repairable(PHANTOM_MEMBRANE));
      OAK_BOAT = register((String)"oak_boat", (Function)((settings) -> new BoatItem(EntityType.OAK_BOAT, settings)), (new Item.Settings()).maxCount(1));
      OAK_CHEST_BOAT = register((String)"oak_chest_boat", (Function)((settings) -> new BoatItem(EntityType.OAK_CHEST_BOAT, settings)), (new Item.Settings()).maxCount(1));
      SPRUCE_BOAT = register((String)"spruce_boat", (Function)((settings) -> new BoatItem(EntityType.SPRUCE_BOAT, settings)), (new Item.Settings()).maxCount(1));
      SPRUCE_CHEST_BOAT = register((String)"spruce_chest_boat", (Function)((settings) -> new BoatItem(EntityType.SPRUCE_CHEST_BOAT, settings)), (new Item.Settings()).maxCount(1));
      BIRCH_BOAT = register((String)"birch_boat", (Function)((settings) -> new BoatItem(EntityType.BIRCH_BOAT, settings)), (new Item.Settings()).maxCount(1));
      BIRCH_CHEST_BOAT = register((String)"birch_chest_boat", (Function)((settings) -> new BoatItem(EntityType.BIRCH_CHEST_BOAT, settings)), (new Item.Settings()).maxCount(1));
      JUNGLE_BOAT = register((String)"jungle_boat", (Function)((settings) -> new BoatItem(EntityType.JUNGLE_BOAT, settings)), (new Item.Settings()).maxCount(1));
      JUNGLE_CHEST_BOAT = register((String)"jungle_chest_boat", (Function)((settings) -> new BoatItem(EntityType.JUNGLE_CHEST_BOAT, settings)), (new Item.Settings()).maxCount(1));
      ACACIA_BOAT = register((String)"acacia_boat", (Function)((settings) -> new BoatItem(EntityType.ACACIA_BOAT, settings)), (new Item.Settings()).maxCount(1));
      ACACIA_CHEST_BOAT = register((String)"acacia_chest_boat", (Function)((settings) -> new BoatItem(EntityType.ACACIA_CHEST_BOAT, settings)), (new Item.Settings()).maxCount(1));
      CHERRY_BOAT = register((String)"cherry_boat", (Function)((settings) -> new BoatItem(EntityType.CHERRY_BOAT, settings)), (new Item.Settings()).maxCount(1));
      CHERRY_CHEST_BOAT = register((String)"cherry_chest_boat", (Function)((settings) -> new BoatItem(EntityType.CHERRY_CHEST_BOAT, settings)), (new Item.Settings()).maxCount(1));
      DARK_OAK_BOAT = register((String)"dark_oak_boat", (Function)((settings) -> new BoatItem(EntityType.DARK_OAK_BOAT, settings)), (new Item.Settings()).maxCount(1));
      DARK_OAK_CHEST_BOAT = register((String)"dark_oak_chest_boat", (Function)((settings) -> new BoatItem(EntityType.DARK_OAK_CHEST_BOAT, settings)), (new Item.Settings()).maxCount(1));
      PALE_OAK_BOAT = register((String)"pale_oak_boat", (Function)((settings) -> new BoatItem(EntityType.PALE_OAK_BOAT, settings)), (new Item.Settings()).maxCount(1));
      PALE_OAK_CHEST_BOAT = register((String)"pale_oak_chest_boat", (Function)((settings) -> new BoatItem(EntityType.PALE_OAK_CHEST_BOAT, settings)), (new Item.Settings()).maxCount(1));
      MANGROVE_BOAT = register((String)"mangrove_boat", (Function)((settings) -> new BoatItem(EntityType.MANGROVE_BOAT, settings)), (new Item.Settings()).maxCount(1));
      MANGROVE_CHEST_BOAT = register((String)"mangrove_chest_boat", (Function)((settings) -> new BoatItem(EntityType.MANGROVE_CHEST_BOAT, settings)), (new Item.Settings()).maxCount(1));
      BAMBOO_RAFT = register((String)"bamboo_raft", (Function)((settings) -> new BoatItem(EntityType.BAMBOO_RAFT, settings)), (new Item.Settings()).maxCount(1));
      BAMBOO_CHEST_RAFT = register((String)"bamboo_chest_raft", (Function)((settings) -> new BoatItem(EntityType.BAMBOO_CHEST_RAFT, settings)), (new Item.Settings()).maxCount(1));
      STRUCTURE_BLOCK = register(Blocks.STRUCTURE_BLOCK, OperatorOnlyBlockItem::new, (new Item.Settings()).rarity(Rarity.EPIC));
      JIGSAW = register(Blocks.JIGSAW, OperatorOnlyBlockItem::new, (new Item.Settings()).rarity(Rarity.EPIC));
      TEST_BLOCK = register(Blocks.TEST_BLOCK, OperatorOnlyBlockItem::new, (new Item.Settings()).rarity(Rarity.EPIC).component(DataComponentTypes.BLOCK_STATE, BlockStateComponent.DEFAULT.with(TestBlock.MODE, TestBlockMode.START)));
      TEST_INSTANCE_BLOCK = register(Blocks.TEST_INSTANCE_BLOCK, OperatorOnlyBlockItem::new, (new Item.Settings()).rarity(Rarity.EPIC));
      TURTLE_HELMET = register("turtle_helmet", (new Item.Settings()).armor(ArmorMaterials.TURTLE_SCUTE, EquipmentType.HELMET));
      TURTLE_SCUTE = register("turtle_scute");
      ARMADILLO_SCUTE = register("armadillo_scute");
      WOLF_ARMOR = register("wolf_armor", (new Item.Settings()).wolfArmor(ArmorMaterials.ARMADILLO_SCUTE));
      FLINT_AND_STEEL = register("flint_and_steel", FlintAndSteelItem::new, (new Item.Settings()).maxDamage(64));
      BOWL = register("bowl");
      APPLE = register("apple", (new Item.Settings()).food(FoodComponents.APPLE));
      BOW = register("bow", BowItem::new, (new Item.Settings()).maxDamage(384).enchantable(1));
      ARROW = register("arrow", ArrowItem::new);
      COAL = register("coal");
      CHARCOAL = register("charcoal");
      DIAMOND = register("diamond", (new Item.Settings()).trimMaterial(ArmorTrimMaterials.DIAMOND));
      EMERALD = register("emerald", (new Item.Settings()).trimMaterial(ArmorTrimMaterials.EMERALD));
      LAPIS_LAZULI = register("lapis_lazuli", (new Item.Settings()).trimMaterial(ArmorTrimMaterials.LAPIS));
      QUARTZ = register("quartz", (new Item.Settings()).trimMaterial(ArmorTrimMaterials.QUARTZ));
      AMETHYST_SHARD = register("amethyst_shard", (new Item.Settings()).trimMaterial(ArmorTrimMaterials.AMETHYST));
      RAW_IRON = register("raw_iron");
      IRON_INGOT = register("iron_ingot", (new Item.Settings()).trimMaterial(ArmorTrimMaterials.IRON));
      RAW_COPPER = register("raw_copper");
      COPPER_INGOT = register("copper_ingot", (new Item.Settings()).trimMaterial(ArmorTrimMaterials.COPPER));
      RAW_GOLD = register("raw_gold");
      GOLD_INGOT = register("gold_ingot", (new Item.Settings()).trimMaterial(ArmorTrimMaterials.GOLD));
      NETHERITE_INGOT = register("netherite_ingot", (new Item.Settings()).fireproof().trimMaterial(ArmorTrimMaterials.NETHERITE));
      NETHERITE_SCRAP = register("netherite_scrap", (new Item.Settings()).fireproof());
      WOODEN_SWORD = register("wooden_sword", (new Item.Settings()).sword(ToolMaterial.WOOD, 3.0F, -2.4F));
      WOODEN_SHOVEL = register((String)"wooden_shovel", (Function)((settings) -> new ShovelItem(ToolMaterial.WOOD, 1.5F, -3.0F, settings)));
      WOODEN_PICKAXE = register("wooden_pickaxe", (new Item.Settings()).pickaxe(ToolMaterial.WOOD, 1.0F, -2.8F));
      WOODEN_AXE = register((String)"wooden_axe", (Function)((settings) -> new AxeItem(ToolMaterial.WOOD, 6.0F, -3.2F, settings)));
      WOODEN_HOE = register((String)"wooden_hoe", (Function)((settings) -> new HoeItem(ToolMaterial.WOOD, 0.0F, -3.0F, settings)));
      STONE_SWORD = register("stone_sword", (new Item.Settings()).sword(ToolMaterial.STONE, 3.0F, -2.4F));
      STONE_SHOVEL = register((String)"stone_shovel", (Function)((settings) -> new ShovelItem(ToolMaterial.STONE, 1.5F, -3.0F, settings)));
      STONE_PICKAXE = register("stone_pickaxe", (new Item.Settings()).pickaxe(ToolMaterial.STONE, 1.0F, -2.8F));
      STONE_AXE = register((String)"stone_axe", (Function)((settings) -> new AxeItem(ToolMaterial.STONE, 7.0F, -3.2F, settings)));
      STONE_HOE = register((String)"stone_hoe", (Function)((settings) -> new HoeItem(ToolMaterial.STONE, -1.0F, -2.0F, settings)));
      GOLDEN_SWORD = register("golden_sword", (new Item.Settings()).sword(ToolMaterial.GOLD, 3.0F, -2.4F));
      GOLDEN_SHOVEL = register((String)"golden_shovel", (Function)((settings) -> new ShovelItem(ToolMaterial.GOLD, 1.5F, -3.0F, settings)));
      GOLDEN_PICKAXE = register("golden_pickaxe", (new Item.Settings()).pickaxe(ToolMaterial.GOLD, 1.0F, -2.8F));
      GOLDEN_AXE = register((String)"golden_axe", (Function)((settings) -> new AxeItem(ToolMaterial.GOLD, 6.0F, -3.0F, settings)));
      GOLDEN_HOE = register((String)"golden_hoe", (Function)((settings) -> new HoeItem(ToolMaterial.GOLD, 0.0F, -3.0F, settings)));
      IRON_SWORD = register("iron_sword", (new Item.Settings()).sword(ToolMaterial.IRON, 3.0F, -2.4F));
      IRON_SHOVEL = register((String)"iron_shovel", (Function)((settings) -> new ShovelItem(ToolMaterial.IRON, 1.5F, -3.0F, settings)));
      IRON_PICKAXE = register("iron_pickaxe", (new Item.Settings()).pickaxe(ToolMaterial.IRON, 1.0F, -2.8F));
      IRON_AXE = register((String)"iron_axe", (Function)((settings) -> new AxeItem(ToolMaterial.IRON, 6.0F, -3.1F, settings)));
      IRON_HOE = register((String)"iron_hoe", (Function)((settings) -> new HoeItem(ToolMaterial.IRON, -2.0F, -1.0F, settings)));
      DIAMOND_SWORD = register("diamond_sword", (new Item.Settings()).sword(ToolMaterial.DIAMOND, 3.0F, -2.4F));
      DIAMOND_SHOVEL = register((String)"diamond_shovel", (Function)((settings) -> new ShovelItem(ToolMaterial.DIAMOND, 1.5F, -3.0F, settings)));
      DIAMOND_PICKAXE = register("diamond_pickaxe", (new Item.Settings()).pickaxe(ToolMaterial.DIAMOND, 1.0F, -2.8F));
      DIAMOND_AXE = register((String)"diamond_axe", (Function)((settings) -> new AxeItem(ToolMaterial.DIAMOND, 5.0F, -3.0F, settings)));
      DIAMOND_HOE = register((String)"diamond_hoe", (Function)((settings) -> new HoeItem(ToolMaterial.DIAMOND, -3.0F, 0.0F, settings)));
      NETHERITE_SWORD = register("netherite_sword", (new Item.Settings()).sword(ToolMaterial.NETHERITE, 3.0F, -2.4F).fireproof());
      NETHERITE_SHOVEL = register((String)"netherite_shovel", (Function)((settings) -> new ShovelItem(ToolMaterial.NETHERITE, 1.5F, -3.0F, settings)), (new Item.Settings()).fireproof());
      NETHERITE_PICKAXE = register("netherite_pickaxe", (new Item.Settings()).pickaxe(ToolMaterial.NETHERITE, 1.0F, -2.8F).fireproof());
      NETHERITE_AXE = register((String)"netherite_axe", (Function)((settings) -> new AxeItem(ToolMaterial.NETHERITE, 5.0F, -3.0F, settings)), (new Item.Settings()).fireproof());
      NETHERITE_HOE = register((String)"netherite_hoe", (Function)((settings) -> new HoeItem(ToolMaterial.NETHERITE, -4.0F, 0.0F, settings)), (new Item.Settings()).fireproof());
      STICK = register("stick");
      MUSHROOM_STEW = register("mushroom_stew", (new Item.Settings()).maxCount(1).food(FoodComponents.MUSHROOM_STEW).useRemainder(BOWL));
      STRING = register("string", createBlockItemWithUniqueName(Blocks.TRIPWIRE));
      FEATHER = register("feather");
      GUNPOWDER = register("gunpowder");
      WHEAT_SEEDS = register("wheat_seeds", createBlockItemWithUniqueName(Blocks.WHEAT));
      WHEAT = register("wheat");
      BREAD = register("bread", (new Item.Settings()).food(FoodComponents.BREAD));
      LEATHER_HELMET = register("leather_helmet", (new Item.Settings()).armor(ArmorMaterials.LEATHER, EquipmentType.HELMET));
      LEATHER_CHESTPLATE = register("leather_chestplate", (new Item.Settings()).armor(ArmorMaterials.LEATHER, EquipmentType.CHESTPLATE));
      LEATHER_LEGGINGS = register("leather_leggings", (new Item.Settings()).armor(ArmorMaterials.LEATHER, EquipmentType.LEGGINGS));
      LEATHER_BOOTS = register("leather_boots", (new Item.Settings()).armor(ArmorMaterials.LEATHER, EquipmentType.BOOTS));
      CHAINMAIL_HELMET = register("chainmail_helmet", (new Item.Settings()).armor(ArmorMaterials.CHAIN, EquipmentType.HELMET).rarity(Rarity.UNCOMMON));
      CHAINMAIL_CHESTPLATE = register("chainmail_chestplate", (new Item.Settings()).armor(ArmorMaterials.CHAIN, EquipmentType.CHESTPLATE).rarity(Rarity.UNCOMMON));
      CHAINMAIL_LEGGINGS = register("chainmail_leggings", (new Item.Settings()).armor(ArmorMaterials.CHAIN, EquipmentType.LEGGINGS).rarity(Rarity.UNCOMMON));
      CHAINMAIL_BOOTS = register("chainmail_boots", (new Item.Settings()).armor(ArmorMaterials.CHAIN, EquipmentType.BOOTS).rarity(Rarity.UNCOMMON));
      IRON_HELMET = register("iron_helmet", (new Item.Settings()).armor(ArmorMaterials.IRON, EquipmentType.HELMET));
      IRON_CHESTPLATE = register("iron_chestplate", (new Item.Settings()).armor(ArmorMaterials.IRON, EquipmentType.CHESTPLATE));
      IRON_LEGGINGS = register("iron_leggings", (new Item.Settings()).armor(ArmorMaterials.IRON, EquipmentType.LEGGINGS));
      IRON_BOOTS = register("iron_boots", (new Item.Settings()).armor(ArmorMaterials.IRON, EquipmentType.BOOTS));
      DIAMOND_HELMET = register("diamond_helmet", (new Item.Settings()).armor(ArmorMaterials.DIAMOND, EquipmentType.HELMET));
      DIAMOND_CHESTPLATE = register("diamond_chestplate", (new Item.Settings()).armor(ArmorMaterials.DIAMOND, EquipmentType.CHESTPLATE));
      DIAMOND_LEGGINGS = register("diamond_leggings", (new Item.Settings()).armor(ArmorMaterials.DIAMOND, EquipmentType.LEGGINGS));
      DIAMOND_BOOTS = register("diamond_boots", (new Item.Settings()).armor(ArmorMaterials.DIAMOND, EquipmentType.BOOTS));
      GOLDEN_HELMET = register("golden_helmet", (new Item.Settings()).armor(ArmorMaterials.GOLD, EquipmentType.HELMET));
      GOLDEN_CHESTPLATE = register("golden_chestplate", (new Item.Settings()).armor(ArmorMaterials.GOLD, EquipmentType.CHESTPLATE));
      GOLDEN_LEGGINGS = register("golden_leggings", (new Item.Settings()).armor(ArmorMaterials.GOLD, EquipmentType.LEGGINGS));
      GOLDEN_BOOTS = register("golden_boots", (new Item.Settings()).armor(ArmorMaterials.GOLD, EquipmentType.BOOTS));
      NETHERITE_HELMET = register("netherite_helmet", (new Item.Settings()).armor(ArmorMaterials.NETHERITE, EquipmentType.HELMET).fireproof());
      NETHERITE_CHESTPLATE = register("netherite_chestplate", (new Item.Settings()).armor(ArmorMaterials.NETHERITE, EquipmentType.CHESTPLATE).fireproof());
      NETHERITE_LEGGINGS = register("netherite_leggings", (new Item.Settings()).armor(ArmorMaterials.NETHERITE, EquipmentType.LEGGINGS).fireproof());
      NETHERITE_BOOTS = register("netherite_boots", (new Item.Settings()).armor(ArmorMaterials.NETHERITE, EquipmentType.BOOTS).fireproof());
      FLINT = register("flint");
      PORKCHOP = register("porkchop", (new Item.Settings()).food(FoodComponents.PORKCHOP));
      COOKED_PORKCHOP = register("cooked_porkchop", (new Item.Settings()).food(FoodComponents.COOKED_PORKCHOP));
      PAINTING = register((String)"painting", (Function)((settings) -> new DecorationItem(EntityType.PAINTING, settings)));
      GOLDEN_APPLE = register("golden_apple", (new Item.Settings()).food(FoodComponents.GOLDEN_APPLE, ConsumableComponents.GOLDEN_APPLE));
      ENCHANTED_GOLDEN_APPLE = register("enchanted_golden_apple", (new Item.Settings()).rarity(Rarity.RARE).food(FoodComponents.ENCHANTED_GOLDEN_APPLE, ConsumableComponents.ENCHANTED_GOLDEN_APPLE).component(DataComponentTypes.ENCHANTMENT_GLINT_OVERRIDE, true));
      OAK_SIGN = register((Block)Blocks.OAK_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.OAK_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      SPRUCE_SIGN = register((Block)Blocks.SPRUCE_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.SPRUCE_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      BIRCH_SIGN = register((Block)Blocks.BIRCH_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.BIRCH_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      JUNGLE_SIGN = register((Block)Blocks.JUNGLE_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.JUNGLE_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      ACACIA_SIGN = register((Block)Blocks.ACACIA_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.ACACIA_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      CHERRY_SIGN = register((Block)Blocks.CHERRY_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.CHERRY_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      DARK_OAK_SIGN = register((Block)Blocks.DARK_OAK_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.DARK_OAK_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      PALE_OAK_SIGN = register((Block)Blocks.PALE_OAK_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.PALE_OAK_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      MANGROVE_SIGN = register((Block)Blocks.MANGROVE_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.MANGROVE_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      BAMBOO_SIGN = register((Block)Blocks.BAMBOO_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.BAMBOO_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      CRIMSON_SIGN = register((Block)Blocks.CRIMSON_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.CRIMSON_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      WARPED_SIGN = register((Block)Blocks.WARPED_SIGN, (BiFunction)((block, settings) -> new SignItem(block, Blocks.WARPED_WALL_SIGN, settings)), (new Item.Settings()).maxCount(16));
      OAK_HANGING_SIGN = register((Block)Blocks.OAK_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.OAK_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      SPRUCE_HANGING_SIGN = register((Block)Blocks.SPRUCE_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.SPRUCE_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      BIRCH_HANGING_SIGN = register((Block)Blocks.BIRCH_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.BIRCH_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      JUNGLE_HANGING_SIGN = register((Block)Blocks.JUNGLE_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.JUNGLE_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      ACACIA_HANGING_SIGN = register((Block)Blocks.ACACIA_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.ACACIA_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      CHERRY_HANGING_SIGN = register((Block)Blocks.CHERRY_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.CHERRY_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      DARK_OAK_HANGING_SIGN = register((Block)Blocks.DARK_OAK_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.DARK_OAK_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      PALE_OAK_HANGING_SIGN = register((Block)Blocks.PALE_OAK_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.PALE_OAK_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      MANGROVE_HANGING_SIGN = register((Block)Blocks.MANGROVE_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.MANGROVE_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      BAMBOO_HANGING_SIGN = register((Block)Blocks.BAMBOO_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.BAMBOO_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      CRIMSON_HANGING_SIGN = register((Block)Blocks.CRIMSON_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.CRIMSON_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      WARPED_HANGING_SIGN = register((Block)Blocks.WARPED_HANGING_SIGN, (BiFunction)((block, settings) -> new HangingSignItem(block, Blocks.WARPED_WALL_HANGING_SIGN, settings)), (new Item.Settings()).maxCount(16));
      BUCKET = register((String)"bucket", (Function)((settings) -> new BucketItem(Fluids.EMPTY, settings)), (new Item.Settings()).maxCount(16));
      WATER_BUCKET = register((String)"water_bucket", (Function)((settings) -> new BucketItem(Fluids.WATER, settings)), (new Item.Settings()).recipeRemainder(BUCKET).maxCount(1));
      LAVA_BUCKET = register((String)"lava_bucket", (Function)((settings) -> new BucketItem(Fluids.LAVA, settings)), (new Item.Settings()).recipeRemainder(BUCKET).maxCount(1));
      POWDER_SNOW_BUCKET = register((String)"powder_snow_bucket", (Function)((settings) -> new PowderSnowBucketItem(Blocks.POWDER_SNOW, SoundEvents.ITEM_BUCKET_EMPTY_POWDER_SNOW, settings)), (new Item.Settings()).maxCount(1).useItemPrefixedTranslationKey());
      SNOWBALL = register("snowball", SnowballItem::new, (new Item.Settings()).maxCount(16));
      LEATHER = register("leather");
      MILK_BUCKET = register("milk_bucket", (new Item.Settings()).recipeRemainder(BUCKET).component(DataComponentTypes.CONSUMABLE, ConsumableComponents.MILK_BUCKET).useRemainder(BUCKET).maxCount(1));
      PUFFERFISH_BUCKET = register((String)"pufferfish_bucket", (Function)((settings) -> new EntityBucketItem(EntityType.PUFFERFISH, Fluids.WATER, SoundEvents.ITEM_BUCKET_EMPTY_FISH, settings)), (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUCKET_ENTITY_DATA, NbtComponent.DEFAULT));
      SALMON_BUCKET = register((String)"salmon_bucket", (Function)((settings) -> new EntityBucketItem(EntityType.SALMON, Fluids.WATER, SoundEvents.ITEM_BUCKET_EMPTY_FISH, settings)), (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUCKET_ENTITY_DATA, NbtComponent.DEFAULT));
      COD_BUCKET = register((String)"cod_bucket", (Function)((settings) -> new EntityBucketItem(EntityType.COD, Fluids.WATER, SoundEvents.ITEM_BUCKET_EMPTY_FISH, settings)), (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUCKET_ENTITY_DATA, NbtComponent.DEFAULT));
      TROPICAL_FISH_BUCKET = register((String)"tropical_fish_bucket", (Function)((settings) -> new EntityBucketItem(EntityType.TROPICAL_FISH, Fluids.WATER, SoundEvents.ITEM_BUCKET_EMPTY_FISH, settings)), (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUCKET_ENTITY_DATA, NbtComponent.DEFAULT));
      AXOLOTL_BUCKET = register((String)"axolotl_bucket", (Function)((settings) -> new EntityBucketItem(EntityType.AXOLOTL, Fluids.WATER, SoundEvents.ITEM_BUCKET_EMPTY_AXOLOTL, settings)), (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUCKET_ENTITY_DATA, NbtComponent.DEFAULT));
      TADPOLE_BUCKET = register((String)"tadpole_bucket", (Function)((settings) -> new EntityBucketItem(EntityType.TADPOLE, Fluids.WATER, SoundEvents.ITEM_BUCKET_EMPTY_TADPOLE, settings)), (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUCKET_ENTITY_DATA, NbtComponent.DEFAULT));
      BRICK = register("brick");
      CLAY_BALL = register("clay_ball");
      DRIED_KELP_BLOCK = register(Blocks.DRIED_KELP_BLOCK);
      PAPER = register("paper");
      BOOK = register("book", (new Item.Settings()).enchantable(1));
      SLIME_BALL = register("slime_ball");
      EGG = register("egg", EggItem::new, (new Item.Settings()).maxCount(16).component(DataComponentTypes.CHICKEN_VARIANT, new LazyRegistryEntryReference(ChickenVariants.TEMPERATE)));
      BLUE_EGG = register("blue_egg", EggItem::new, (new Item.Settings()).maxCount(16).component(DataComponentTypes.CHICKEN_VARIANT, new LazyRegistryEntryReference(ChickenVariants.COLD)));
      BROWN_EGG = register("brown_egg", EggItem::new, (new Item.Settings()).maxCount(16).component(DataComponentTypes.CHICKEN_VARIANT, new LazyRegistryEntryReference(ChickenVariants.WARM)));
      COMPASS = register("compass", CompassItem::new);
      RECOVERY_COMPASS = register("recovery_compass", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      BUNDLE = register("bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      WHITE_BUNDLE = register("white_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      ORANGE_BUNDLE = register("orange_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      MAGENTA_BUNDLE = register("magenta_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      LIGHT_BLUE_BUNDLE = register("light_blue_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      YELLOW_BUNDLE = register("yellow_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      LIME_BUNDLE = register("lime_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      PINK_BUNDLE = register("pink_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      GRAY_BUNDLE = register("gray_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      LIGHT_GRAY_BUNDLE = register("light_gray_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      CYAN_BUNDLE = register("cyan_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      PURPLE_BUNDLE = register("purple_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      BLUE_BUNDLE = register("blue_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      BROWN_BUNDLE = register("brown_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      GREEN_BUNDLE = register("green_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      RED_BUNDLE = register("red_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      BLACK_BUNDLE = register("black_bundle", BundleItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.BUNDLE_CONTENTS, BundleContentsComponent.DEFAULT));
      FISHING_ROD = register("fishing_rod", FishingRodItem::new, (new Item.Settings()).maxDamage(64).enchantable(1));
      CLOCK = register("clock");
      SPYGLASS = register("spyglass", SpyglassItem::new, (new Item.Settings()).maxCount(1));
      GLOWSTONE_DUST = register("glowstone_dust");
      COD = register("cod", (new Item.Settings()).food(FoodComponents.COD));
      SALMON = register("salmon", (new Item.Settings()).food(FoodComponents.SALMON));
      TROPICAL_FISH = register("tropical_fish", (new Item.Settings()).food(FoodComponents.TROPICAL_FISH));
      PUFFERFISH = register("pufferfish", (new Item.Settings()).food(FoodComponents.PUFFERFISH, ConsumableComponents.PUFFERFISH));
      COOKED_COD = register("cooked_cod", (new Item.Settings()).food(FoodComponents.COOKED_COD));
      COOKED_SALMON = register("cooked_salmon", (new Item.Settings()).food(FoodComponents.COOKED_SALMON));
      INK_SAC = register("ink_sac", InkSacItem::new);
      GLOW_INK_SAC = register("glow_ink_sac", GlowInkSacItem::new);
      COCOA_BEANS = register("cocoa_beans", createBlockItemWithUniqueName(Blocks.COCOA));
      WHITE_DYE = register((String)"white_dye", (Function)((settings) -> new DyeItem(DyeColor.WHITE, settings)));
      ORANGE_DYE = register((String)"orange_dye", (Function)((settings) -> new DyeItem(DyeColor.ORANGE, settings)));
      MAGENTA_DYE = register((String)"magenta_dye", (Function)((settings) -> new DyeItem(DyeColor.MAGENTA, settings)));
      LIGHT_BLUE_DYE = register((String)"light_blue_dye", (Function)((settings) -> new DyeItem(DyeColor.LIGHT_BLUE, settings)));
      YELLOW_DYE = register((String)"yellow_dye", (Function)((settings) -> new DyeItem(DyeColor.YELLOW, settings)));
      LIME_DYE = register((String)"lime_dye", (Function)((settings) -> new DyeItem(DyeColor.LIME, settings)));
      PINK_DYE = register((String)"pink_dye", (Function)((settings) -> new DyeItem(DyeColor.PINK, settings)));
      GRAY_DYE = register((String)"gray_dye", (Function)((settings) -> new DyeItem(DyeColor.GRAY, settings)));
      LIGHT_GRAY_DYE = register((String)"light_gray_dye", (Function)((settings) -> new DyeItem(DyeColor.LIGHT_GRAY, settings)));
      CYAN_DYE = register((String)"cyan_dye", (Function)((settings) -> new DyeItem(DyeColor.CYAN, settings)));
      PURPLE_DYE = register((String)"purple_dye", (Function)((settings) -> new DyeItem(DyeColor.PURPLE, settings)));
      BLUE_DYE = register((String)"blue_dye", (Function)((settings) -> new DyeItem(DyeColor.BLUE, settings)));
      BROWN_DYE = register((String)"brown_dye", (Function)((settings) -> new DyeItem(DyeColor.BROWN, settings)));
      GREEN_DYE = register((String)"green_dye", (Function)((settings) -> new DyeItem(DyeColor.GREEN, settings)));
      RED_DYE = register((String)"red_dye", (Function)((settings) -> new DyeItem(DyeColor.RED, settings)));
      BLACK_DYE = register((String)"black_dye", (Function)((settings) -> new DyeItem(DyeColor.BLACK, settings)));
      BONE_MEAL = register("bone_meal", BoneMealItem::new);
      BONE = register("bone");
      SUGAR = register("sugar");
      CAKE = register(Blocks.CAKE, (new Item.Settings()).maxCount(1));
      WHITE_BED = register(Blocks.WHITE_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      ORANGE_BED = register(Blocks.ORANGE_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      MAGENTA_BED = register(Blocks.MAGENTA_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      LIGHT_BLUE_BED = register(Blocks.LIGHT_BLUE_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      YELLOW_BED = register(Blocks.YELLOW_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      LIME_BED = register(Blocks.LIME_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      PINK_BED = register(Blocks.PINK_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      GRAY_BED = register(Blocks.GRAY_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      LIGHT_GRAY_BED = register(Blocks.LIGHT_GRAY_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      CYAN_BED = register(Blocks.CYAN_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      PURPLE_BED = register(Blocks.PURPLE_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      BLUE_BED = register(Blocks.BLUE_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      BROWN_BED = register(Blocks.BROWN_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      GREEN_BED = register(Blocks.GREEN_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      RED_BED = register(Blocks.RED_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      BLACK_BED = register(Blocks.BLACK_BED, BedItem::new, (new Item.Settings()).maxCount(1));
      COOKIE = register("cookie", (new Item.Settings()).food(FoodComponents.COOKIE));
      CRAFTER = register((Block)Blocks.CRAFTER, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      FILLED_MAP = register("filled_map", FilledMapItem::new, (new Item.Settings()).component(DataComponentTypes.MAP_COLOR, MapColorComponent.DEFAULT).component(DataComponentTypes.MAP_DECORATIONS, MapDecorationsComponent.DEFAULT));
      SHEARS = register("shears", ShearsItem::new, (new Item.Settings()).maxDamage(238).component(DataComponentTypes.TOOL, ShearsItem.createToolComponent()));
      MELON_SLICE = register("melon_slice", (new Item.Settings()).food(FoodComponents.MELON_SLICE));
      DRIED_KELP = register("dried_kelp", (new Item.Settings()).food(FoodComponents.DRIED_KELP, ConsumableComponents.DRIED_KELP));
      PUMPKIN_SEEDS = register(ItemKeys.PUMPKIN_SEEDS, createBlockItemWithUniqueName(Blocks.PUMPKIN_STEM));
      MELON_SEEDS = register(ItemKeys.MELON_SEEDS, createBlockItemWithUniqueName(Blocks.MELON_STEM));
      BEEF = register("beef", (new Item.Settings()).food(FoodComponents.BEEF));
      COOKED_BEEF = register("cooked_beef", (new Item.Settings()).food(FoodComponents.COOKED_BEEF));
      CHICKEN = register("chicken", (new Item.Settings()).food(FoodComponents.CHICKEN, ConsumableComponents.RAW_CHICKEN));
      COOKED_CHICKEN = register("cooked_chicken", (new Item.Settings()).food(FoodComponents.COOKED_CHICKEN));
      ROTTEN_FLESH = register("rotten_flesh", (new Item.Settings()).food(FoodComponents.ROTTEN_FLESH, ConsumableComponents.ROTTEN_FLESH));
      ENDER_PEARL = register("ender_pearl", EnderPearlItem::new, (new Item.Settings()).maxCount(16).useCooldown(1.0F));
      BLAZE_ROD = register("blaze_rod");
      GHAST_TEAR = register("ghast_tear");
      GOLD_NUGGET = register("gold_nugget");
      NETHER_WART = register("nether_wart", createBlockItemWithUniqueName(Blocks.NETHER_WART));
      GLASS_BOTTLE = register("glass_bottle", GlassBottleItem::new);
      POTION = register("potion", PotionItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.POTION_CONTENTS, PotionContentsComponent.DEFAULT).component(DataComponentTypes.CONSUMABLE, ConsumableComponents.DRINK).useRemainder(GLASS_BOTTLE));
      SPIDER_EYE = register("spider_eye", (new Item.Settings()).food(FoodComponents.SPIDER_EYE, ConsumableComponents.SPIDER_EYE));
      FERMENTED_SPIDER_EYE = register("fermented_spider_eye");
      BLAZE_POWDER = register("blaze_powder");
      MAGMA_CREAM = register("magma_cream");
      BREWING_STAND = register((Block)Blocks.BREWING_STAND, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      CAULDRON = register(Blocks.CAULDRON, Blocks.WATER_CAULDRON, Blocks.LAVA_CAULDRON, Blocks.POWDER_SNOW_CAULDRON);
      ENDER_EYE = register("ender_eye", EnderEyeItem::new);
      GLISTERING_MELON_SLICE = register("glistering_melon_slice");
      ARMADILLO_SPAWN_EGG = register((String)"armadillo_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ARMADILLO, settings)));
      ALLAY_SPAWN_EGG = register((String)"allay_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ALLAY, settings)));
      AXOLOTL_SPAWN_EGG = register((String)"axolotl_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.AXOLOTL, settings)));
      BAT_SPAWN_EGG = register((String)"bat_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.BAT, settings)));
      BEE_SPAWN_EGG = register((String)"bee_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.BEE, settings)));
      BLAZE_SPAWN_EGG = register((String)"blaze_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.BLAZE, settings)));
      BOGGED_SPAWN_EGG = register((String)"bogged_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.BOGGED, settings)));
      BREEZE_SPAWN_EGG = register((String)"breeze_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.BREEZE, settings)));
      CAT_SPAWN_EGG = register((String)"cat_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.CAT, settings)));
      CAMEL_SPAWN_EGG = register((String)"camel_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.CAMEL, settings)));
      CAVE_SPIDER_SPAWN_EGG = register((String)"cave_spider_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.CAVE_SPIDER, settings)));
      CHICKEN_SPAWN_EGG = register((String)"chicken_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.CHICKEN, settings)));
      COD_SPAWN_EGG = register((String)"cod_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.COD, settings)));
      COW_SPAWN_EGG = register((String)"cow_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.COW, settings)));
      CREEPER_SPAWN_EGG = register((String)"creeper_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.CREEPER, settings)));
      DOLPHIN_SPAWN_EGG = register((String)"dolphin_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.DOLPHIN, settings)));
      DONKEY_SPAWN_EGG = register((String)"donkey_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.DONKEY, settings)));
      DROWNED_SPAWN_EGG = register((String)"drowned_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.DROWNED, settings)));
      ELDER_GUARDIAN_SPAWN_EGG = register((String)"elder_guardian_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ELDER_GUARDIAN, settings)));
      ENDER_DRAGON_SPAWN_EGG = register((String)"ender_dragon_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ENDER_DRAGON, settings)));
      ENDERMAN_SPAWN_EGG = register((String)"enderman_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ENDERMAN, settings)));
      ENDERMITE_SPAWN_EGG = register((String)"endermite_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ENDERMITE, settings)));
      EVOKER_SPAWN_EGG = register((String)"evoker_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.EVOKER, settings)));
      FOX_SPAWN_EGG = register((String)"fox_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.FOX, settings)));
      FROG_SPAWN_EGG = register((String)"frog_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.FROG, settings)));
      GHAST_SPAWN_EGG = register((String)"ghast_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.GHAST, settings)));
      GLOW_SQUID_SPAWN_EGG = register((String)"glow_squid_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.GLOW_SQUID, settings)));
      GOAT_SPAWN_EGG = register((String)"goat_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.GOAT, settings)));
      GUARDIAN_SPAWN_EGG = register((String)"guardian_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.GUARDIAN, settings)));
      HOGLIN_SPAWN_EGG = register((String)"hoglin_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.HOGLIN, settings)));
      HORSE_SPAWN_EGG = register((String)"horse_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.HORSE, settings)));
      HUSK_SPAWN_EGG = register((String)"husk_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.HUSK, settings)));
      IRON_GOLEM_SPAWN_EGG = register((String)"iron_golem_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.IRON_GOLEM, settings)));
      LLAMA_SPAWN_EGG = register((String)"llama_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.LLAMA, settings)));
      MAGMA_CUBE_SPAWN_EGG = register((String)"magma_cube_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.MAGMA_CUBE, settings)));
      MOOSHROOM_SPAWN_EGG = register((String)"mooshroom_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.MOOSHROOM, settings)));
      MULE_SPAWN_EGG = register((String)"mule_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.MULE, settings)));
      OCELOT_SPAWN_EGG = register((String)"ocelot_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.OCELOT, settings)));
      PANDA_SPAWN_EGG = register((String)"panda_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.PANDA, settings)));
      PARROT_SPAWN_EGG = register((String)"parrot_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.PARROT, settings)));
      PHANTOM_SPAWN_EGG = register((String)"phantom_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.PHANTOM, settings)));
      PIG_SPAWN_EGG = register((String)"pig_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.PIG, settings)));
      PIGLIN_SPAWN_EGG = register((String)"piglin_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.PIGLIN, settings)));
      PIGLIN_BRUTE_SPAWN_EGG = register((String)"piglin_brute_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.PIGLIN_BRUTE, settings)));
      PILLAGER_SPAWN_EGG = register((String)"pillager_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.PILLAGER, settings)));
      POLAR_BEAR_SPAWN_EGG = register((String)"polar_bear_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.POLAR_BEAR, settings)));
      PUFFERFISH_SPAWN_EGG = register((String)"pufferfish_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.PUFFERFISH, settings)));
      RABBIT_SPAWN_EGG = register((String)"rabbit_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.RABBIT, settings)));
      RAVAGER_SPAWN_EGG = register((String)"ravager_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.RAVAGER, settings)));
      SALMON_SPAWN_EGG = register((String)"salmon_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SALMON, settings)));
      SHEEP_SPAWN_EGG = register((String)"sheep_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SHEEP, settings)));
      SHULKER_SPAWN_EGG = register((String)"shulker_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SHULKER, settings)));
      SILVERFISH_SPAWN_EGG = register((String)"silverfish_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SILVERFISH, settings)));
      SKELETON_SPAWN_EGG = register((String)"skeleton_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SKELETON, settings)));
      SKELETON_HORSE_SPAWN_EGG = register((String)"skeleton_horse_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SKELETON_HORSE, settings)));
      SLIME_SPAWN_EGG = register((String)"slime_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SLIME, settings)));
      SNIFFER_SPAWN_EGG = register((String)"sniffer_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SNIFFER, settings)));
      SNOW_GOLEM_SPAWN_EGG = register((String)"snow_golem_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SNOW_GOLEM, settings)));
      SPIDER_SPAWN_EGG = register((String)"spider_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SPIDER, settings)));
      SQUID_SPAWN_EGG = register((String)"squid_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.SQUID, settings)));
      STRAY_SPAWN_EGG = register((String)"stray_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.STRAY, settings)));
      STRIDER_SPAWN_EGG = register((String)"strider_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.STRIDER, settings)));
      TADPOLE_SPAWN_EGG = register((String)"tadpole_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.TADPOLE, settings)));
      TRADER_LLAMA_SPAWN_EGG = register((String)"trader_llama_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.TRADER_LLAMA, settings)));
      TROPICAL_FISH_SPAWN_EGG = register((String)"tropical_fish_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.TROPICAL_FISH, settings)));
      TURTLE_SPAWN_EGG = register((String)"turtle_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.TURTLE, settings)));
      VEX_SPAWN_EGG = register((String)"vex_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.VEX, settings)));
      VILLAGER_SPAWN_EGG = register((String)"villager_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.VILLAGER, settings)));
      VINDICATOR_SPAWN_EGG = register((String)"vindicator_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.VINDICATOR, settings)));
      WANDERING_TRADER_SPAWN_EGG = register((String)"wandering_trader_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.WANDERING_TRADER, settings)));
      WARDEN_SPAWN_EGG = register((String)"warden_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.WARDEN, settings)));
      WITCH_SPAWN_EGG = register((String)"witch_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.WITCH, settings)));
      WITHER_SPAWN_EGG = register((String)"wither_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.WITHER, settings)));
      WITHER_SKELETON_SPAWN_EGG = register((String)"wither_skeleton_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.WITHER_SKELETON, settings)));
      WOLF_SPAWN_EGG = register((String)"wolf_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.WOLF, settings)));
      ZOGLIN_SPAWN_EGG = register((String)"zoglin_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ZOGLIN, settings)));
      CREAKING_SPAWN_EGG = register((String)"creaking_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.CREAKING, settings)));
      ZOMBIE_SPAWN_EGG = register((String)"zombie_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ZOMBIE, settings)));
      ZOMBIE_HORSE_SPAWN_EGG = register((String)"zombie_horse_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ZOMBIE_HORSE, settings)));
      ZOMBIE_VILLAGER_SPAWN_EGG = register((String)"zombie_villager_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ZOMBIE_VILLAGER, settings)));
      ZOMBIFIED_PIGLIN_SPAWN_EGG = register((String)"zombified_piglin_spawn_egg", (Function)((settings) -> new SpawnEggItem(EntityType.ZOMBIFIED_PIGLIN, settings)));
      EXPERIENCE_BOTTLE = register("experience_bottle", ExperienceBottleItem::new, (new Item.Settings()).rarity(Rarity.UNCOMMON).component(DataComponentTypes.ENCHANTMENT_GLINT_OVERRIDE, true));
      FIRE_CHARGE = register("fire_charge", FireChargeItem::new);
      WIND_CHARGE = register("wind_charge", WindChargeItem::new, (new Item.Settings()).useCooldown(0.5F));
      WRITABLE_BOOK = register("writable_book", WritableBookItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.WRITABLE_BOOK_CONTENT, WritableBookContentComponent.DEFAULT));
      WRITTEN_BOOK = register("written_book", WrittenBookItem::new, (new Item.Settings()).maxCount(16).component(DataComponentTypes.ENCHANTMENT_GLINT_OVERRIDE, true));
      BREEZE_ROD = register("breeze_rod");
      MACE = register("mace", MaceItem::new, (new Item.Settings()).rarity(Rarity.EPIC).maxDamage(500).component(DataComponentTypes.TOOL, MaceItem.createToolComponent()).repairable(BREEZE_ROD).attributeModifiers(MaceItem.createAttributeModifiers()).enchantable(15).component(DataComponentTypes.WEAPON, new WeaponComponent(1)));
      ITEM_FRAME = register((String)"item_frame", (Function)((settings) -> new ItemFrameItem(EntityType.ITEM_FRAME, settings)));
      GLOW_ITEM_FRAME = register((String)"glow_item_frame", (Function)((settings) -> new ItemFrameItem(EntityType.GLOW_ITEM_FRAME, settings)));
      FLOWER_POT = register(Blocks.FLOWER_POT);
      CARROT = register("carrot", createBlockItemWithUniqueName(Blocks.CARROTS), (new Item.Settings()).food(FoodComponents.CARROT));
      POTATO = register("potato", createBlockItemWithUniqueName(Blocks.POTATOES), (new Item.Settings()).food(FoodComponents.POTATO));
      BAKED_POTATO = register("baked_potato", (new Item.Settings()).food(FoodComponents.BAKED_POTATO));
      POISONOUS_POTATO = register("poisonous_potato", (new Item.Settings()).food(FoodComponents.POISONOUS_POTATO, ConsumableComponents.POISONOUS_POTATO));
      MAP = register("map", EmptyMapItem::new);
      GOLDEN_CARROT = register("golden_carrot", (new Item.Settings()).food(FoodComponents.GOLDEN_CARROT));
      SKELETON_SKULL = register((Block)Blocks.SKELETON_SKULL, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.SKELETON_WALL_SKULL, Direction.DOWN, settings)), (new Item.Settings()).rarity(Rarity.UNCOMMON).equippableUnswappable(EquipmentSlot.HEAD));
      WITHER_SKELETON_SKULL = register((Block)Blocks.WITHER_SKELETON_SKULL, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.WITHER_SKELETON_WALL_SKULL, Direction.DOWN, settings)), (new Item.Settings()).rarity(Rarity.RARE).equippableUnswappable(EquipmentSlot.HEAD));
      PLAYER_HEAD = register((Block)Blocks.PLAYER_HEAD, (BiFunction)((block, settings) -> new PlayerHeadItem(block, Blocks.PLAYER_WALL_HEAD, settings)), (new Item.Settings()).rarity(Rarity.UNCOMMON).equippableUnswappable(EquipmentSlot.HEAD));
      ZOMBIE_HEAD = register((Block)Blocks.ZOMBIE_HEAD, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.ZOMBIE_WALL_HEAD, Direction.DOWN, settings)), (new Item.Settings()).rarity(Rarity.UNCOMMON).equippableUnswappable(EquipmentSlot.HEAD));
      CREEPER_HEAD = register((Block)Blocks.CREEPER_HEAD, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.CREEPER_WALL_HEAD, Direction.DOWN, settings)), (new Item.Settings()).rarity(Rarity.UNCOMMON).equippableUnswappable(EquipmentSlot.HEAD));
      DRAGON_HEAD = register((Block)Blocks.DRAGON_HEAD, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.DRAGON_WALL_HEAD, Direction.DOWN, settings)), (new Item.Settings()).rarity(Rarity.EPIC).equippableUnswappable(EquipmentSlot.HEAD));
      PIGLIN_HEAD = register((Block)Blocks.PIGLIN_HEAD, (BiFunction)((block, settings) -> new VerticallyAttachableBlockItem(block, Blocks.PIGLIN_WALL_HEAD, Direction.DOWN, settings)), (new Item.Settings()).rarity(Rarity.UNCOMMON).equippableUnswappable(EquipmentSlot.HEAD));
      NETHER_STAR = register("nether_star", (new Item.Settings()).rarity(Rarity.RARE).component(DataComponentTypes.ENCHANTMENT_GLINT_OVERRIDE, true).component(DataComponentTypes.DAMAGE_RESISTANT, new DamageResistantComponent(DamageTypeTags.IS_EXPLOSION)));
      PUMPKIN_PIE = register("pumpkin_pie", (new Item.Settings()).food(FoodComponents.PUMPKIN_PIE));
      FIREWORK_ROCKET = register("firework_rocket", FireworkRocketItem::new, (new Item.Settings()).component(DataComponentTypes.FIREWORKS, new FireworksComponent(1, List.of())));
      FIREWORK_STAR = register("firework_star");
      ENCHANTED_BOOK = register("enchanted_book", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).component(DataComponentTypes.STORED_ENCHANTMENTS, ItemEnchantmentsComponent.DEFAULT).component(DataComponentTypes.ENCHANTMENT_GLINT_OVERRIDE, true));
      NETHER_BRICK = register("nether_brick");
      RESIN_BRICK = register("resin_brick", (new Item.Settings()).trimMaterial(ArmorTrimMaterials.RESIN));
      PRISMARINE_SHARD = register("prismarine_shard");
      PRISMARINE_CRYSTALS = register("prismarine_crystals");
      RABBIT = register("rabbit", (new Item.Settings()).food(FoodComponents.RABBIT));
      COOKED_RABBIT = register("cooked_rabbit", (new Item.Settings()).food(FoodComponents.COOKED_RABBIT));
      RABBIT_STEW = register("rabbit_stew", (new Item.Settings()).maxCount(1).food(FoodComponents.RABBIT_STEW).useRemainder(BOWL));
      RABBIT_FOOT = register("rabbit_foot");
      RABBIT_HIDE = register("rabbit_hide");
      ARMOR_STAND = register("armor_stand", ArmorStandItem::new, (new Item.Settings()).maxCount(16));
      IRON_HORSE_ARMOR = register("iron_horse_armor", (new Item.Settings()).horseArmor(ArmorMaterials.IRON));
      GOLDEN_HORSE_ARMOR = register("golden_horse_armor", (new Item.Settings()).horseArmor(ArmorMaterials.GOLD));
      DIAMOND_HORSE_ARMOR = register("diamond_horse_armor", (new Item.Settings()).horseArmor(ArmorMaterials.DIAMOND));
      LEATHER_HORSE_ARMOR = register("leather_horse_armor", (new Item.Settings()).horseArmor(ArmorMaterials.LEATHER));
      LEAD = register("lead", LeadItem::new);
      NAME_TAG = register("name_tag", NameTagItem::new);
      COMMAND_BLOCK_MINECART = register((String)"command_block_minecart", (Function)((settings) -> new MinecartItem(EntityType.COMMAND_BLOCK_MINECART, settings)), (new Item.Settings()).maxCount(1).rarity(Rarity.EPIC));
      MUTTON = register("mutton", (new Item.Settings()).food(FoodComponents.MUTTON));
      COOKED_MUTTON = register("cooked_mutton", (new Item.Settings()).food(FoodComponents.COOKED_MUTTON));
      WHITE_BANNER = register((Block)Blocks.WHITE_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.WHITE_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      ORANGE_BANNER = register((Block)Blocks.ORANGE_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.ORANGE_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      MAGENTA_BANNER = register((Block)Blocks.MAGENTA_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.MAGENTA_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      LIGHT_BLUE_BANNER = register((Block)Blocks.LIGHT_BLUE_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.LIGHT_BLUE_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      YELLOW_BANNER = register((Block)Blocks.YELLOW_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.YELLOW_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      LIME_BANNER = register((Block)Blocks.LIME_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.LIME_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      PINK_BANNER = register((Block)Blocks.PINK_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.PINK_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      GRAY_BANNER = register((Block)Blocks.GRAY_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.GRAY_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      LIGHT_GRAY_BANNER = register((Block)Blocks.LIGHT_GRAY_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.LIGHT_GRAY_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      CYAN_BANNER = register((Block)Blocks.CYAN_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.CYAN_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      PURPLE_BANNER = register((Block)Blocks.PURPLE_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.PURPLE_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      BLUE_BANNER = register((Block)Blocks.BLUE_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.BLUE_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      BROWN_BANNER = register((Block)Blocks.BROWN_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.BROWN_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      GREEN_BANNER = register((Block)Blocks.GREEN_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.GREEN_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      RED_BANNER = register((Block)Blocks.RED_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.RED_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      BLACK_BANNER = register((Block)Blocks.BLACK_BANNER, (BiFunction)((block, settings) -> new BannerItem(block, Blocks.BLACK_WALL_BANNER, settings)), (new Item.Settings()).maxCount(16).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT));
      END_CRYSTAL = register("end_crystal", EndCrystalItem::new, (new Item.Settings()).component(DataComponentTypes.ENCHANTMENT_GLINT_OVERRIDE, true));
      CHORUS_FRUIT = register("chorus_fruit", (new Item.Settings()).food(FoodComponents.CHORUS_FRUIT, ConsumableComponents.CHORUS_FRUIT).useCooldown(1.0F));
      POPPED_CHORUS_FRUIT = register("popped_chorus_fruit");
      TORCHFLOWER_SEEDS = register("torchflower_seeds", createBlockItemWithUniqueName(Blocks.TORCHFLOWER_CROP));
      PITCHER_POD = register("pitcher_pod", createBlockItemWithUniqueName(Blocks.PITCHER_CROP));
      BEETROOT = register("beetroot", (new Item.Settings()).food(FoodComponents.BEETROOT));
      BEETROOT_SEEDS = register("beetroot_seeds", createBlockItemWithUniqueName(Blocks.BEETROOTS));
      BEETROOT_SOUP = register("beetroot_soup", (new Item.Settings()).maxCount(1).food(FoodComponents.BEETROOT_SOUP).useRemainder(BOWL));
      DRAGON_BREATH = register("dragon_breath", (new Item.Settings()).recipeRemainder(GLASS_BOTTLE).rarity(Rarity.UNCOMMON));
      SPLASH_POTION = register("splash_potion", SplashPotionItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.POTION_CONTENTS, PotionContentsComponent.DEFAULT));
      SPECTRAL_ARROW = register("spectral_arrow", SpectralArrowItem::new);
      TIPPED_ARROW = register("tipped_arrow", TippedArrowItem::new, (new Item.Settings()).component(DataComponentTypes.POTION_CONTENTS, PotionContentsComponent.DEFAULT).component(DataComponentTypes.POTION_DURATION_SCALE, 0.125F));
      LINGERING_POTION = register("lingering_potion", LingeringPotionItem::new, (new Item.Settings()).maxCount(1).component(DataComponentTypes.POTION_CONTENTS, PotionContentsComponent.DEFAULT).component(DataComponentTypes.POTION_DURATION_SCALE, 0.25F));
      SHIELD = register("shield", ShieldItem::new, (new Item.Settings()).maxDamage(336).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT).repairable(ItemTags.WOODEN_TOOL_MATERIALS).equippableUnswappable(EquipmentSlot.OFFHAND).component(DataComponentTypes.BLOCKS_ATTACKS, new BlocksAttacksComponent(0.25F, 1.0F, List.of(new BlocksAttacksComponent.DamageReduction(90.0F, Optional.empty(), 0.0F, 1.0F)), new BlocksAttacksComponent.ItemDamage(3.0F, 1.0F, 1.0F), Optional.of(DamageTypeTags.BYPASSES_SHIELD), Optional.of(SoundEvents.ITEM_SHIELD_BLOCK), Optional.of(SoundEvents.ITEM_SHIELD_BREAK))).component(DataComponentTypes.BREAK_SOUND, SoundEvents.ITEM_SHIELD_BREAK));
      TOTEM_OF_UNDYING = register("totem_of_undying", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).component(DataComponentTypes.DEATH_PROTECTION, DeathProtectionComponent.TOTEM_OF_UNDYING));
      SHULKER_SHELL = register("shulker_shell");
      IRON_NUGGET = register("iron_nugget");
      KNOWLEDGE_BOOK = register("knowledge_book", KnowledgeBookItem::new, (new Item.Settings()).maxCount(1).rarity(Rarity.EPIC).component(DataComponentTypes.RECIPES, List.of()));
      DEBUG_STICK = register("debug_stick", DebugStickItem::new, (new Item.Settings()).maxCount(1).rarity(Rarity.EPIC).component(DataComponentTypes.DEBUG_STICK_STATE, DebugStickStateComponent.DEFAULT).component(DataComponentTypes.ENCHANTMENT_GLINT_OVERRIDE, true));
      MUSIC_DISC_13 = register("music_disc_13", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.THIRTEEN));
      MUSIC_DISC_CAT = register("music_disc_cat", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.CAT));
      MUSIC_DISC_BLOCKS = register("music_disc_blocks", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.BLOCKS));
      MUSIC_DISC_CHIRP = register("music_disc_chirp", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.CHIRP));
      MUSIC_DISC_CREATOR = register("music_disc_creator", (new Item.Settings()).maxCount(1).rarity(Rarity.RARE).jukeboxPlayable(JukeboxSongs.CREATOR));
      MUSIC_DISC_CREATOR_MUSIC_BOX = register("music_disc_creator_music_box", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.CREATOR_MUSIC_BOX));
      MUSIC_DISC_FAR = register("music_disc_far", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.FAR));
      MUSIC_DISC_MALL = register("music_disc_mall", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.MALL));
      MUSIC_DISC_MELLOHI = register("music_disc_mellohi", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.MELLOHI));
      MUSIC_DISC_STAL = register("music_disc_stal", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.STAL));
      MUSIC_DISC_STRAD = register("music_disc_strad", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.STRAD));
      MUSIC_DISC_WARD = register("music_disc_ward", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.WARD));
      MUSIC_DISC_11 = register("music_disc_11", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.ELEVEN));
      MUSIC_DISC_WAIT = register("music_disc_wait", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.WAIT));
      MUSIC_DISC_OTHERSIDE = register("music_disc_otherside", (new Item.Settings()).maxCount(1).rarity(Rarity.RARE).jukeboxPlayable(JukeboxSongs.OTHERSIDE));
      MUSIC_DISC_RELIC = register("music_disc_relic", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.RELIC));
      MUSIC_DISC_5 = register("music_disc_5", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.FIVE));
      MUSIC_DISC_PIGSTEP = register("music_disc_pigstep", (new Item.Settings()).maxCount(1).rarity(Rarity.RARE).jukeboxPlayable(JukeboxSongs.PIGSTEP));
      MUSIC_DISC_PRECIPICE = register("music_disc_precipice", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).jukeboxPlayable(JukeboxSongs.PRECIPICE));
      DISC_FRAGMENT_5 = register("disc_fragment_5", DiscFragmentItem::new, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      TRIDENT = register("trident", TridentItem::new, (new Item.Settings()).rarity(Rarity.RARE).maxDamage(250).attributeModifiers(TridentItem.createAttributeModifiers()).component(DataComponentTypes.TOOL, TridentItem.createToolComponent()).enchantable(1).component(DataComponentTypes.WEAPON, new WeaponComponent(1)));
      NAUTILUS_SHELL = register("nautilus_shell", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      HEART_OF_THE_SEA = register("heart_of_the_sea", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      CROSSBOW = register("crossbow", CrossbowItem::new, (new Item.Settings()).maxCount(1).maxDamage(465).component(DataComponentTypes.CHARGED_PROJECTILES, ChargedProjectilesComponent.DEFAULT).enchantable(1));
      SUSPICIOUS_STEW = register("suspicious_stew", (new Item.Settings()).maxCount(1).food(FoodComponents.SUSPICIOUS_STEW).component(DataComponentTypes.SUSPICIOUS_STEW_EFFECTS, SuspiciousStewEffectsComponent.DEFAULT).useRemainder(BOWL));
      LOOM = register(Blocks.LOOM);
      FLOWER_BANNER_PATTERN = register("flower_banner_pattern", (new Item.Settings()).maxCount(1).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.FLOWER_PATTERN_ITEM));
      CREEPER_BANNER_PATTERN = register("creeper_banner_pattern", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.CREEPER_PATTERN_ITEM));
      SKULL_BANNER_PATTERN = register("skull_banner_pattern", (new Item.Settings()).maxCount(1).rarity(Rarity.RARE).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.SKULL_PATTERN_ITEM));
      MOJANG_BANNER_PATTERN = register("mojang_banner_pattern", (new Item.Settings()).maxCount(1).rarity(Rarity.RARE).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.MOJANG_PATTERN_ITEM));
      GLOBE_BANNER_PATTERN = register("globe_banner_pattern", (new Item.Settings()).maxCount(1).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.GLOBE_PATTERN_ITEM));
      PIGLIN_BANNER_PATTERN = register("piglin_banner_pattern", (new Item.Settings()).maxCount(1).rarity(Rarity.UNCOMMON).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.PIGLIN_PATTERN_ITEM));
      FLOW_BANNER_PATTERN = register("flow_banner_pattern", (new Item.Settings()).maxCount(1).rarity(Rarity.RARE).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.FLOW_PATTERN_ITEM));
      GUSTER_BANNER_PATTERN = register("guster_banner_pattern", (new Item.Settings()).maxCount(1).rarity(Rarity.RARE).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.GUSTER_PATTERN_ITEM));
      FIELD_MASONED_BANNER_PATTERN = register("field_masoned_banner_pattern", (new Item.Settings()).maxCount(1).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.FIELD_MASONED_PATTERN_ITEM));
      BORDURE_INDENTED_BANNER_PATTERN = register("bordure_indented_banner_pattern", (new Item.Settings()).maxCount(1).component(DataComponentTypes.PROVIDES_BANNER_PATTERNS, BannerPatternTags.BORDURE_INDENTED_PATTERN_ITEM));
      GOAT_HORN = register("goat_horn", GoatHornItem::new, (new Item.Settings()).rarity(Rarity.UNCOMMON).maxCount(1).component(DataComponentTypes.INSTRUMENT, new InstrumentComponent(Instruments.PONDER_GOAT_HORN)));
      COMPOSTER = register(Blocks.COMPOSTER);
      BARREL = register((Block)Blocks.BARREL, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      SMOKER = register((Block)Blocks.SMOKER, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      BLAST_FURNACE = register((Block)Blocks.BLAST_FURNACE, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      CARTOGRAPHY_TABLE = register(Blocks.CARTOGRAPHY_TABLE);
      FLETCHING_TABLE = register(Blocks.FLETCHING_TABLE);
      GRINDSTONE = register(Blocks.GRINDSTONE);
      SMITHING_TABLE = register(Blocks.SMITHING_TABLE);
      STONECUTTER = register(Blocks.STONECUTTER);
      BELL = register(Blocks.BELL);
      LANTERN = register(Blocks.LANTERN);
      SOUL_LANTERN = register(Blocks.SOUL_LANTERN);
      SWEET_BERRIES = register("sweet_berries", createBlockItemWithUniqueName(Blocks.SWEET_BERRY_BUSH), (new Item.Settings()).food(FoodComponents.SWEET_BERRIES));
      GLOW_BERRIES = register("glow_berries", createBlockItemWithUniqueName(Blocks.CAVE_VINES), (new Item.Settings()).food(FoodComponents.GLOW_BERRIES));
      CAMPFIRE = register((Block)Blocks.CAMPFIRE, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      SOUL_CAMPFIRE = register((Block)Blocks.SOUL_CAMPFIRE, (UnaryOperator)((settings) -> settings.component(DataComponentTypes.CONTAINER, ContainerComponent.DEFAULT)));
      SHROOMLIGHT = register(Blocks.SHROOMLIGHT);
      HONEYCOMB = register("honeycomb", HoneycombItem::new);
      BEE_NEST = register(Blocks.BEE_NEST, (new Item.Settings()).component(DataComponentTypes.BEES, BeesComponent.DEFAULT).component(DataComponentTypes.BLOCK_STATE, BlockStateComponent.DEFAULT.with(BeehiveBlock.HONEY_LEVEL, 0)));
      BEEHIVE = register(Blocks.BEEHIVE, (new Item.Settings()).component(DataComponentTypes.BEES, BeesComponent.DEFAULT).component(DataComponentTypes.BLOCK_STATE, BlockStateComponent.DEFAULT.with(BeehiveBlock.HONEY_LEVEL, 0)));
      HONEY_BOTTLE = register("honey_bottle", (new Item.Settings()).recipeRemainder(GLASS_BOTTLE).food(FoodComponents.HONEY_BOTTLE, ConsumableComponents.HONEY_BOTTLE).useRemainder(GLASS_BOTTLE).maxCount(16));
      HONEYCOMB_BLOCK = register(Blocks.HONEYCOMB_BLOCK);
      LODESTONE = register(Blocks.LODESTONE);
      CRYING_OBSIDIAN = register(Blocks.CRYING_OBSIDIAN);
      BLACKSTONE = register(Blocks.BLACKSTONE);
      BLACKSTONE_SLAB = register(Blocks.BLACKSTONE_SLAB);
      BLACKSTONE_STAIRS = register(Blocks.BLACKSTONE_STAIRS);
      GILDED_BLACKSTONE = register(Blocks.GILDED_BLACKSTONE);
      POLISHED_BLACKSTONE = register(Blocks.POLISHED_BLACKSTONE);
      POLISHED_BLACKSTONE_SLAB = register(Blocks.POLISHED_BLACKSTONE_SLAB);
      POLISHED_BLACKSTONE_STAIRS = register(Blocks.POLISHED_BLACKSTONE_STAIRS);
      CHISELED_POLISHED_BLACKSTONE = register(Blocks.CHISELED_POLISHED_BLACKSTONE);
      POLISHED_BLACKSTONE_BRICKS = register(Blocks.POLISHED_BLACKSTONE_BRICKS);
      POLISHED_BLACKSTONE_BRICK_SLAB = register(Blocks.POLISHED_BLACKSTONE_BRICK_SLAB);
      POLISHED_BLACKSTONE_BRICK_STAIRS = register(Blocks.POLISHED_BLACKSTONE_BRICK_STAIRS);
      CRACKED_POLISHED_BLACKSTONE_BRICKS = register(Blocks.CRACKED_POLISHED_BLACKSTONE_BRICKS);
      RESPAWN_ANCHOR = register(Blocks.RESPAWN_ANCHOR);
      CANDLE = register(Blocks.CANDLE);
      WHITE_CANDLE = register(Blocks.WHITE_CANDLE);
      ORANGE_CANDLE = register(Blocks.ORANGE_CANDLE);
      MAGENTA_CANDLE = register(Blocks.MAGENTA_CANDLE);
      LIGHT_BLUE_CANDLE = register(Blocks.LIGHT_BLUE_CANDLE);
      YELLOW_CANDLE = register(Blocks.YELLOW_CANDLE);
      LIME_CANDLE = register(Blocks.LIME_CANDLE);
      PINK_CANDLE = register(Blocks.PINK_CANDLE);
      GRAY_CANDLE = register(Blocks.GRAY_CANDLE);
      LIGHT_GRAY_CANDLE = register(Blocks.LIGHT_GRAY_CANDLE);
      CYAN_CANDLE = register(Blocks.CYAN_CANDLE);
      PURPLE_CANDLE = register(Blocks.PURPLE_CANDLE);
      BLUE_CANDLE = register(Blocks.BLUE_CANDLE);
      BROWN_CANDLE = register(Blocks.BROWN_CANDLE);
      GREEN_CANDLE = register(Blocks.GREEN_CANDLE);
      RED_CANDLE = register(Blocks.RED_CANDLE);
      BLACK_CANDLE = register(Blocks.BLACK_CANDLE);
      SMALL_AMETHYST_BUD = register(Blocks.SMALL_AMETHYST_BUD);
      MEDIUM_AMETHYST_BUD = register(Blocks.MEDIUM_AMETHYST_BUD);
      LARGE_AMETHYST_BUD = register(Blocks.LARGE_AMETHYST_BUD);
      AMETHYST_CLUSTER = register(Blocks.AMETHYST_CLUSTER);
      POINTED_DRIPSTONE = register(Blocks.POINTED_DRIPSTONE);
      OCHRE_FROGLIGHT = register(Blocks.OCHRE_FROGLIGHT);
      VERDANT_FROGLIGHT = register(Blocks.VERDANT_FROGLIGHT);
      PEARLESCENT_FROGLIGHT = register(Blocks.PEARLESCENT_FROGLIGHT);
      FROGSPAWN = register(Blocks.FROGSPAWN, PlaceableOnWaterItem::new);
      ECHO_SHARD = register("echo_shard", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      BRUSH = register("brush", BrushItem::new, (new Item.Settings()).maxDamage(64));
      NETHERITE_UPGRADE_SMITHING_TEMPLATE = register("netherite_upgrade_smithing_template", SmithingTemplateItem::createNetheriteUpgrade, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SENTRY_ARMOR_TRIM_SMITHING_TEMPLATE = register("sentry_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      DUNE_ARMOR_TRIM_SMITHING_TEMPLATE = register("dune_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      COAST_ARMOR_TRIM_SMITHING_TEMPLATE = register("coast_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      WILD_ARMOR_TRIM_SMITHING_TEMPLATE = register("wild_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      WARD_ARMOR_TRIM_SMITHING_TEMPLATE = register("ward_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.RARE));
      EYE_ARMOR_TRIM_SMITHING_TEMPLATE = register("eye_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.RARE));
      VEX_ARMOR_TRIM_SMITHING_TEMPLATE = register("vex_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.RARE));
      TIDE_ARMOR_TRIM_SMITHING_TEMPLATE = register("tide_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SNOUT_ARMOR_TRIM_SMITHING_TEMPLATE = register("snout_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      RIB_ARMOR_TRIM_SMITHING_TEMPLATE = register("rib_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SPIRE_ARMOR_TRIM_SMITHING_TEMPLATE = register("spire_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.RARE));
      WAYFINDER_ARMOR_TRIM_SMITHING_TEMPLATE = register("wayfinder_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SHAPER_ARMOR_TRIM_SMITHING_TEMPLATE = register("shaper_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SILENCE_ARMOR_TRIM_SMITHING_TEMPLATE = register("silence_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.EPIC));
      RAISER_ARMOR_TRIM_SMITHING_TEMPLATE = register("raiser_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      HOST_ARMOR_TRIM_SMITHING_TEMPLATE = register("host_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      FLOW_ARMOR_TRIM_SMITHING_TEMPLATE = register("flow_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      BOLT_ARMOR_TRIM_SMITHING_TEMPLATE = register("bolt_armor_trim_smithing_template", SmithingTemplateItem::of, (new Item.Settings()).rarity(Rarity.UNCOMMON));
      ANGLER_POTTERY_SHERD = register("angler_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      ARCHER_POTTERY_SHERD = register("archer_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      ARMS_UP_POTTERY_SHERD = register("arms_up_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      BLADE_POTTERY_SHERD = register("blade_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      BREWER_POTTERY_SHERD = register("brewer_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      BURN_POTTERY_SHERD = register("burn_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      DANGER_POTTERY_SHERD = register("danger_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      EXPLORER_POTTERY_SHERD = register("explorer_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      FLOW_POTTERY_SHERD = register("flow_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      FRIEND_POTTERY_SHERD = register("friend_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      GUSTER_POTTERY_SHERD = register("guster_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      HEART_POTTERY_SHERD = register("heart_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      HEARTBREAK_POTTERY_SHERD = register("heartbreak_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      HOWL_POTTERY_SHERD = register("howl_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      MINER_POTTERY_SHERD = register("miner_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      MOURNER_POTTERY_SHERD = register("mourner_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      PLENTY_POTTERY_SHERD = register("plenty_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      PRIZE_POTTERY_SHERD = register("prize_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SCRAPE_POTTERY_SHERD = register("scrape_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SHEAF_POTTERY_SHERD = register("sheaf_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SHELTER_POTTERY_SHERD = register("shelter_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SKULL_POTTERY_SHERD = register("skull_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      SNORT_POTTERY_SHERD = register("snort_pottery_sherd", (new Item.Settings()).rarity(Rarity.UNCOMMON));
      COPPER_GRATE = register(Blocks.COPPER_GRATE);
      EXPOSED_COPPER_GRATE = register(Blocks.EXPOSED_COPPER_GRATE);
      WEATHERED_COPPER_GRATE = register(Blocks.WEATHERED_COPPER_GRATE);
      OXIDIZED_COPPER_GRATE = register(Blocks.OXIDIZED_COPPER_GRATE);
      WAXED_COPPER_GRATE = register(Blocks.WAXED_COPPER_GRATE);
      WAXED_EXPOSED_COPPER_GRATE = register(Blocks.WAXED_EXPOSED_COPPER_GRATE);
      WAXED_WEATHERED_COPPER_GRATE = register(Blocks.WAXED_WEATHERED_COPPER_GRATE);
      WAXED_OXIDIZED_COPPER_GRATE = register(Blocks.WAXED_OXIDIZED_COPPER_GRATE);
      COPPER_BULB = register(Blocks.COPPER_BULB);
      EXPOSED_COPPER_BULB = register(Blocks.EXPOSED_COPPER_BULB);
      WEATHERED_COPPER_BULB = register(Blocks.WEATHERED_COPPER_BULB);
      OXIDIZED_COPPER_BULB = register(Blocks.OXIDIZED_COPPER_BULB);
      WAXED_COPPER_BULB = register(Blocks.WAXED_COPPER_BULB);
      WAXED_EXPOSED_COPPER_BULB = register(Blocks.WAXED_EXPOSED_COPPER_BULB);
      WAXED_WEATHERED_COPPER_BULB = register(Blocks.WAXED_WEATHERED_COPPER_BULB);
      WAXED_OXIDIZED_COPPER_BULB = register(Blocks.WAXED_OXIDIZED_COPPER_BULB);
      TRIAL_SPAWNER = register(Blocks.TRIAL_SPAWNER);
      TRIAL_KEY = register("trial_key");
      OMINOUS_TRIAL_KEY = register("ominous_trial_key");
      VAULT = register(Blocks.VAULT);
      OMINOUS_BOTTLE = register("ominous_bottle", (new Item.Settings()).rarity(Rarity.UNCOMMON).component(DataComponentTypes.CONSUMABLE, ConsumableComponents.OMINOUS_BOTTLE).component(DataComponentTypes.OMINOUS_BOTTLE_AMPLIFIER, new OminousBottleAmplifierComponent(0)));
   }
}
