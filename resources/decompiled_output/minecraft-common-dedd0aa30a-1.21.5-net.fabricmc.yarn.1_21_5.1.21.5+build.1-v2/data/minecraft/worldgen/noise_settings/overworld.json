{"aquifers_enabled": true, "default_block": {"Name": "minecraft:stone"}, "default_fluid": {"Name": "minecraft:water", "Properties": {"level": "0"}}, "disable_mob_generation": false, "legacy_random_source": false, "noise": {"height": 384, "min_y": -64, "size_horizontal": 1, "size_vertical": 2}, "noise_router": {"barrier": {"type": "minecraft:noise", "noise": "minecraft:aquifer_barrier", "xz_scale": 1.0, "y_scale": 0.5}, "continents": "minecraft:overworld/continents", "depth": "minecraft:overworld/depth", "erosion": "minecraft:overworld/erosion", "final_density": {"type": "minecraft:min", "argument1": {"type": "minecraft:squeeze", "argument": {"type": "minecraft:mul", "argument1": 0.64, "argument2": {"type": "minecraft:interpolated", "argument": {"type": "minecraft:blend_density", "argument": {"type": "minecraft:add", "argument1": 0.1171875, "argument2": {"type": "minecraft:mul", "argument1": {"type": "minecraft:y_clamped_gradient", "from_value": 0.0, "from_y": -64, "to_value": 1.0, "to_y": -40}, "argument2": {"type": "minecraft:add", "argument1": -0.1171875, "argument2": {"type": "minecraft:add", "argument1": -0.078125, "argument2": {"type": "minecraft:mul", "argument1": {"type": "minecraft:y_clamped_gradient", "from_value": 1.0, "from_y": 240, "to_value": 0.0, "to_y": 256}, "argument2": {"type": "minecraft:add", "argument1": 0.078125, "argument2": {"type": "minecraft:range_choice", "input": "minecraft:overworld/sloped_cheese", "max_exclusive": 1.5625, "min_inclusive": -1000000.0, "when_in_range": {"type": "minecraft:min", "argument1": "minecraft:overworld/sloped_cheese", "argument2": {"type": "minecraft:mul", "argument1": 5.0, "argument2": "minecraft:overworld/caves/entrances"}}, "when_out_of_range": {"type": "minecraft:max", "argument1": {"type": "minecraft:min", "argument1": {"type": "minecraft:min", "argument1": {"type": "minecraft:add", "argument1": {"type": "minecraft:mul", "argument1": 4.0, "argument2": {"type": "minecraft:square", "argument": {"type": "minecraft:noise", "noise": "minecraft:cave_layer", "xz_scale": 1.0, "y_scale": 8.0}}}, "argument2": {"type": "minecraft:add", "argument1": {"type": "minecraft:clamp", "input": {"type": "minecraft:add", "argument1": 0.27, "argument2": {"type": "minecraft:noise", "noise": "minecraft:cave_cheese", "xz_scale": 1.0, "y_scale": 0.6666666666666666}}, "max": 1.0, "min": -1.0}, "argument2": {"type": "minecraft:clamp", "input": {"type": "minecraft:add", "argument1": 1.5, "argument2": {"type": "minecraft:mul", "argument1": -0.64, "argument2": "minecraft:overworld/sloped_cheese"}}, "max": 0.5, "min": 0.0}}}, "argument2": "minecraft:overworld/caves/entrances"}, "argument2": {"type": "minecraft:add", "argument1": "minecraft:overworld/caves/spaghetti_2d", "argument2": "minecraft:overworld/caves/spaghetti_roughness_function"}}, "argument2": {"type": "minecraft:range_choice", "input": "minecraft:overworld/caves/pillars", "max_exclusive": 0.03, "min_inclusive": -1000000.0, "when_in_range": -1000000.0, "when_out_of_range": "minecraft:overworld/caves/pillars"}}}}}}}}}}}}}, "argument2": "minecraft:overworld/caves/noodle"}, "fluid_level_floodedness": {"type": "minecraft:noise", "noise": "minecraft:aquifer_fluid_level_floodedness", "xz_scale": 1.0, "y_scale": 0.67}, "fluid_level_spread": {"type": "minecraft:noise", "noise": "minecraft:aquifer_fluid_level_spread", "xz_scale": 1.0, "y_scale": 0.7142857142857143}, "initial_density_without_jaggedness": {"type": "minecraft:add", "argument1": 0.1171875, "argument2": {"type": "minecraft:mul", "argument1": {"type": "minecraft:y_clamped_gradient", "from_value": 0.0, "from_y": -64, "to_value": 1.0, "to_y": -40}, "argument2": {"type": "minecraft:add", "argument1": -0.1171875, "argument2": {"type": "minecraft:add", "argument1": -0.078125, "argument2": {"type": "minecraft:mul", "argument1": {"type": "minecraft:y_clamped_gradient", "from_value": 1.0, "from_y": 240, "to_value": 0.0, "to_y": 256}, "argument2": {"type": "minecraft:add", "argument1": 0.078125, "argument2": {"type": "minecraft:clamp", "input": {"type": "minecraft:add", "argument1": -0.703125, "argument2": {"type": "minecraft:mul", "argument1": 4.0, "argument2": {"type": "minecraft:quarter_negative", "argument": {"type": "minecraft:mul", "argument1": "minecraft:overworld/depth", "argument2": {"type": "minecraft:cache_2d", "argument": "minecraft:overworld/factor"}}}}}, "max": 64.0, "min": -64.0}}}}}}}, "lava": {"type": "minecraft:noise", "noise": "minecraft:aquifer_lava", "xz_scale": 1.0, "y_scale": 1.0}, "ridges": "minecraft:overworld/ridges", "temperature": {"type": "minecraft:shifted_noise", "noise": "minecraft:temperature", "shift_x": "minecraft:shift_x", "shift_y": 0.0, "shift_z": "minecraft:shift_z", "xz_scale": 0.25, "y_scale": 0.0}, "vegetation": {"type": "minecraft:shifted_noise", "noise": "minecraft:vegetation", "shift_x": "minecraft:shift_x", "shift_y": 0.0, "shift_z": "minecraft:shift_z", "xz_scale": 0.25, "y_scale": 0.0}, "vein_gap": {"type": "minecraft:noise", "noise": "minecraft:ore_gap", "xz_scale": 1.0, "y_scale": 1.0}, "vein_ridged": {"type": "minecraft:add", "argument1": -0.07999999821186066, "argument2": {"type": "minecraft:max", "argument1": {"type": "minecraft:abs", "argument": {"type": "minecraft:interpolated", "argument": {"type": "minecraft:range_choice", "input": "minecraft:y", "max_exclusive": 51.0, "min_inclusive": -60.0, "when_in_range": {"type": "minecraft:noise", "noise": "minecraft:ore_vein_a", "xz_scale": 4.0, "y_scale": 4.0}, "when_out_of_range": 0.0}}}, "argument2": {"type": "minecraft:abs", "argument": {"type": "minecraft:interpolated", "argument": {"type": "minecraft:range_choice", "input": "minecraft:y", "max_exclusive": 51.0, "min_inclusive": -60.0, "when_in_range": {"type": "minecraft:noise", "noise": "minecraft:ore_vein_b", "xz_scale": 4.0, "y_scale": 4.0}, "when_out_of_range": 0.0}}}}}, "vein_toggle": {"type": "minecraft:interpolated", "argument": {"type": "minecraft:range_choice", "input": "minecraft:y", "max_exclusive": 51.0, "min_inclusive": -60.0, "when_in_range": {"type": "minecraft:noise", "noise": "minecraft:ore_veininess", "xz_scale": 1.5, "y_scale": 1.5}, "when_out_of_range": 0.0}}}, "ore_veins_enabled": true, "sea_level": 63, "spawn_target": [{"continentalness": [-0.11, 1.0], "depth": 0.0, "erosion": [-1.0, 1.0], "humidity": [-1.0, 1.0], "offset": 0.0, "temperature": [-1.0, 1.0], "weirdness": [-1.0, -0.16]}, {"continentalness": [-0.11, 1.0], "depth": 0.0, "erosion": [-1.0, 1.0], "humidity": [-1.0, 1.0], "offset": 0.0, "temperature": [-1.0, 1.0], "weirdness": [0.16, 1.0]}], "surface_rule": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:vertical_gradient", "false_at_and_above": {"above_bottom": 5}, "random_name": "minecraft:bedrock_floor", "true_at_and_below": {"above_bottom": 0}}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:bedrock"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:above_preliminary_surface"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "floor"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:wooded_badlands"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:y_above", "add_stone_depth": false, "anchor": {"absolute": 97}, "surface_depth_multiplier": 2}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": -0.5454, "min_threshold": -0.909, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:coarse_dirt"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.1818, "min_threshold": -0.1818, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:coarse_dirt"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.909, "min_threshold": 0.5454, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:coarse_dirt"}}}, {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:grass_block", "Properties": {"snowy": "false"}}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:dirt"}}]}]}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:swamp"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:y_above", "add_stone_depth": false, "anchor": {"absolute": 62}, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:not", "invert": {"type": "minecraft:y_above", "add_stone_depth": false, "anchor": {"absolute": 63}, "surface_depth_multiplier": 0}}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.0, "noise": "minecraft:surface_swamp"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:water", "Properties": {"level": "0"}}}}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:mangrove_swamp"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:y_above", "add_stone_depth": false, "anchor": {"absolute": 60}, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:not", "invert": {"type": "minecraft:y_above", "add_stone_depth": false, "anchor": {"absolute": 63}, "surface_depth_multiplier": 0}}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.0, "noise": "minecraft:surface_swamp"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:water", "Properties": {"level": "0"}}}}}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:badlands", "minecraft:eroded_badlands", "minecraft:wooded_badlands"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "floor"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:y_above", "add_stone_depth": false, "anchor": {"absolute": 256}, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:orange_terracotta"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:y_above", "add_stone_depth": true, "anchor": {"absolute": 74}, "surface_depth_multiplier": 1}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": -0.5454, "min_threshold": -0.909, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:terracotta"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.1818, "min_threshold": -0.1818, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:terracotta"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.909, "min_threshold": 0.5454, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:terracotta"}}}, {"type": "minecraft:bandlands"}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": -1, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:red_sandstone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:red_sand"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:not", "invert": {"type": "minecraft:hole"}}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:orange_terracotta"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": true, "offset": -6, "surface_depth_multiplier": -1}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:white_terracotta"}}}, {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:gravel"}}]}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:y_above", "add_stone_depth": true, "anchor": {"absolute": 63}, "surface_depth_multiplier": -1}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:y_above", "add_stone_depth": false, "anchor": {"absolute": 63}, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:not", "invert": {"type": "minecraft:y_above", "add_stone_depth": true, "anchor": {"absolute": 74}, "surface_depth_multiplier": 1}}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:orange_terracotta"}}}}, {"type": "minecraft:bandlands"}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": true, "offset": 0, "secondary_depth_range": 0, "surface_type": "floor"}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": true, "offset": -6, "surface_depth_multiplier": -1}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:white_terracotta"}}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "floor"}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": -1, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:frozen_ocean", "minecraft:deep_frozen_ocean"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:hole"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:air"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:temperature"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:ice"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:water", "Properties": {"level": "0"}}}]}}}, {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:frozen_peaks"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:steep"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:packed_ice"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.2, "min_threshold": 0.0, "noise": "minecraft:packed_ice"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:packed_ice"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.025, "min_threshold": 0.0, "noise": "minecraft:ice"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:ice"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:snow_block"}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:snowy_slopes"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:steep"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.6, "min_threshold": 0.35, "noise": "minecraft:powder_snow"}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:powder_snow"}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:snow_block"}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:jagged_peaks"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:steep"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:snow_block"}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:grove"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.6, "min_threshold": 0.35, "noise": "minecraft:powder_snow"}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:powder_snow"}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:snow_block"}}}]}}, {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:stony_peaks"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.0125, "min_threshold": -0.0125, "noise": "minecraft:calcite"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:calcite"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:stony_shore"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.05, "min_threshold": -0.05, "noise": "minecraft:gravel"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:gravel"}}]}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:windswept_hills"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.12121212121212122, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:warm_ocean", "minecraft:beach", "minecraft:snowy_beach"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:sandstone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:sand"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:desert"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:sandstone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:sand"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:dripstone_caves"]}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}]}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:windswept_savanna"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.21212121212121213, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": -0.06060606060606061, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:coarse_dirt"}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:windswept_gravelly_hills"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.24242424242424243, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:gravel"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.12121212121212122, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": -0.12121212121212122, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:grass_block", "Properties": {"snowy": "false"}}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:dirt"}}]}}, {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:gravel"}}]}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:old_growth_pine_taiga", "minecraft:old_growth_spruce_taiga"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.21212121212121213, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:coarse_dirt"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": -0.11515151515151514, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:podzol", "Properties": {"snowy": "false"}}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:ice_spikes"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:snow_block"}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:mangrove_swamp"]}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:mud"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:mushroom_fields"]}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:mycelium", "Properties": {"snowy": "false"}}}}, {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:grass_block", "Properties": {"snowy": "false"}}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:dirt"}}]}]}]}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": true, "offset": -6, "surface_depth_multiplier": -1}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "floor"}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:frozen_ocean", "minecraft:deep_frozen_ocean"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:hole"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:water", "Properties": {"level": "0"}}}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": true, "offset": 0, "secondary_depth_range": 0, "surface_type": "floor"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:frozen_peaks"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:steep"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:packed_ice"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.2, "min_threshold": -0.5, "noise": "minecraft:packed_ice"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:packed_ice"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.025, "min_threshold": -0.0625, "noise": "minecraft:ice"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:ice"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:snow_block"}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:snowy_slopes"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:steep"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.58, "min_threshold": 0.45, "noise": "minecraft:powder_snow"}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:powder_snow"}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:snow_block"}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:jagged_peaks"]}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:grove"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.58, "min_threshold": 0.45, "noise": "minecraft:powder_snow"}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:water", "add_stone_depth": false, "offset": 0, "surface_depth_multiplier": 0}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:powder_snow"}}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:dirt"}}]}}, {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:stony_peaks"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.0125, "min_threshold": -0.0125, "noise": "minecraft:calcite"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:calcite"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:stony_shore"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 0.05, "min_threshold": -0.05, "noise": "minecraft:gravel"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:gravel"}}]}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:windswept_hills"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.12121212121212122, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:warm_ocean", "minecraft:beach", "minecraft:snowy_beach"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:sandstone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:sand"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:desert"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:sandstone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:sand"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:dripstone_caves"]}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}]}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:windswept_savanna"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.21212121212121213, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:windswept_gravelly_hills"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.24242424242424243, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:gravel"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": 0.12121212121212122, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:noise_threshold", "max_threshold": 1.7976931348623157e+308, "min_threshold": -0.12121212121212122, "noise": "minecraft:surface"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:dirt"}}}, {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:gravel"}}]}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:mangrove_swamp"]}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:mud"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:dirt"}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:warm_ocean", "minecraft:beach", "minecraft:snowy_beach"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": true, "offset": 0, "secondary_depth_range": 6, "surface_type": "floor"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:sandstone"}}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:desert"]}, "then_run": {"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": true, "offset": 0, "secondary_depth_range": 30, "surface_type": "floor"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:sandstone"}}}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "floor"}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:frozen_peaks", "minecraft:jagged_peaks"]}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:biome", "biome_is": ["minecraft:warm_ocean", "minecraft:lukewarm_ocean", "minecraft:deep_lukewarm_ocean"]}, "then_run": {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:sandstone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:sand"}}]}}, {"type": "minecraft:sequence", "sequence": [{"type": "minecraft:condition", "if_true": {"type": "minecraft:stone_depth", "add_surface_depth": false, "offset": 0, "secondary_depth_range": 0, "surface_type": "ceiling"}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:stone"}}}, {"type": "minecraft:block", "result_state": {"Name": "minecraft:gravel"}}]}]}}]}}, {"type": "minecraft:condition", "if_true": {"type": "minecraft:vertical_gradient", "false_at_and_above": {"absolute": 8}, "random_name": "minecraft:deepslate", "true_at_and_below": {"absolute": 0}}, "then_run": {"type": "minecraft:block", "result_state": {"Name": "minecraft:deepslate", "Properties": {"axis": "y"}}}}]}}